flowchart TB
    subgraph External["🌐 External Data Sources"]
        ETH[Ethereum Testnet]
        INFURA[Infura API]
    end
    
    subgraph Ingestion["📥 Data Ingestion Layer"]
        ZK[Zookeeper]
        KAFKA[Kafka Broker]
        KUI[Kafka UI<br/>:8080]
    end
    
    subgraph CEP["⚡ Complex Event Processing"]
        FLINK[Flink SQL CEP]
        FILTER[Event Filter<br/>85% Reduction]
        FUI[Flink Dashboard<br/>:8081]
    end
    
    subgraph Graph["📊 Graph Construction"]
        SPARK[Spark Streaming]
        WINDOW[5-min Windows<br/>&lt;500ms latency]
        SUI[Spark UI<br/>:4040]
    end
    
    subgraph ML["🧠 Machine Learning Layer"]
        TGAT[TGAT Model<br/>Temporal GNN]
        GPU[GPU Inference<br/>&lt;1s response]
        EXPLAINER[GNN Explainer<br/>&gt;90% interpretability]
    end
    
    subgraph Monitoring["📈 Visualization & Monitoring"]
        DASHBOARD[Analytics Dashboard<br/>:3000]
        ALERTS[Real-time Alerts]
    end
    
    subgraph Storage["💾 Data Storage"]
        ELLIPTIC[(Elliptic Dataset<br/>Training Data)]
        MODELS[(Model Storage)]
    end
    
    ETH --> INFURA
    INFURA -->|Real-time Tx Data| KAFKA
    ZK -.->|Coordination| KAFKA
    KAFKA -->|Stream Events| FLINK
    FLINK -->|Filtered Events| FILTER
    FILTER -->|Suspicious Patterns| SPARK
    SPARK -->|Graph Snapshots| WINDOW
    WINDOW -->|Temporal Graphs| TGAT
    TGAT -->|Anomaly Scores| GPU
    GPU -->|Predictions| EXPLAINER
    EXPLAINER -->|Explanations| DASHBOARD
    DASHBOARD -->|Alerts| ALERTS
    
    ELLIPTIC -.->|Training| TGAT
    TGAT -.->|Save Model| MODELS
    MODELS -.->|Load Model| GPU
    
    KAFKA -.->|Monitor| KUI
    FLINK -.->|Monitor| FUI
    SPARK -.->|Monitor| SUI
    
    classDef external fill:#e1f5fe,stroke:#01579b,stroke-width:2px
    classDef ingestion fill:#f3e5f5,stroke:#4a148c,stroke-width:2px
    classDef processing fill:#e8f5e8,stroke:#1b5e20,stroke-width:2px
    classDef ml fill:#fff3e0,stroke:#e65100,stroke-width:2px
    classDef monitoring fill:#fce4ec,stroke:#880e4f,stroke-width:2px
    classDef storage fill:#f1f8e9,stroke:#33691e,stroke-width:2px
    
    class ETH,INFURA external
    class ZK,KAFKA,KUI ingestion
    class FLINK,FILTER,FUI,SPARK,WINDOW,SUI processing
    class TGAT,GPU,EXPLAINER ml
    class DASHBOARD,ALERTS monitoring
    class ELLIPTIC,MODELS storage