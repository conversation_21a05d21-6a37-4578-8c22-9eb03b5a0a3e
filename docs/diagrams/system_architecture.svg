<svg aria-roledescription="flowchart-v2" role="graphics-document document" viewBox="0 0 1098.33203125 2033.5462646484375" style="max-width: 1098.33px; background-color: white;" class="flowchart" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns="http://www.w3.org/2000/svg" width="100%" id="my-svg"><style>#my-svg{font-family:"trebuchet ms",verdana,arial,sans-serif;font-size:16px;fill:#333;}@keyframes edge-animation-frame{from{stroke-dashoffset:0;}}@keyframes dash{to{stroke-dashoffset:0;}}#my-svg .edge-animation-slow{stroke-dasharray:9,5!important;stroke-dashoffset:900;animation:dash 50s linear infinite;stroke-linecap:round;}#my-svg .edge-animation-fast{stroke-dasharray:9,5!important;stroke-dashoffset:900;animation:dash 20s linear infinite;stroke-linecap:round;}#my-svg .error-icon{fill:#552222;}#my-svg .error-text{fill:#552222;stroke:#552222;}#my-svg .edge-thickness-normal{stroke-width:1px;}#my-svg .edge-thickness-thick{stroke-width:3.5px;}#my-svg .edge-pattern-solid{stroke-dasharray:0;}#my-svg .edge-thickness-invisible{stroke-width:0;fill:none;}#my-svg .edge-pattern-dashed{stroke-dasharray:3;}#my-svg .edge-pattern-dotted{stroke-dasharray:2;}#my-svg .marker{fill:#333333;stroke:#333333;}#my-svg .marker.cross{stroke:#333333;}#my-svg svg{font-family:"trebuchet ms",verdana,arial,sans-serif;font-size:16px;}#my-svg p{margin:0;}#my-svg .label{font-family:"trebuchet ms",verdana,arial,sans-serif;color:#333;}#my-svg .cluster-label text{fill:#333;}#my-svg .cluster-label span{color:#333;}#my-svg .cluster-label span p{background-color:transparent;}#my-svg .label text,#my-svg span{fill:#333;color:#333;}#my-svg .node rect,#my-svg .node circle,#my-svg .node ellipse,#my-svg .node polygon,#my-svg .node path{fill:#ECECFF;stroke:#9370DB;stroke-width:1px;}#my-svg .rough-node .label text,#my-svg .node .label text,#my-svg .image-shape .label,#my-svg .icon-shape .label{text-anchor:middle;}#my-svg .node .katex path{fill:#000;stroke:#000;stroke-width:1px;}#my-svg .rough-node .label,#my-svg .node .label,#my-svg .image-shape .label,#my-svg .icon-shape .label{text-align:center;}#my-svg .node.clickable{cursor:pointer;}#my-svg .root .anchor path{fill:#333333!important;stroke-width:0;stroke:#333333;}#my-svg .arrowheadPath{fill:#333333;}#my-svg .edgePath .path{stroke:#333333;stroke-width:2.0px;}#my-svg .flowchart-link{stroke:#333333;fill:none;}#my-svg .edgeLabel{background-color:rgba(232,232,232, 0.8);text-align:center;}#my-svg .edgeLabel p{background-color:rgba(232,232,232, 0.8);}#my-svg .edgeLabel rect{opacity:0.5;background-color:rgba(232,232,232, 0.8);fill:rgba(232,232,232, 0.8);}#my-svg .labelBkg{background-color:rgba(232, 232, 232, 0.5);}#my-svg .cluster rect{fill:#ffffde;stroke:#aaaa33;stroke-width:1px;}#my-svg .cluster text{fill:#333;}#my-svg .cluster span{color:#333;}#my-svg div.mermaidTooltip{position:absolute;text-align:center;max-width:200px;padding:2px;font-family:"trebuchet ms",verdana,arial,sans-serif;font-size:12px;background:hsl(80, 100%, 96.2745098039%);border:1px solid #aaaa33;border-radius:2px;pointer-events:none;z-index:100;}#my-svg .flowchartTitleText{text-anchor:middle;font-size:18px;fill:#333;}#my-svg rect.text{fill:none;stroke-width:0;}#my-svg .icon-shape,#my-svg .image-shape{background-color:rgba(232,232,232, 0.8);text-align:center;}#my-svg .icon-shape p,#my-svg .image-shape p{background-color:rgba(232,232,232, 0.8);padding:2px;}#my-svg .icon-shape rect,#my-svg .image-shape rect{opacity:0.5;background-color:rgba(232,232,232, 0.8);fill:rgba(232,232,232, 0.8);}#my-svg :root{--mermaid-font-family:"trebuchet ms",verdana,arial,sans-serif;}#my-svg .external&gt;*{fill:#e1f5fe!important;stroke:#01579b!important;stroke-width:2px!important;}#my-svg .external span{fill:#e1f5fe!important;stroke:#01579b!important;stroke-width:2px!important;}#my-svg .ingestion&gt;*{fill:#f3e5f5!important;stroke:#4a148c!important;stroke-width:2px!important;}#my-svg .ingestion span{fill:#f3e5f5!important;stroke:#4a148c!important;stroke-width:2px!important;}#my-svg .processing&gt;*{fill:#e8f5e8!important;stroke:#1b5e20!important;stroke-width:2px!important;}#my-svg .processing span{fill:#e8f5e8!important;stroke:#1b5e20!important;stroke-width:2px!important;}#my-svg .ml&gt;*{fill:#fff3e0!important;stroke:#e65100!important;stroke-width:2px!important;}#my-svg .ml span{fill:#fff3e0!important;stroke:#e65100!important;stroke-width:2px!important;}#my-svg .monitoring&gt;*{fill:#fce4ec!important;stroke:#880e4f!important;stroke-width:2px!important;}#my-svg .monitoring span{fill:#fce4ec!important;stroke:#880e4f!important;stroke-width:2px!important;}#my-svg .storage&gt;*{fill:#f1f8e9!important;stroke:#33691e!important;stroke-width:2px!important;}#my-svg .storage span{fill:#f1f8e9!important;stroke:#33691e!important;stroke-width:2px!important;}</style><g><marker orient="auto" markerHeight="8" markerWidth="8" markerUnits="userSpaceOnUse" refY="5" refX="5" viewBox="0 0 10 10" class="marker flowchart-v2" id="my-svg_flowchart-v2-pointEnd"><path style="stroke-width: 1; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 0 0 L 10 5 L 0 10 z"/></marker><marker orient="auto" markerHeight="8" markerWidth="8" markerUnits="userSpaceOnUse" refY="5" refX="4.5" viewBox="0 0 10 10" class="marker flowchart-v2" id="my-svg_flowchart-v2-pointStart"><path style="stroke-width: 1; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 0 5 L 10 10 L 10 0 z"/></marker><marker orient="auto" markerHeight="11" markerWidth="11" markerUnits="userSpaceOnUse" refY="5" refX="11" viewBox="0 0 10 10" class="marker flowchart-v2" id="my-svg_flowchart-v2-circleEnd"><circle style="stroke-width: 1; stroke-dasharray: 1, 0;" class="arrowMarkerPath" r="5" cy="5" cx="5"/></marker><marker orient="auto" markerHeight="11" markerWidth="11" markerUnits="userSpaceOnUse" refY="5" refX="-1" viewBox="0 0 10 10" class="marker flowchart-v2" id="my-svg_flowchart-v2-circleStart"><circle style="stroke-width: 1; stroke-dasharray: 1, 0;" class="arrowMarkerPath" r="5" cy="5" cx="5"/></marker><marker orient="auto" markerHeight="11" markerWidth="11" markerUnits="userSpaceOnUse" refY="5.2" refX="12" viewBox="0 0 11 11" class="marker cross flowchart-v2" id="my-svg_flowchart-v2-crossEnd"><path style="stroke-width: 2; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 1,1 l 9,9 M 10,1 l -9,9"/></marker><marker orient="auto" markerHeight="11" markerWidth="11" markerUnits="userSpaceOnUse" refY="5.2" refX="-1" viewBox="0 0 11 11" class="marker cross flowchart-v2" id="my-svg_flowchart-v2-crossStart"><path style="stroke-width: 2; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 1,1 l 9,9 M 10,1 l -9,9"/></marker><g class="root"><g class="clusters"><g data-look="classic" id="Storage" class="cluster"><rect height="487.54622650146484" width="285" y="927" x="750.65234375" style=""/><g transform="translate(847.25390625, 927)" class="cluster-label"><foreignObject height="24" width="91.796875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Data Storage</p></span></div></foreignObject></g></g><g data-look="classic" id="Monitoring" class="cluster"><rect height="232" width="318.0625" y="1793.5462265014648" x="300.01171875" style=""/><g transform="translate(365.30078125, 1793.5462265014648)" class="cluster-label"><foreignObject height="24" width="187.484375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Visualization &amp; Monitoring</p></span></div></foreignObject></g></g><g data-look="classic" id="ML" class="cluster"><rect height="558.1197509765625" width="613.7578125" y="1161.4264755249023" x="10.28515625" style=""/><g transform="translate(232.6796875, 1161.4264755249023)" class="cluster-label"><foreignObject height="24" width="168.96875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Machine Learning Layer</p></span></div></foreignObject></g></g><g data-look="classic" id="Graph" class="cluster"><rect height="313.42647552490234" width="546.44921875" y="774" x="8" style=""/><g transform="translate(211.841796875, 774)" class="cluster-label"><foreignObject height="24" width="138.765625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Graph Construction</p></span></div></foreignObject></g></g><g data-look="classic" id="CEP" class="cluster"><rect height="257" width="589.28125" y="443" x="143.1875" style=""/><g transform="translate(345.03125, 443)" class="cluster-label"><foreignObject height="24" width="185.59375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Complex Event Processing</p></span></div></foreignObject></g></g><g data-look="classic" id="Ingestion" class="cluster"><rect height="410" width="337.86328125" y="137" x="752.46875" style=""/><g transform="translate(848.087890625, 137)" class="cluster-label"><foreignObject height="24" width="146.625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Data Ingestion Layer</p></span></div></foreignObject></g></g><g data-look="classic" id="External" class="cluster"><rect height="233" width="256.546875" y="8" x="307.36328125" style=""/><g transform="translate(357.27734375, 8)" class="cluster-label"><foreignObject height="24" width="156.71875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>External Data Sources</p></span></div></foreignObject></g></g></g><g class="edgePaths"><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_ETH_INFURA_0" d="M435.637,87L435.637,91.167C435.637,95.333,435.637,103.667,435.637,112C435.637,120.333,435.637,128.667,435.637,136.333C435.637,144,435.637,151,435.637,154.5L435.637,158"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_INFURA_KAFKA_0" d="M435.637,216L435.637,220.167C435.637,224.333,435.637,232.667,504.719,243C573.801,253.333,711.965,265.667,786.853,277.533C861.74,289.399,873.352,300.799,879.158,306.498L884.963,312.198"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-dotted edge-thickness-normal edge-pattern-solid flowchart-link" id="L_ZK_KAFKA_0" d="M980.512,216L980.512,220.167C980.512,224.333,980.512,232.667,980.512,243C980.512,253.333,980.512,265.667,974.706,277.533C968.9,289.399,957.289,300.799,951.483,306.498L945.677,312.198"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_KAFKA_FLINK_0" d="M884.996,369L878.071,375.167C871.145,381.333,857.293,393.667,850.367,406C843.441,418.333,843.441,430.667,790.985,443.699C738.528,456.731,633.614,470.462,581.157,477.327L528.701,484.193"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_FLINK_FILTER_0" d="M372.228,522L360.824,526.167C349.42,530.333,326.612,538.667,315.209,549C303.805,559.333,303.805,571.667,303.805,583.333C303.805,595,303.805,606,303.805,611.5L303.805,617"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_FILTER_SPARK_0" d="M303.805,675L303.805,679.167C303.805,683.333,303.805,691.667,303.805,702C303.805,712.333,303.805,724.667,303.805,737C303.805,749.333,303.805,761.667,303.805,771.333C303.805,781,303.805,788,303.805,791.5L303.805,795"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_SPARK_WINDOW_0" d="M248.621,853L236.018,859.167C223.414,865.333,198.207,877.667,185.604,890C173,902.333,173,914.667,173,927.036C173,939.404,173,951.809,173,958.011L173,964.213"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_WINDOW_TGAT_0" d="M173,1046.213L173,1053.082C173,1059.951,173,1073.689,173,1086.724C173,1099.76,173,1112.093,173,1124.426C173,1136.76,173,1149.093,173,1158.76C173,1168.426,173,1175.426,173,1178.926L173,1182.426"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_TGAT_GPU_0" d="M155.14,1240.426L151.06,1246.593C146.981,1252.76,138.823,1265.093,134.743,1283.686C130.664,1302.28,130.664,1327.133,130.664,1349.986C130.664,1372.84,130.664,1393.693,130.664,1410.286C130.664,1426.88,130.664,1439.213,163.866,1451.85C197.068,1464.488,263.471,1477.43,296.673,1483.901L329.875,1490.372"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_GPU_EXPLAINER_0" d="M459.043,1542.546L459.043,1548.713C459.043,1554.88,459.043,1567.213,459.043,1578.88C459.043,1590.546,459.043,1601.546,459.043,1607.046L459.043,1612.546"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_EXPLAINER_DASHBOARD_0" d="M459.043,1694.546L459.043,1698.713C459.043,1702.88,459.043,1711.213,459.043,1721.546C459.043,1731.88,459.043,1744.213,459.043,1756.546C459.043,1768.88,459.043,1781.213,459.043,1790.88C459.043,1800.546,459.043,1807.546,459.043,1811.046L459.043,1814.546"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_DASHBOARD_ALERTS_0" d="M459.043,1872.546L459.043,1878.713C459.043,1884.88,459.043,1897.213,459.043,1908.88C459.043,1920.546,459.043,1931.546,459.043,1937.046L459.043,1942.546"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-dotted edge-thickness-normal edge-pattern-solid flowchart-link" id="L_ELLIPTIC_TGAT_0" d="M893.152,1062.426L893.152,1066.593C893.152,1070.76,893.152,1079.093,893.152,1089.426C893.152,1099.76,893.152,1112.093,893.152,1124.426C893.152,1136.76,893.152,1149.093,794.697,1162.369C696.241,1175.645,499.33,1189.863,400.875,1196.972L302.419,1204.082"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-dotted edge-thickness-normal edge-pattern-solid flowchart-link" id="L_TGAT_MODELS_0" d="M195.172,1240.426L200.235,1246.593C205.299,1252.76,215.427,1265.093,321.417,1282.532C427.408,1299.97,629.261,1322.514,730.188,1333.786L831.115,1345.058"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-dotted edge-thickness-normal edge-pattern-solid flowchart-link" id="L_MODELS_GPU_0" d="M893.152,1389.546L893.152,1393.713C893.152,1397.88,893.152,1406.213,829.504,1416.546C765.855,1426.88,638.559,1439.213,570.3,1451.03C502.042,1462.846,492.822,1474.147,488.212,1479.797L483.601,1485.447"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-dotted edge-thickness-normal edge-pattern-solid flowchart-link" id="L_KAFKA_KUI_0" d="M939.81,369L945.404,375.167C950.997,381.333,962.184,393.667,967.778,406C973.371,418.333,973.371,430.667,973.371,440.333C973.371,450,973.371,457,973.371,460.5L973.371,464"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-dotted edge-thickness-normal edge-pattern-solid flowchart-link" id="L_FLINK_FUI_0" d="M520.022,522L531.426,526.167C542.83,530.333,565.638,538.667,577.041,549C588.445,559.333,588.445,571.667,588.445,583.333C588.445,595,588.445,606,588.445,611.5L588.445,617"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-dotted edge-thickness-normal edge-pattern-solid flowchart-link" id="L_SPARK_SUI_0" d="M358.988,853L371.591,859.167C384.195,865.333,409.402,877.667,422.006,890C434.609,902.333,434.609,914.667,434.609,929.036C434.609,943.404,434.609,959.809,434.609,968.011L434.609,976.213"/></g><g class="edgeLabels"><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g transform="translate(850.12890625, 278)" class="edgeLabel"><g transform="translate(-64.46875, -12)" class="label"><foreignObject height="24" width="128.9375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>Real-time Tx Data</p></span></div></foreignObject></g></g><g transform="translate(980.51171875, 278)" class="edgeLabel"><g transform="translate(-45.9140625, -12)" class="label"><foreignObject height="24" width="91.828125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>Coordination</p></span></div></foreignObject></g></g><g transform="translate(843.44140625, 406)" class="edgeLabel"><g transform="translate(-51.09375, -12)" class="label"><foreignObject height="24" width="102.1875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>Stream Events</p></span></div></foreignObject></g></g><g transform="translate(303.8046875, 584)" class="edgeLabel"><g transform="translate(-54.0625, -12)" class="label"><foreignObject height="24" width="108.125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>Filtered Events</p></span></div></foreignObject></g></g><g transform="translate(303.8046875, 737)" class="edgeLabel"><g transform="translate(-68.46875, -12)" class="label"><foreignObject height="24" width="136.9375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>Suspicious Patterns</p></span></div></foreignObject></g></g><g transform="translate(173, 890)" class="edgeLabel"><g transform="translate(-59.15625, -12)" class="label"><foreignObject height="24" width="118.3125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>Graph Snapshots</p></span></div></foreignObject></g></g><g transform="translate(173, 1124.4264755249023)" class="edgeLabel"><g transform="translate(-60.2734375, -12)" class="label"><foreignObject height="24" width="120.546875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>Temporal Graphs</p></span></div></foreignObject></g></g><g transform="translate(130.6640625, 1351.9863510131836)" class="edgeLabel"><g transform="translate(-55.7578125, -12)" class="label"><foreignObject height="24" width="111.515625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>Anomaly Scores</p></span></div></foreignObject></g></g><g transform="translate(459.04296875, 1579.5462265014648)" class="edgeLabel"><g transform="translate(-39.6171875, -12)" class="label"><foreignObject height="24" width="79.234375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>Predictions</p></span></div></foreignObject></g></g><g transform="translate(459.04296875, 1756.5462265014648)" class="edgeLabel"><g transform="translate(-45.2421875, -12)" class="label"><foreignObject height="24" width="90.484375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>Explanations</p></span></div></foreignObject></g></g><g transform="translate(459.04296875, 1909.5462265014648)" class="edgeLabel"><g transform="translate(-20.9609375, -12)" class="label"><foreignObject height="24" width="41.921875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>Alerts</p></span></div></foreignObject></g></g><g transform="translate(893.15234375, 1124.4264755249023)" class="edgeLabel"><g transform="translate(-28.3984375, -12)" class="label"><foreignObject height="24" width="56.796875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>Training</p></span></div></foreignObject></g></g><g transform="translate(225.5546875, 1277.4264755249023)" class="edgeLabel"><g transform="translate(-39.890625, -12)" class="label"><foreignObject height="24" width="79.78125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>Save Model</p></span></div></foreignObject></g></g><g transform="translate(511.26171875, 1451.5462265014648)" class="edgeLabel"><g transform="translate(-40.5625, -12)" class="label"><foreignObject height="24" width="81.125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>Load Model</p></span></div></foreignObject></g></g><g transform="translate(973.37109375, 406)" class="edgeLabel"><g transform="translate(-27.1953125, -12)" class="label"><foreignObject height="24" width="54.390625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>Monitor</p></span></div></foreignObject></g></g><g transform="translate(588.4453125, 584)" class="edgeLabel"><g transform="translate(-27.1953125, -12)" class="label"><foreignObject height="24" width="54.390625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>Monitor</p></span></div></foreignObject></g></g><g transform="translate(434.609375, 890)" class="edgeLabel"><g transform="translate(-27.1953125, -12)" class="label"><foreignObject height="24" width="54.390625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>Monitor</p></span></div></foreignObject></g></g></g><g class="nodes"><g transform="translate(435.63671875, 60)" id="flowchart-ETH-0" class="node default external"><rect height="54" width="186.546875" y="-27" x="-93.2734375" style="fill:#e1f5fe !important;stroke:#01579b !important;stroke-width:2px !important" class="basic label-container"/><g transform="translate(-63.2734375, -12)" style="" class="label"><rect/><foreignObject height="24" width="126.546875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Ethereum Testnet</p></span></div></foreignObject></g></g><g transform="translate(435.63671875, 189)" id="flowchart-INFURA-1" class="node default external"><rect height="54" width="129.234375" y="-27" x="-64.6171875" style="fill:#e1f5fe !important;stroke:#01579b !important;stroke-width:2px !important" class="basic label-container"/><g transform="translate(-34.6171875, -12)" style="" class="label"><rect/><foreignObject height="24" width="69.234375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Infura API</p></span></div></foreignObject></g></g><g transform="translate(980.51171875, 189)" id="flowchart-ZK-2" class="node default ingestion"><rect height="54" width="135.359375" y="-27" x="-67.6796875" style="fill:#f3e5f5 !important;stroke:#4a148c !important;stroke-width:2px !important" class="basic label-container"/><g transform="translate(-37.6796875, -12)" style="" class="label"><rect/><foreignObject height="24" width="75.359375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Zookeeper</p></span></div></foreignObject></g></g><g transform="translate(915.3203125, 342)" id="flowchart-KAFKA-3" class="node default ingestion"><rect height="54" width="151.703125" y="-27" x="-75.8515625" style="fill:#f3e5f5 !important;stroke:#4a148c !important;stroke-width:2px !important" class="basic label-container"/><g transform="translate(-45.8515625, -12)" style="" class="label"><rect/><foreignObject height="24" width="91.703125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Kafka Broker</p></span></div></foreignObject></g></g><g transform="translate(973.37109375, 495)" id="flowchart-KUI-4" class="node default ingestion"><rect height="54" width="163.921875" y="-27" x="-81.9609375" style="fill:#f3e5f5 !important;stroke:#4a148c !important;stroke-width:2px !important" class="basic label-container"/><g transform="translate(-51.9609375, -12)" style="" class="label"><rect/><foreignObject height="24" width="103.921875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Kafka UI :8080</p></span></div></foreignObject></g></g><g transform="translate(446.125, 495)" id="flowchart-FLINK-5" class="node default processing"><rect height="54" width="157.21875" y="-27" x="-78.609375" style="fill:#e8f5e8 !important;stroke:#1b5e20 !important;stroke-width:2px !important" class="basic label-container"/><g transform="translate(-48.609375, -12)" style="" class="label"><rect/><foreignObject height="24" width="97.21875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Flink SQL CEP</p></span></div></foreignObject></g></g><g transform="translate(303.8046875, 648)" id="flowchart-FILTER-6" class="node default processing"><rect height="54" width="251.234375" y="-27" x="-125.6171875" style="fill:#e8f5e8 !important;stroke:#1b5e20 !important;stroke-width:2px !important" class="basic label-container"/><g transform="translate(-95.6171875, -12)" style="" class="label"><rect/><foreignObject height="24" width="191.234375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Event Filter 85% Reduction</p></span></div></foreignObject></g></g><g transform="translate(588.4453125, 648)" id="flowchart-FUI-7" class="node default processing"><rect height="54" width="218.046875" y="-27" x="-109.0234375" style="fill:#e8f5e8 !important;stroke:#1b5e20 !important;stroke-width:2px !important" class="basic label-container"/><g transform="translate(-79.0234375, -12)" style="" class="label"><rect/><foreignObject height="24" width="158.046875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Flink Dashboard :8081</p></span></div></foreignObject></g></g><g transform="translate(303.8046875, 826)" id="flowchart-SPARK-8" class="node default processing"><rect height="54" width="176.140625" y="-27" x="-88.0703125" style="fill:#e8f5e8 !important;stroke:#1b5e20 !important;stroke-width:2px !important" class="basic label-container"/><g transform="translate(-58.0703125, -12)" style="" class="label"><rect/><foreignObject height="24" width="116.140625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Spark Streaming</p></span></div></foreignObject></g></g><g transform="translate(173, 1007.2132377624512)" id="flowchart-WINDOW-9" class="node default processing"><rect height="78" width="260" y="-39" x="-130" style="fill:#e8f5e8 !important;stroke:#1b5e20 !important;stroke-width:2px !important" class="basic label-container"/><g transform="translate(-100, -24)" style="" class="label"><rect/><foreignObject height="48" width="200"><div style="display: table; white-space: break-spaces; line-height: 1.5; max-width: 200px; text-align: center; width: 200px;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>5-min Windows 500ms latency</p></span></div></foreignObject></g></g><g transform="translate(434.609375, 1007.2132377624512)" id="flowchart-SUI-10" class="node default processing"><rect height="54" width="163.21875" y="-27" x="-81.609375" style="fill:#e8f5e8 !important;stroke:#1b5e20 !important;stroke-width:2px !important" class="basic label-container"/><g transform="translate(-51.609375, -12)" style="" class="label"><rect/><foreignObject height="24" width="103.21875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Spark UI :4040</p></span></div></foreignObject></g></g><g transform="translate(173, 1213.4264755249023)" id="flowchart-TGAT-11" class="node default ml"><rect height="54" width="250.859375" y="-27" x="-125.4296875" style="fill:#fff3e0 !important;stroke:#e65100 !important;stroke-width:2px !important" class="basic label-container"/><g transform="translate(-95.4296875, -12)" style="" class="label"><rect/><foreignObject height="24" width="190.859375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>TGAT Model Temporal GNN</p></span></div></foreignObject></g></g><g transform="translate(459.04296875, 1515.5462265014648)" id="flowchart-GPU-12" class="node default ml"><rect height="54" width="250.484375" y="-27" x="-125.2421875" style="fill:#fff3e0 !important;stroke:#e65100 !important;stroke-width:2px !important" class="basic label-container"/><g transform="translate(-95.2421875, -12)" style="" class="label"><rect/><foreignObject height="24" width="190.484375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>GPU Inference 1s response</p></span></div></foreignObject></g></g><g transform="translate(459.04296875, 1655.5462265014648)" id="flowchart-EXPLAINER-13" class="node default ml"><rect height="78" width="260" y="-39" x="-130" style="fill:#fff3e0 !important;stroke:#e65100 !important;stroke-width:2px !important" class="basic label-container"/><g transform="translate(-100, -24)" style="" class="label"><rect/><foreignObject height="48" width="200"><div style="display: table; white-space: break-spaces; line-height: 1.5; max-width: 200px; text-align: center; width: 200px;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>GNN Explainer 90% interpretability</p></span></div></foreignObject></g></g><g transform="translate(459.04296875, 1845.5462265014648)" id="flowchart-DASHBOARD-14" class="node default monitoring"><rect height="54" width="248.0625" y="-27" x="-124.03125" style="fill:#fce4ec !important;stroke:#880e4f !important;stroke-width:2px !important" class="basic label-container"/><g transform="translate(-94.03125, -12)" style="" class="label"><rect/><foreignObject height="24" width="188.0625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Analytics Dashboard :3000</p></span></div></foreignObject></g></g><g transform="translate(459.04296875, 1973.5462265014648)" id="flowchart-ALERTS-15" class="node default monitoring"><rect height="54" width="175.171875" y="-27" x="-87.5859375" style="fill:#fce4ec !important;stroke:#880e4f !important;stroke-width:2px !important" class="basic label-container"/><g transform="translate(-57.5859375, -12)" style="" class="label"><rect/><foreignObject height="24" width="115.171875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Real-time Alerts</p></span></div></foreignObject></g></g><g transform="translate(893.15234375, 1007.2132377624512)" id="flowchart-ELLIPTIC-16" class="node default storage"><path transform="translate(-107.5, -55.21323529411765)" style="fill:#f1f8e9 !important;stroke:#33691e !important;stroke-width:2px !important" class="basic label-container" d="M0,15.808823529411764 a107.5,15.808823529411764 0,0,0 215,0 a107.5,15.808823529411764 0,0,0 -215,0 l0,78.80882352941177 a107.5,15.808823529411764 0,0,0 215,0 l0,-78.80882352941177"/><g transform="translate(-100, -14)" style="" class="label"><rect/><foreignObject height="48" width="200"><div style="display: table; white-space: break-spaces; line-height: 1.5; max-width: 200px; text-align: center; width: 200px;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Elliptic Dataset Training Data</p></span></div></foreignObject></g></g><g transform="translate(893.15234375, 1351.9863510131836)" id="flowchart-MODELS-17" class="node default storage"><path transform="translate(-58.0625, -37.559875583203734)" style="fill:#f1f8e9 !important;stroke:#33691e !important;stroke-width:2px !important" class="basic label-container" d="M0,12.039917055469155 a58.0625,12.039917055469155 0,0,0 116.125,0 a58.0625,12.039917055469155 0,0,0 -116.125,0 l0,51.039917055469154 a58.0625,12.039917055469155 0,0,0 116.125,0 l0,-51.039917055469154"/><g transform="translate(-50.5625, -2)" style="" class="label"><rect/><foreignObject height="24" width="101.125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Model Storage</p></span></div></foreignObject></g></g></g></g></g></svg>