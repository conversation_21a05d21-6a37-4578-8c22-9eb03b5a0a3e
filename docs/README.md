# Real-Time On-Chain Anomaly Detection for Anti-Money Laundering

[![License: MIT](https://img.shields.io/badge/License-MIT-yellow.svg)](https://opensource.org/licenses/MIT)
[![Python 3.8+](https://img.shields.io/badge/python-3.8+-blue.svg)](https://www.python.org/downloads/)
[![Docker](https://img.shields.io/badge/docker-%230db7ed.svg?style=flat&logo=docker&logoColor=white)](https://www.docker.com/)
[![Kafka](https://img.shields.io/badge/Apache%20Kafka-000?style=flat&logo=apachekafka)](https://kafka.apache.org/)
[![Spark](https://img.shields.io/badge/Apache%20Spark-FDEE21?style=flat&logo=apachespark&logoColor=black)](https://spark.apache.org/)
[![Flink](https://img.shields.io/badge/Apache%20Flink-E6526F?style=flat&logo=Apache%20Flink&logoColor=white)](https://flink.apache.org/)

## 🎯 Overview

A cutting-edge **real-time streaming pipeline** for blockchain Anti-Money Laundering (AML) that combines **Complex Event Processing (CEP)** with **Spatio-Temporal Graph Neural Networks**. This system enables **sub-second detection** of suspicious transaction patterns in blockchain networks, specifically targeting Ethereum transactions with **<1 second end-to-end inference** and **>90% explainability**.

### 🏆 Key Innovation
**First end-to-end system** that combines CEP's sub-second filtering with spatio-temporal GNN inference on sliding-window graphs, addressing critical gaps in current blockchain AML solutions.

## 🔍 Problem Statement

Current blockchain AML solutions face four critical limitations:

1. **🌊 High Velocity Data Streams**: No existing framework supports real-time graph construction at scale for high-velocity blockchain streams
2. **🔧 Rigid CEP Rules**: Hand-crafted rules lack adaptability to evolving laundering topologies, leading to false negatives
3. **⏱️ Batch-Only GNNs**: Spatio-temporal GNNs confined to batch processing, preventing timely alerts on dynamic transaction networks
4. **🔍 Missing Explainability**: Absence of integrated, streaming-capable explainability inhibits investigator trust and compliance adoption

## 🎯 Research Objectives

### **Primary Aim**
Develop a real-time on-chain anomaly detection streaming pipeline employing Kafka-based event ingestion, Complex Event Processing pre-filtering, sliding-window graph construction in Spark, and TGAT inference with explainability.

### **Specific Objectives**

| Objective | Target Metric | Implementation |
|-----------|---------------|----------------|
| **CEP Filtering** | Prune >85% benign transactions | Kafka-Flink-SQL module for Sepolia events |
| **Graph Construction** | <500ms latency per snapshot | Spark Structured Streaming with 5-min sliding windows |
| **TGAT Inference** | <1 second end-to-end | Docker/GPU deployment with optimized serving |
| **Explainability** | >90% alert interpretability | GNNExplainer integration for node/edge attribution |
| **Performance** | >1000 tx/s throughput | Benchmark on Sepolia testnet |

## 🏗️ System Architecture

```mermaid
flowchart LR
    subgraph "Data Ingestion"
    A[Infura API] --> B[Kafka Broker]
        A1[Sepolia Testnet] --> B
  end
    
    subgraph "CEP Layer"
        B --> C[Flink SQL Engine]
        C --> D[Pattern Detection]
        D --> E[85% Benign Filtering]
  end
    
    subgraph "Graph Construction"
        E --> F[Spark Structured Streaming]
        F --> G[5-min Sliding Windows]
        G --> H[PyTorch Geometric Graphs]
  end
    
    subgraph "ML Inference"
        H --> I[TGAT Model]
        I --> J[Anomaly Scores]
        J --> K[GNNExplainer]
    end
    
    subgraph "Monitoring & Alerts"
        K --> L[Real-time Dashboard]
        K --> M[Investigator Interface]
        J --> N[Alert System]
    end
    
    style A fill:#e1f5fe
    style C fill:#f3e5f5
    style F fill:#e8f5e8
    style I fill:#fff3e0
    style L fill:#fce4ec
```

## 🔧 Technology Stack

### **Core Components**
- **Data Ingestion**: Apache Kafka + Infura API (Java/Python)
- **Complex Event Processing**: Apache Flink SQL (Java-based, stateful streaming)
- **Graph Construction**: Apache Spark Structured Streaming (Scala/Python)
- **Machine Learning**: PyTorch + PyTorch Geometric (Python)
- **Model**: TGAT (Temporal Graph Attention Network)
- **Explainability**: GNNExplainer (Python)
- **Deployment**: Docker + GPU acceleration

### **Why Java-Based CEP?**
The CEP layer uses **Apache Flink's Java-based stateful stream processing** because:
- **🔄 Stateful Operations**: Maintains transaction state across time windows for pattern detection
- **⚡ Low Latency**: JVM optimization for sub-millisecond pattern matching
- **🎯 Complex Patterns**: SQL-based temporal pattern queries with event-time processing
- **💾 Fault Tolerance**: Distributed checkpointing for exactly-once processing guarantees
- **📈 Scalability**: Horizontal scaling with consistent state management

### **Infrastructure**
- **Orchestration**: Docker Compose
- **Monitoring**: Custom dashboards
- **Data Storage**: In-memory + checkpointing
- **API Integration**: Infura Ethereum API

## 📊 Performance Targets

| Metric | Target | Current Status |
|--------|--------|----------------|
| **Inference Latency** | <1 second | 🎯 Target |
| **Graph Construction** | <500ms per snapshot | 🎯 Target |
| **Throughput** | >1000 tx/s | 🎯 Target |
| **CEP Filtering** | >85% benign removal | 🎯 Target |
| **Explainability** | >90% interpretability | 🎯 Target |
| **Model Performance** | >80% F1 Score | 🎯 Target |
| **Resource Usage** | <70% CPU/GPU | 🎯 Target |

## 📚 Datasets

### **1. Elliptic Bitcoin Dataset** (Training)
- **Source**: Kaggle public dataset
- **Records**: 203,769 transactions
- **Features**: 166 features per transaction
- **Structure**: Directed graph (nodes=transactions, edges=bitcoin flows)
- **Files**:
  - `elliptic_txs_features.csv` - Transaction features
  - `elliptic_txs_classes.csv` - Transaction labels (illicit/licit/unknown)
  - `elliptic_txs_edgelist.csv` - Transaction connections

### **2. Live Ethereum Data** (Evaluation)
- **Source**: Infura API + Sepolia Testnet
- **Type**: Continuous streaming data
- **Features**: >20 transaction attributes
- **Structure**: Real-time transaction flows

## 🚀 Quick Start

### Prerequisites
```bash
# Required software
- Docker & Docker Compose
- Python 3.8+
- Java 11+ (required for Flink CEP stateful processing)
- CUDA-compatible GPU (recommended)
- 16GB+ RAM
- Maven 3.6+ (for Java CEP components)
```

### Installation
```bash
# Clone repository
git clone https://github.com/yourusername/real-time-blockchain-aml.git
cd real-time-blockchain-aml

# Set up environment
python -m venv venv
source venv/bin/activate  # Windows: venv\Scripts\activate

# Install dependencies
pip install -r requirements.txt

# Configure environment
cp .env.example .env
# Edit .env with your Infura API key
```

### Quick Start
```bash
# Start core infrastructure
docker-compose up -d zookeeper kafka flink

# Start the pipeline
docker-compose up -d --build

# Monitor services
docker-compose ps
```

### Access Interfaces
- **Kafka UI**: http://localhost:8080
- **Flink Dashboard**: http://localhost:8081  
- **AML Dashboard**: http://localhost:3000
- **API Docs**: http://localhost:8000/docs

## 🏗️ Project Structure

```
real-time-blockchain-aml/
├── 📁 ingestion/              # Kafka + Infura integration
│   ├── infura_client.py       # Ethereum API client
│   ├── kafka_producer.py      # Transaction streaming
│   └── config/                # API configuration
├── 📁 cep/                    # Complex Event Processing (Java-based)
│   ├── flink_sql_jobs/        # Stateful CEP pattern detection (Java)
│   ├── pattern_rules.sql      # AML rule definitions (SQL)
│   ├── java_operators/        # Custom Flink operators (Java)
│   └── deployment/            # Flink cluster configuration
├── 📁 graph/                  # Graph construction layer
│   ├── spark_streaming.py     # Windowed graph builder
│   ├── graph_builder.py       # Transaction → Graph conversion
│   └── optimization/          # Performance tuning
├── 📁 gnn/                    # TGAT model layer
│   ├── tgat_model.py         # Model implementation
│   ├── training/             # Elliptic dataset training
│   ├── inference_service.py  # Optimized GPU serving
│   └── checkpoints/          # Trained models
├── 📁 explainer/             # GNNExplainer integration
│   ├── gnn_explainer.py     # Attribution analysis
│   ├── visualization.py     # Explanation interfaces
│   └── evaluation/          # Interpretability testing
├── 📁 dashboard/             # Monitoring & visualization
│   ├── frontend/            # Web interface
│   ├── api/                 # REST endpoints
│   └── monitoring/          # System health
├── 📁 tutorials/             # Learning materials
│   ├── simple_streaming_demo.py      # Basic concepts
│   ├── advanced_windowing_tutorial.py # Temporal patterns
│   └── ml_streaming_optimization.py   # ML serving patterns
├── 📁 scripts/               # Utility scripts
├── 📁 tests/                 # Test suite
├── docker-compose.yml        # Service orchestration
└── requirements.txt          # Python dependencies
```

## 🔬 Research Methodology

### **Development Approach**
Modular, containerized architecture using Docker Compose orchestration with five interconnected layers:

1. **📥 Data Ingestion Layer**: Kafka broker + Infura API integration with asyncio
2. **🔍 Complex Event Processing**: Flink SQL (Java-based) for stateful pattern detection with configurable CEP rules
3. **📊 Graph Construction**: Spark Structured Streaming with PyTorch Geometric
4. **🧠 ML Inference**: TGAT model with GPU optimization and memory management
5. **📱 Visualization**: Real-time dashboard with explainability interface

### **Evaluation Framework**

#### **Accuracy Assessment**
- **Metrics**: Precision, Recall, F1-Score (focus on minority class)
- **Validation**: Time-split cross-validation to prevent data leakage
- **Thresholds**: ROC-AUC and PR-AUC analysis

#### **Performance Evaluation**
- **Latency**: End-to-end transaction → alert timing
- **Throughput**: System capacity under varying loads
- **Scalability**: Stress testing with synthetic transaction generation

#### **Explainability Testing**
- **User Studies**: >90% interpretability validation
- **Attribution Quality**: Node/edge importance accuracy
- **Visualization**: Real-time explanation delivery

## 📈 Expected Outcomes

### **Performance Benchmarks**
- ✅ **85%+ benign transaction filtering** at CEP stage
- ✅ **<500ms graph construction** with 5-min sliding windows
- ✅ **<1 second inference** with dockerized GPU deployment
- ✅ **80%+ F1 score** on Elliptic dataset benchmarks
- ✅ **>90% explainability** in user evaluations
- ✅ **>1000 tx/s throughput** with <70% resource usage

### **Research Impact**
1. **🏦 Financial Institutions**: Faster money laundering detection and disruption
2. **🏛️ Regulatory Compliance**: Streamlined audits and FATF directive support
3. **🔬 Academic Contribution**: Blueprint for streaming GNN inference in high-velocity domains
4. **🌐 Broader Impact**: Enhanced financial stability and reduced illicit capital flows

## 🔍 Research Gaps Addressed

| Research Gap | Our Solution | Innovation |
|--------------|--------------|------------|
| **No end-to-end CEP+GNN** | Kafka-Flink-Spark-TGAT pipeline | First integrated streaming system |
| **Missing streaming explainability** | Real-time GNNExplainer | Per-alert attribution analysis |
| **Batch-only GNN inference** | GPU-optimized TGAT serving | Sub-second temporal graph inference |
| **Rigid CEP rules** | Configurable Flink SQL patterns | Adaptive rule-based filtering |

## 🤝 Contributing

We welcome contributions! See our [Contributing Guidelines](CONTRIBUTING.md) for details.

### Development Setup
```bash
# Development installation
pip install -r requirements-dev.txt

# Run tests
pytest tests/

# Code quality
black . && flake8 . && mypy .
```

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

### **Academic References**
- **TGAT**: [Temporal Graph Attention Network](https://arxiv.org/abs/2002.07962)
- **GNNExplainer**: [GNNExplainer: Generating Explanations for Graph Neural Networks](https://arxiv.org/abs/1903.03894)
- **Elliptic Dataset**: [The Elliptic Data Set](https://www.kaggle.com/ellipticco/elliptic-data-set)

### **Technology Stack**
- [Apache Kafka](https://kafka.apache.org/) - Distributed streaming platform
- [Apache Flink](https://flink.apache.org/) - Stream processing framework
- [Apache Spark](https://spark.apache.org/) - Unified analytics engine
- [PyTorch Geometric](https://pytorch-geometric.readthedocs.io/) - Graph neural networks
- [Infura](https://infura.io/) - Ethereum infrastructure

### **Research Community**
Special thanks to the blockchain AML research community and the authors whose work inspired this project.

---

## 📞 Contact

- **Principal Investigator**: [Your Name]
- **Institution**: [Your University/Organization]
- **Email**: [<EMAIL>]
- **Project Repository**: [GitHub Link]

---

*This project represents cutting-edge research in real-time blockchain AML, bridging the gap between low-latency Complex Event Processing and batch-processed Graph Neural Networks.*
