# Project Structure

## Core Components

```
/
├── src/                      # Source code
│   ├── ingestion/           # Blockchain data ingestion
│   ├── cep_layer/           # Complex Event Processing
│   ├── graph_layer/         # Graph construction and analysis
│   ├── models/              # ML models and algorithms
│   │   └── tgat/           # Temporal Graph Attention Network
│   └── dashboard/           # Monitoring dashboard
│
├── configs/                  # Configuration files
│   ├── docker/              # Docker compose files
│   ├── kafka/               # Kafka configuration
│   └── spark/               # Spark configuration
│
├── scripts/                  # Utility scripts
│   ├── deployment/          # Deployment scripts
│   ├── monitoring/          # Monitoring scripts
│   └── maintenance/         # Maintenance scripts
│
├── tests/                    # Test files
│   ├── unit/               
│   ├── integration/        
│   └── performance/        
│
├── docs/                     # Documentation
│   ├── architecture/        # System architecture
│   ├── api/                 # API documentation
│   ├── deployment/          # Deployment guides
│   └── diagrams/            # System diagrams
│
└── tools/                    # Development tools
    ├── analysis/            # Analysis tools
    └── debugging/           # Debugging tools
```

## Additional Components

```
/
├── data/                     # Data files
│   ├── raw/                 # Raw blockchain data
│   ├── processed/           # Processed data
│   └── models/              # Trained models
│
├── ios_aml_app/             # iOS application
│
└── logs/                    # Application logs
    ├── ingestion/
    ├── cep/
    ├── graph/
    └── models/
```

## Key Files

- `README.md` - Project overview and setup instructions
- `docker-compose.yml` - Main Docker Compose configuration
- `requirements.txt` - Python dependencies
- `.env` - Environment variables (not in version control)
- `.gitignore` - Git ignore rules

## Cleanup Rules

1. No backup files in main directories
2. No duplicate directories (e.g., 'dataset copy')
3. All configuration files in configs/
4. All documentation in docs/
5. All source code in src/
6. All tests in tests/
7. All tools in tools/

## 🎯 Benefits of This Organization

1. **🔍 Clear Separation of Concerns**: Each directory has a specific purpose
2. **📚 Easy Navigation**: Logical grouping of related files
3. **🧪 Better Testing**: All tests organized in one location
4. **🚀 Simplified Deployment**: Configuration and scripts separated
5. **📖 Comprehensive Documentation**: Each component well-documented
6. **🔧 Easier Maintenance**: Related files grouped together
7. **👥 Team Collaboration**: Clear structure for multiple developers

## 🔗 Component Integration

The organized structure maintains clear integration paths:
```
ingestion → cep_layer → graph_layer → tgat_layer
                                           ↓
database_layer ← gnn_explainer_layer ←─────┘
     ↓
dashboard (future)
```

## 📝 Next Steps

With this organized structure, the project is ready for:
1. **End-to-end integration testing**
2. **Web dashboard development**
3. **Production deployment**
4. **Team collaboration**
5. **Continuous integration setup** 