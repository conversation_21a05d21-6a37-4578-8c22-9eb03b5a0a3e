# Test Files Summary

## 🧪 Remaining Test Files After Cleanup

### CEP (Complex Event Processing) Tests
- **`debug_enhanced_cep.py`** - Quick debug test for CEP layer
  - Single high-value transaction test (10 ETH)
  - Minimal setup for rapid CEP debugging
  - Use: `python debug_enhanced_cep.py`

- **`test_enhanced_cep_focused.py`** - Comprehensive CEP pattern testing  
  - Tests all 5 AML patterns: High Value, Round Amount, Rapid Succession, Micro-Structuring, Velocity
  - Focused test data with clear expected outcomes
  - Use: `python test_enhanced_cep_focused.py`

### Graph Construction Tests
- **`test_graph_layer_core.py`** - Core graph construction functionality
  - Basic imports, configuration, NetworkX graphs
  - Node/edge feature generation, serialization
  - Cross-platform compatibility (macOS safe)
  - Use: `python test_graph_layer_core.py`

- **`test_graph_layer_optimized.py`** - Performance optimization tests
  - Context7 performance enhancements
  - torch.compile optimizations, memory efficiency
  - Vectorized operations, sparse tensors
  - Use: `python test_graph_layer_optimized.py`

### Integration Tests  
- **`test_pipeline_integration.py`** - End-to-end pipeline testing
  - Kafka connectivity, topic management
  - CEP → Graph Layer integration
  - Producer/consumer validation
  - Use: `python test_pipeline_integration.py`

- **`test_ingestion.py`** - Data ingestion layer testing
  - Infura API integration
  - Kafka producer functionality
  - Real-time Sepolia data streaming
  - Use: `python test_ingestion.py`

## 🚀 Recommended Testing Workflow

1. **Quick CEP Debug**: `python debug_enhanced_cep.py`
2. **Full CEP Testing**: `python test_enhanced_cep_focused.py`  
3. **Graph Core Tests**: `python test_graph_layer_core.py`
4. **Integration Tests**: `python test_pipeline_integration.py`
5. **Performance Tests**: `python test_graph_layer_optimized.py`

## 📋 Removed Files (Redundant)
- `direct_test.py` - Basic test superseded by debug_enhanced_cep.py
- `simple_cep_test.py` - Simple test superseded by test_enhanced_cep_focused.py  
- `test_enhanced_cep.py` - Less comprehensive than test_enhanced_cep_focused.py
- `test_graph_layer.py` - Basic test superseded by test_graph_layer_core.py
