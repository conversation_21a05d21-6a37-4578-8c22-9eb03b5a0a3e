"""
AML Database Storage Component

This module handles storage of detected illicit transactions for:
1. Audit trail and compliance reporting
2. Future model training data
3. Performance monitoring and analytics
"""

import sqlite3
import pandas as pd
import numpy as np
import json
import logging
from datetime import datetime, timezone
from typing import Dict, List, Optional, Tuple
import os
from dataclasses import dataclass
import hashlib

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@dataclass
class IllicitTransaction:
    """Data class for illicit transaction records."""
    
    transaction_id: str
    timestamp: datetime
    source_address: str
    destination_address: str
    amount: float
    transaction_hash: str
    risk_score: float
    confidence_level: float
    detection_method: str
    feature_vector: List[float]
    network_features: Dict
    explanation: Optional[Dict] = None
    human_verified: Optional[bool] = None
    verification_timestamp: Optional[datetime] = None
    notes: Optional[str] = None

class AMLDatabase:
    """Database manager for AML detection results."""
    
    def __init__(self, db_path: str = "aml_detections.db"):
        self.db_path = db_path
        self.connection = None
        self._initialize_database()
    
    def _initialize_database(self):
        """Initialize the database with required tables."""
        logger.info(f"Initializing AML database: {self.db_path}")
        
        self.connection = sqlite3.connect(self.db_path)
        self.connection.row_factory = sqlite3.Row  # Enable dict-like access
        
        # Create tables
        self._create_tables()
        
        logger.info("Database initialized successfully")
    
    def _create_tables(self):
        """Create database tables for AML data storage."""
        
        cursor = self.connection.cursor()
        
        # Main illicit transactions table
        cursor.execute('''
        CREATE TABLE IF NOT EXISTS illicit_transactions (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            transaction_id TEXT UNIQUE NOT NULL,
            timestamp DATETIME NOT NULL,
            source_address TEXT NOT NULL,
            destination_address TEXT NOT NULL,
            amount REAL NOT NULL,
            transaction_hash TEXT NOT NULL,
            risk_score REAL NOT NULL,
            confidence_level REAL NOT NULL,
            detection_method TEXT NOT NULL,
            feature_vector TEXT NOT NULL,  -- JSON string
            network_features TEXT NOT NULL,  -- JSON string
            explanation TEXT,  -- JSON string
            human_verified BOOLEAN,
            verification_timestamp DATETIME,
            notes TEXT,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
        )
        ''')
        
        # Model performance tracking table
        cursor.execute('''
        CREATE TABLE IF NOT EXISTS model_performance (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            model_version TEXT NOT NULL,
            evaluation_date DATETIME NOT NULL,
            dataset_size INTEGER NOT NULL,
            accuracy REAL NOT NULL,
            precision_score REAL NOT NULL,
            recall REAL NOT NULL,
            f1_score REAL NOT NULL,
            auc_score REAL NOT NULL,
            false_positive_rate REAL NOT NULL,
            false_negative_rate REAL NOT NULL,
            notes TEXT,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP
        )
        ''')
        
        # Detection statistics table
        cursor.execute('''
        CREATE TABLE IF NOT EXISTS detection_statistics (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            date DATE NOT NULL,
            total_transactions INTEGER NOT NULL,
            flagged_transactions INTEGER NOT NULL,
            confirmed_illicit INTEGER NOT NULL,
            false_positives INTEGER NOT NULL,
            avg_risk_score REAL NOT NULL,
            processing_time_ms REAL NOT NULL,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP
        )
        ''')
        
        # Feature importance tracking
        cursor.execute('''
        CREATE TABLE IF NOT EXISTS feature_importance (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            transaction_id TEXT NOT NULL,
            feature_name TEXT NOT NULL,
            importance_score REAL NOT NULL,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (transaction_id) REFERENCES illicit_transactions (transaction_id)
        )
        ''')
        
        # Create indexes for better performance
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_transaction_id ON illicit_transactions (transaction_id)')
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_timestamp ON illicit_transactions (timestamp)')
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_risk_score ON illicit_transactions (risk_score)')
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_detection_date ON detection_statistics (date)')
        
        self.connection.commit()
        logger.info("Database tables created/verified successfully")
    
    def store_illicit_transaction(self, transaction: IllicitTransaction) -> bool:
        """Store a detected illicit transaction in the database."""
        
        try:
            cursor = self.connection.cursor()
            
            # Convert complex data to JSON strings
            feature_vector_json = json.dumps(transaction.feature_vector)
            network_features_json = json.dumps(transaction.network_features)
            explanation_json = json.dumps(transaction.explanation) if transaction.explanation else None
            
            cursor.execute('''
            INSERT OR REPLACE INTO illicit_transactions (
                transaction_id, timestamp, source_address, destination_address,
                amount, transaction_hash, risk_score, confidence_level,
                detection_method, feature_vector, network_features,
                explanation, human_verified, verification_timestamp, notes,
                updated_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
            ''', (
                transaction.transaction_id,
                transaction.timestamp,
                transaction.source_address,
                transaction.destination_address,
                transaction.amount,
                transaction.transaction_hash,
                transaction.risk_score,
                transaction.confidence_level,
                transaction.detection_method,
                feature_vector_json,
                network_features_json,
                explanation_json,
                transaction.human_verified,
                transaction.verification_timestamp,
                transaction.notes
            ))
            
            self.connection.commit()
            logger.info(f"Stored illicit transaction: {transaction.transaction_id}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to store transaction {transaction.transaction_id}: {e}")
            return False
    
    def get_transactions_for_training(self, verified_only: bool = False, 
                                    limit: Optional[int] = None) -> List[Dict]:
        """Retrieve transactions for model training."""
        
        cursor = self.connection.cursor()
        
        query = '''
        SELECT transaction_id, feature_vector, risk_score, human_verified, explanation
        FROM illicit_transactions
        '''
        
        if verified_only:
            query += ' WHERE human_verified = 1'
        
        if limit:
            query += f' LIMIT {limit}'
        
        cursor.execute(query)
        rows = cursor.fetchall()
        
        training_data = []
        for row in rows:
            training_data.append({
                'transaction_id': row['transaction_id'],
                'features': json.loads(row['feature_vector']),
                'risk_score': row['risk_score'],
                'human_verified': row['human_verified'],
                'explanation': json.loads(row['explanation']) if row['explanation'] else None
            })
        
        logger.info(f"Retrieved {len(training_data)} transactions for training")
        return training_data
    
    def update_human_verification(self, transaction_id: str, verified: bool, 
                                notes: Optional[str] = None) -> bool:
        """Update human verification status for a transaction."""
        
        try:
            cursor = self.connection.cursor()
            
            cursor.execute('''
            UPDATE illicit_transactions 
            SET human_verified = ?, verification_timestamp = CURRENT_TIMESTAMP, 
                notes = ?, updated_at = CURRENT_TIMESTAMP
            WHERE transaction_id = ?
            ''', (verified, notes, transaction_id))
            
            self.connection.commit()
            
            if cursor.rowcount > 0:
                logger.info(f"Updated verification for transaction {transaction_id}: {verified}")
                return True
            else:
                logger.warning(f"Transaction {transaction_id} not found for verification update")
                return False
                
        except Exception as e:
            logger.error(f"Failed to update verification for {transaction_id}: {e}")
            return False
    
    def store_model_performance(self, model_version: str, metrics: Dict[str, float], 
                              dataset_size: int, notes: Optional[str] = None) -> bool:
        """Store model performance metrics."""
        
        try:
            cursor = self.connection.cursor()
            
            cursor.execute('''
            INSERT INTO model_performance (
                model_version, evaluation_date, dataset_size, accuracy,
                precision_score, recall, f1_score, auc_score,
                false_positive_rate, false_negative_rate, notes
            ) VALUES (?, CURRENT_TIMESTAMP, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                model_version,
                dataset_size,
                metrics.get('accuracy', 0.0),
                metrics.get('precision', 0.0),
                metrics.get('recall', 0.0),
                metrics.get('f1', 0.0),
                metrics.get('auc', 0.0),
                metrics.get('fpr', 0.0),
                metrics.get('fnr', 0.0),
                notes
            ))
            
            self.connection.commit()
            logger.info(f"Stored performance metrics for model {model_version}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to store model performance: {e}")
            return False
    
    def store_daily_statistics(self, date: datetime, total_transactions: int,
                             flagged_transactions: int, confirmed_illicit: int,
                             false_positives: int, avg_risk_score: float,
                             processing_time_ms: float) -> bool:
        """Store daily detection statistics."""
        
        try:
            cursor = self.connection.cursor()
            
            cursor.execute('''
            INSERT OR REPLACE INTO detection_statistics (
                date, total_transactions, flagged_transactions,
                confirmed_illicit, false_positives, avg_risk_score,
                processing_time_ms
            ) VALUES (?, ?, ?, ?, ?, ?, ?)
            ''', (
                date.date(),
                total_transactions,
                flagged_transactions,
                confirmed_illicit,
                false_positives,
                avg_risk_score,
                processing_time_ms
            ))
            
            self.connection.commit()
            logger.info(f"Stored daily statistics for {date.date()}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to store daily statistics: {e}")
            return False
    
    def get_detection_summary(self, days: int = 30) -> Dict:
        """Get detection summary for the last N days."""
        
        cursor = self.connection.cursor()
        
        # Overall statistics
        cursor.execute('''
        SELECT 
            COUNT(*) as total_detections,
            AVG(risk_score) as avg_risk_score,
            SUM(CASE WHEN human_verified = 1 THEN 1 ELSE 0 END) as verified_count,
            SUM(CASE WHEN human_verified = 0 THEN 1 ELSE 0 END) as false_positive_count
        FROM illicit_transactions 
        WHERE timestamp >= datetime('now', '-{} days')
        '''.format(days))
        
        overall_stats = dict(cursor.fetchone())
        
        # Daily trends
        cursor.execute('''
        SELECT 
            date,
            total_transactions,
            flagged_transactions,
            confirmed_illicit,
            false_positives,
            avg_risk_score
        FROM detection_statistics 
        WHERE date >= date('now', '-{} days')
        ORDER BY date DESC
        '''.format(days))
        
        daily_stats = [dict(row) for row in cursor.fetchall()]
        
        # Top risk transactions
        cursor.execute('''
        SELECT transaction_id, risk_score, timestamp, human_verified
        FROM illicit_transactions
        WHERE timestamp >= datetime('now', '-{} days')
        ORDER BY risk_score DESC
        LIMIT 10
        '''.format(days))
        
        top_risks = [dict(row) for row in cursor.fetchall()]
        
        summary = {
            'period_days': days,
            'overall_statistics': overall_stats,
            'daily_trends': daily_stats,
            'top_risk_transactions': top_risks,
            'generated_at': datetime.now(timezone.utc).isoformat()
        }
        
        return summary
    
    def export_training_dataset(self, output_path: str, verified_only: bool = True) -> bool:
        """Export stored transactions as a training dataset."""
        
        try:
            transactions = self.get_transactions_for_training(verified_only=verified_only)
            
            if not transactions:
                logger.warning("No transactions found for export")
                return False
            
            # Convert to DataFrame for easy export
            export_data = []
            for tx in transactions:
                row = {
                    'transaction_id': tx['transaction_id'],
                    'risk_score': tx['risk_score'],
                    'human_verified': tx['human_verified']
                }
                
                # Add feature columns
                for i, feature_val in enumerate(tx['features']):
                    row[f'feature_{i}'] = feature_val
                
                export_data.append(row)
            
            df = pd.DataFrame(export_data)
            df.to_csv(output_path, index=False)
            
            logger.info(f"Exported {len(export_data)} transactions to {output_path}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to export training dataset: {e}")
            return False
    
    def close(self):
        """Close database connection."""
        if self.connection:
            self.connection.close()
            logger.info("Database connection closed")

class AMLDataIngester:
    """Helper class for ingesting TGAT model predictions into the database."""
    
    def __init__(self, database: AMLDatabase):
        self.database = database
    
    def ingest_tgat_predictions(self, predictions: Dict, transaction_data: Dict,
                              model_version: str = "TGAT_v1.0") -> int:
        """Ingest TGAT model predictions into the database."""
        
        ingested_count = 0
        
        node_predictions = predictions.get('node_predictions', [])
        risk_scores = predictions.get('risk_scores', [])
        
        for i, (pred, risk_score) in enumerate(zip(node_predictions, risk_scores)):
            if risk_score > 0.5:  # Only store suspicious transactions
                
                # Create transaction record
                tx_data = transaction_data.get(str(i), {})
                
                transaction = IllicitTransaction(
                    transaction_id=tx_data.get('transaction_id', f"TX_{i}"),
                    timestamp=datetime.now(timezone.utc),
                    source_address=tx_data.get('source_address', f"ADDR_{i}"),
                    destination_address=tx_data.get('destination_address', f"ADDR_{i+1}"),
                    amount=tx_data.get('amount', 0.0),
                    transaction_hash=tx_data.get('hash', f"HASH_{i}"),
                    risk_score=float(risk_score),
                    confidence_level=float(abs(risk_score - 0.5) * 2),  # Distance from neutral
                    detection_method=model_version,
                    feature_vector=tx_data.get('features', []),
                    network_features={
                        'node_degree': tx_data.get('node_degree', 0),
                        'clustering_coefficient': tx_data.get('clustering_coeff', 0.0),
                        'subgraph_size': tx_data.get('subgraph_size', 0)
                    }
                )
                
                if self.database.store_illicit_transaction(transaction):
                    ingested_count += 1
        
        logger.info(f"Ingested {ingested_count} suspicious transactions")
        return ingested_count

def demo_database_storage():
    """Demonstrate database storage functionality."""
    
    print("💾 AML Database Storage Demo")
    
    # Initialize database
    db = AMLDatabase("demo_aml.db")
    ingester = AMLDataIngester(db)
    
    # Create sample illicit transactions
    sample_transactions = []
    for i in range(5):
        transaction = IllicitTransaction(
            transaction_id=f"DEMO_TX_{i}",
            timestamp=datetime.now(timezone.utc),
            source_address=f"0xABC{i:03d}",
            destination_address=f"0xDEF{i:03d}",
            amount=1000.0 + i * 500,
            transaction_hash=f"0x{hashlib.md5(f'tx_{i}'.encode()).hexdigest()}",
            risk_score=0.6 + i * 0.1,
            confidence_level=0.8,
            detection_method="TGAT_v1.0",
            feature_vector=[np.random.random() for _ in range(64)],
            network_features={
                'node_degree': 5 + i,
                'clustering_coefficient': 0.3 + i * 0.1,
                'subgraph_size': 10 + i * 2
            }
        )
        sample_transactions.append(transaction)
    
    # Store transactions
    print(f"\n📥 Storing {len(sample_transactions)} sample transactions...")
    for tx in sample_transactions:
        db.store_illicit_transaction(tx)
    
    # Store model performance
    print("\n📊 Storing model performance metrics...")
    metrics = {
        'accuracy': 0.875,
        'precision': 0.723,
        'recall': 0.891,
        'f1': 0.798,
        'auc': 0.934
    }
    db.store_model_performance("TGAT_v1.0", metrics, 10000)
    
    # Store daily statistics
    print("\n📈 Storing daily statistics...")
    db.store_daily_statistics(
        date=datetime.now(timezone.utc),
        total_transactions=10000,
        flagged_transactions=150,
        confirmed_illicit=95,
        false_positives=55,
        avg_risk_score=0.73,
        processing_time_ms=245.6
    )
    
    # Update verification status
    print("\n✅ Updating human verification...")
    db.update_human_verification("DEMO_TX_0", True, "Confirmed illicit by analyst")
    db.update_human_verification("DEMO_TX_1", False, "False positive - legitimate transaction")
    
    # Get summary
    print("\n📋 Generating detection summary...")
    summary = db.get_detection_summary(days=30)
    
    print(f"   Total detections: {summary['overall_statistics']['total_detections']}")
    print(f"   Average risk score: {summary['overall_statistics']['avg_risk_score']:.3f}")
    print(f"   Verified count: {summary['overall_statistics']['verified_count']}")
    print(f"   False positives: {summary['overall_statistics']['false_positive_count']}")
    
    # Export training data
    print("\n💾 Exporting training dataset...")
    db.export_training_dataset("demo_training_data.csv", verified_only=False)
    
    # Cleanup
    db.close()
    
    print("\n✅ Database storage demo complete!")
    print("   📄 Check demo_aml.db and demo_training_data.csv files")

if __name__ == "__main__":
    demo_database_storage() 