# Database Layer

This directory contains components for storing and managing AML detection results.

## Components

### `aml_database_storage.py`
- **Purpose**: Main database storage component for illicit transaction detection results
- **Features**:
  - SQLite database for audit trail and compliance reporting
  - Storage of detected illicit transactions with full context
  - Model performance tracking and metrics storage
  - Human verification workflow for compliance
  - Training data export for model improvement

## Database Schema

### Tables:
- `illicit_transactions`: Main storage for flagged transactions
- `model_performance`: Model evaluation metrics over time
- `detection_statistics`: Daily detection summaries
- `feature_importance`: Feature-level explanations for transactions

## Usage

```python
from database_layer.aml_database_storage import AMLDatabase, IllicitTransaction

# Initialize database
db = AMLDatabase("aml_detections.db")

# Store detected illicit transaction
transaction = IllicitTransaction(
    transaction_id="tx_123",
    risk_score=0.85,
    confidence_level=0.92,
    # ... other fields
)
db.store_illicit_transaction(transaction)
```

## Integration

This layer integrates with:
- TGAT model predictions (input)
- GNN Explainer results (explanations)
- Web dashboard (data source)
- Compliance reporting systems (export) 