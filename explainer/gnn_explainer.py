"""
Enhanced GNN Explainer Layer for AML Detection

This module provides state-of-the-art explainability for TGAT-based AML detection,
incorporating latest research from 2024:
- TempME: Temporal motif discovery for TGNN explainability  
- TGIB: Information bottleneck principle for temporal graphs
- Advanced PyTorch Geometric integration
- LIME/SHAP-like techniques for blockchain transactions
"""

import numpy as np
import torch
import torch.nn as nn
import torch.optim as optim
import torch.nn.functional as F
from typing import Dict, List, Tuple, Optional, Union
import matplotlib.pyplot as plt
import networkx as nx
import pandas as pd
import logging
from torch_geometric.explain import Explainer, GNNExplainer as PyGGNNExplainer
from torch_geometric.explain import PGExplainer, CaptumExplainer
from torch_geometric.data import Data
import json
import os
from datetime import datetime
import seaborn as sns
from sklearn.metrics import roc_auc_score
import warnings
warnings.filterwarnings('ignore')

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class TemporalMotifExplainer:
    """
    Enhanced explainer for temporal graph neural networks using motif discovery.
    
    Based on TempME: Towards the Explainability of Temporal Graph Neural Networks 
    via Motif Discovery (NeurIPS 2023)
    """
    
    def __init__(self, model: nn.Module, num_hops: int = 3, device: str = 'cpu'):
        self.model = model
        self.num_hops = num_hops
        self.device = device
        self.model.eval()
        
        # Temporal motif discovery parameters
        self.motif_size_range = (3, 8)  # Min and max motif sizes
        self.temporal_window = 10  # Time window for motif discovery
        self.information_threshold = 0.1  # Information bottleneck threshold
        
    def discover_temporal_motifs(self, x: torch.Tensor, edge_index: torch.Tensor,
                                edge_time: torch.Tensor, target_node: int) -> List[Dict]:
        """
        Discover temporal motifs around a target node for explanation.
        
        Implements motif discovery algorithm inspired by TempME.
        """
        motifs = []
        
        # Get temporal neighborhood
        temporal_subgraph = self._extract_temporal_subgraph(
            target_node, edge_index, edge_time, self.temporal_window
        )
        
        # Discover recurring patterns
        for motif_size in range(self.motif_size_range[0], self.motif_size_range[1] + 1):
            size_motifs = self._find_motifs_of_size(temporal_subgraph, motif_size)
            motifs.extend(size_motifs)
        
        # Rank motifs by importance using information bottleneck
        ranked_motifs = self._rank_motifs_by_importance(motifs, x, edge_index, target_node)
        
        return ranked_motifs[:5]  # Return top 5 motifs
    
    def _extract_temporal_subgraph(self, target_node: int, edge_index: torch.Tensor,
                                  edge_time: torch.Tensor, window: int) -> Dict:
        """Extract temporal subgraph around target node."""
        
        # Find edges involving target node
        target_edges = (edge_index[0] == target_node) | (edge_index[1] == target_node)
        if target_edges.sum() == 0:
            return {'nodes': [target_node], 'edges': [], 'times': []}
        
        # Get time range
        target_times = edge_time[target_edges]
        if len(target_times) == 0:
            return {'nodes': [target_node], 'edges': [], 'times': []}
            
        time_center = target_times.float().mean()
        time_window = [time_center - window/2, time_center + window/2]
        
        # Filter edges by time window
        time_mask = (edge_time >= time_window[0]) & (edge_time <= time_window[1])
        windowed_edges = edge_index[:, time_mask]
        windowed_times = edge_time[time_mask]
        
        # Get all nodes in window
        nodes = torch.cat([windowed_edges[0], windowed_edges[1]]).unique()
        
        return {
            'nodes': nodes.tolist(),
            'edges': windowed_edges.tolist(),
            'times': windowed_times.tolist()
        }
    
    def _find_motifs_of_size(self, subgraph: Dict, size: int) -> List[Dict]:
        """Find temporal motifs of specific size."""
        motifs = []
        nodes = subgraph['nodes']
        edges = subgraph['edges']
        times = subgraph['times']
        
        if len(nodes) < size:
            return motifs
        
        # Create temporal graph representation
        G = nx.DiGraph()
        for i, (src, dst) in enumerate(zip(edges[0], edges[1])):
            G.add_edge(src, dst, time=times[i])
        
        # Find connected subgraphs of given size
        for node_subset in self._get_node_combinations(nodes, size):
            subgraph_edges = []
            subgraph_times = []
            
            for src, dst, data in G.edges(data=True):
                if src in node_subset and dst in node_subset:
                    subgraph_edges.append([src, dst])
                    subgraph_times.append(data['time'])
            
            if len(subgraph_edges) >= size - 1:  # At least size-1 edges for connectivity
                motifs.append({
                    'nodes': list(node_subset),
                    'edges': subgraph_edges,
                    'times': subgraph_times,
                    'pattern_hash': hash(tuple(sorted(subgraph_edges)))
                })
        
        return motifs
    
    def _get_node_combinations(self, nodes: List[int], size: int) -> List[Tuple]:
        """Generate combinations of nodes for motif discovery."""
        import itertools
        return list(itertools.combinations(nodes[:min(20, len(nodes))], min(size, len(nodes))))
    
    def _rank_motifs_by_importance(self, motifs: List[Dict], x: torch.Tensor,
                                  edge_index: torch.Tensor, target_node: int) -> List[Dict]:
        """Rank motifs by their importance using information bottleneck principle."""
        
        if not motifs:
            return []
        
        # Get original prediction
        with torch.no_grad():
            original_pred = self.model(x, edge_index, torch.zeros(edge_index.size(1)), 
                                     torch.ones(edge_index.size(1)))
            original_score = F.softmax(original_pred[target_node], dim=0)[1].item()
        
        scored_motifs = []
        for motif in motifs:
            # Calculate motif importance score
            importance = self._calculate_motif_importance(motif, x, edge_index, target_node, original_score)
            motif['importance_score'] = importance
            scored_motifs.append(motif)
        
        # Sort by importance
        scored_motifs.sort(key=lambda x: x['importance_score'], reverse=True)
        return scored_motifs
    
    def _calculate_motif_importance(self, motif: Dict, x: torch.Tensor, edge_index: torch.Tensor,
                                  target_node: int, original_score: float) -> float:
        """Calculate importance of a motif using perturbation analysis."""
        
        try:
            # Create perturbed graph by removing motif edges
            motif_edges = torch.tensor(motif['edges']).t()
            if motif_edges.numel() == 0:
                return 0.0
            
            # Remove motif edges from graph
            edge_list = edge_index.t().tolist()
            motif_edge_list = motif_edges.tolist()
            
            perturbed_edges = []
            for edge in edge_list:
                if edge not in motif_edge_list:
                    perturbed_edges.append(edge)
            
            if not perturbed_edges:
                return 0.0
            
            perturbed_edge_index = torch.tensor(perturbed_edges).t()
            
            # Get prediction without motif
            with torch.no_grad():
                perturbed_pred = self.model(x, perturbed_edge_index, 
                                          torch.zeros(perturbed_edge_index.size(1)),
                                          torch.ones(perturbed_edge_index.size(1)))
                perturbed_score = F.softmax(perturbed_pred[target_node], dim=0)[1].item()
            
            # Importance is the difference in prediction
            importance = abs(original_score - perturbed_score)
            return importance
            
        except Exception as e:
            logger.warning(f"Error calculating motif importance: {e}")
            return 0.0

class InformationBottleneckExplainer:
    """
    Information Bottleneck-based explainer for temporal graphs.
    
    Based on TGIB: Self-Explainable Temporal Graph Networks based on 
    Graph Information Bottleneck (KDD 2024)
    """
    
    def __init__(self, model: nn.Module, beta: float = 0.01, device: str = 'cpu'):
        self.model = model
        self.beta = beta  # Information bottleneck trade-off parameter
        self.device = device
        
    def explain_with_bottleneck(self, x: torch.Tensor, edge_index: torch.Tensor,
                               edge_attr: torch.Tensor, edge_time: torch.Tensor,
                               target_node: int, epochs: int = 100) -> Dict:
        """
        Generate explanation using information bottleneck principle.
        
        Finds minimal sufficient information for prediction.
        """
        
        # Initialize learnable masks
        num_edges = edge_index.size(1)
        num_features = x.size(1)
        
        edge_mask = nn.Parameter(torch.randn(num_edges, requires_grad=True, device=self.device))
        feature_mask = nn.Parameter(torch.randn(num_features, requires_grad=True, device=self.device))
        
        optimizer = optim.Adam([edge_mask, feature_mask], lr=0.01)
        
        # Get original prediction
        with torch.no_grad():
            original_pred = self.model(x, edge_index, edge_attr, edge_time)
            original_logits = original_pred[target_node]
        
        best_explanation = None
        best_score = float('inf')
        
        for epoch in range(epochs):
            optimizer.zero_grad()
            
            # Apply masks with information bottleneck
            edge_weights = torch.sigmoid(edge_mask)
            feature_weights = torch.sigmoid(feature_mask)
            
            # Masked inputs
            masked_x = x * feature_weights.unsqueeze(0)
            masked_edge_attr = edge_attr * edge_weights.unsqueeze(1)
            
            # Forward pass
            pred = self.model(masked_x, edge_index, masked_edge_attr, edge_time)
            pred_logits = pred[target_node]
            
            # Information bottleneck loss
            # Prediction loss (fidelity)
            prediction_loss = F.mse_loss(pred_logits, original_logits)
            
            # Information loss (sparsity)
            edge_info_loss = torch.sum(edge_weights * torch.log(edge_weights + 1e-8))
            feature_info_loss = torch.sum(feature_weights * torch.log(feature_weights + 1e-8))
            info_loss = edge_info_loss + feature_info_loss
            
            # Total loss
            total_loss = prediction_loss + self.beta * info_loss
            
            total_loss.backward()
            optimizer.step()
            
            # Track best explanation
            if total_loss.item() < best_score:
                best_score = total_loss.item()
                best_explanation = {
                    'edge_mask': edge_weights.detach().cpu().numpy(),
                    'feature_mask': feature_weights.detach().cpu().numpy(),
                    'prediction_loss': prediction_loss.item(),
                    'info_loss': info_loss.item(),
                    'total_loss': total_loss.item()
                }
        
        return best_explanation

class AdvancedAMLExplainer:
    """
    Advanced AML-specific explainer combining multiple techniques.
    
    Integrates temporal motifs, information bottleneck, and domain-specific
    explanation generation for blockchain AML detection.
    """
    
    def __init__(self, model: nn.Module, device: str = 'cpu'):
        self.model = model
        self.device = device
        
        # Initialize sub-explainers
        self.motif_explainer = TemporalMotifExplainer(model, device=device)
        self.bottleneck_explainer = InformationBottleneckExplainer(model, device=device)
        
        # AML-specific feature names
        self.aml_feature_names = self._get_aml_feature_names()
        
    def generate_comprehensive_explanation(self, x: torch.Tensor, edge_index: torch.Tensor,
                                         edge_attr: torch.Tensor, edge_time: torch.Tensor,
                                         target_node: int) -> Dict:
        """Generate comprehensive explanation using multiple techniques."""
        
        logger.info(f"Generating comprehensive explanation for node {target_node}")
        
        # 1. Temporal Motif Analysis
        logger.info("Discovering temporal motifs...")
        motifs = self.motif_explainer.discover_temporal_motifs(
            x, edge_index, edge_time, target_node
        )
        
        # 2. Information Bottleneck Analysis
        logger.info("Applying information bottleneck analysis...")
        bottleneck_result = self.bottleneck_explainer.explain_with_bottleneck(
            x, edge_index, edge_attr, edge_time, target_node
        )
        
        # 3. Feature Importance Analysis
        logger.info("Analyzing feature importance...")
        feature_importance = self._analyze_feature_importance(
            x, edge_index, edge_attr, edge_time, target_node
        )
        
        # 4. Risk Assessment
        logger.info("Generating risk assessment...")
        risk_assessment = self._generate_risk_assessment(
            x, edge_index, edge_attr, edge_time, target_node,
            motifs, bottleneck_result, feature_importance
        )
        
        # 5. Compliance Report
        logger.info("Creating compliance report...")
        compliance_report = self._create_compliance_report(
            target_node, motifs, feature_importance, risk_assessment
        )
        
        explanation = {
            'node_id': target_node,
            'temporal_motifs': motifs,
            'information_bottleneck': bottleneck_result,
            'feature_importance': feature_importance,
            'risk_assessment': risk_assessment,
            'compliance_report': compliance_report,
            'explanation_quality': self._calculate_explanation_quality(motifs, bottleneck_result),
            'timestamp': datetime.now().isoformat()
        }
        
        logger.info("Comprehensive explanation generation complete")
        return explanation
    
    def _get_aml_feature_names(self) -> List[str]:
        """Get AML-specific feature names for interpretability."""
        return [
            # Transaction Features
            'transaction_amount', 'transaction_frequency', 'velocity_1h', 'velocity_24h',
            'amount_variance', 'time_between_transactions', 'circular_transactions',
            
            # Network Features
            'in_degree', 'out_degree', 'clustering_coefficient', 'betweenness_centrality',
            'eigenvector_centrality', 'pagerank_score', 'triangle_count',
            
            # Behavioral Features
            'unusual_hours', 'weekend_activity', 'cross_border_ratio', 'cash_equivalent_ratio',
            'mixing_pattern_score', 'layering_indicator', 'structuring_indicator',
            
            # Counterparty Features
            'high_risk_counterparties', 'pep_exposure', 'sanctions_exposure', 'blacklist_hits',
            'suspicious_entity_links', 'offshore_jurisdiction_links',
            
            # Temporal Features
            'time_since_first_tx', 'account_age', 'dormancy_periods', 'burst_activity',
            'seasonal_patterns', 'temporal_anomaly_score',
            
            # Geographic Features
            'jurisdiction_risk_score', 'geographic_diversity', 'high_risk_countries',
            'beneficial_owner_countries', 'ip_location_consistency',
            
            # Additional derived features
            'derivation_1', 'derivation_2', 'derivation_3', 'derivation_4', 'derivation_5',
            'pattern_1', 'pattern_2', 'pattern_3', 'pattern_4', 'pattern_5',
            'risk_1', 'risk_2', 'risk_3', 'risk_4', 'risk_5',
            'network_1', 'network_2', 'network_3', 'network_4', 'network_5',
            'temporal_1', 'temporal_2', 'temporal_3', 'temporal_4', 'temporal_5',
            'behavioral_1', 'behavioral_2', 'behavioral_3', 'behavioral_4', 'behavioral_5'
        ]
    
    def _analyze_feature_importance(self, x: torch.Tensor, edge_index: torch.Tensor,
                                   edge_attr: torch.Tensor, edge_time: torch.Tensor,
                                   target_node: int) -> Dict:
        """Analyze feature importance using perturbation analysis."""
        
        # Get original prediction
        with torch.no_grad():
            original_pred = self.model(x, edge_index, edge_attr, edge_time)
            original_score = F.softmax(original_pred[target_node], dim=0)[1].item()
        
        feature_importance = []
        node_features = x[target_node].clone()
        
        # Perturbation analysis for each feature
        for i in range(min(len(self.aml_feature_names), x.size(1))):
            # Create perturbed features
            perturbed_x = x.clone()
            perturbed_x[target_node, i] = 0  # Zero out feature
            
            try:
                with torch.no_grad():
                    perturbed_pred = self.model(perturbed_x, edge_index, edge_attr, edge_time)
                    perturbed_score = F.softmax(perturbed_pred[target_node], dim=0)[1].item()
                
                importance = abs(original_score - perturbed_score)
                feature_importance.append({
                    'feature_name': self.aml_feature_names[i],
                    'feature_index': i,
                    'importance_score': importance,
                    'original_value': node_features[i].item(),
                    'feature_contribution': importance * node_features[i].item()
                })
            except Exception as e:
                logger.warning(f"Error analyzing feature {i}: {e}")
                feature_importance.append({
                    'feature_name': self.aml_feature_names[i],
                    'feature_index': i,
                    'importance_score': 0.0,
                    'original_value': node_features[i].item(),
                    'feature_contribution': 0.0
                })
        
        # Sort by importance
        feature_importance.sort(key=lambda x: x['importance_score'], reverse=True)
        
        return {
            'top_features': feature_importance[:10],
            'all_features': feature_importance,
            'original_prediction': original_score
        }
    
    def _generate_risk_assessment(self, x: torch.Tensor, edge_index: torch.Tensor,
                                 edge_attr: torch.Tensor, edge_time: torch.Tensor,
                                 target_node: int, motifs: List[Dict],
                                 bottleneck_result: Dict, feature_importance: Dict) -> Dict:
        """Generate comprehensive risk assessment."""
        
        original_score = feature_importance['original_prediction']
        
        # Risk level classification
        if original_score > 0.8:
            risk_level = "CRITICAL"
            risk_color = "🔴"
            priority = "IMMEDIATE"
        elif original_score > 0.6:
            risk_level = "HIGH"
            risk_color = "🟠"
            priority = "URGENT"
        elif original_score > 0.4:
            risk_level = "MEDIUM"
            risk_color = "🟡"
            priority = "ELEVATED"
        else:
            risk_level = "LOW"
            risk_color = "🟢"
            priority = "ROUTINE"
        
        # Analyze risk factors
        top_features = feature_importance['top_features'][:5]
        risk_factors = []
        
        for feature in top_features:
            if feature['importance_score'] > 0.1:
                risk_factors.append({
                    'factor': feature['feature_name'],
                    'impact': feature['importance_score'],
                    'value': feature['original_value'],
                    'severity': "High" if feature['importance_score'] > 0.3 else "Medium"
                })
        
        # Temporal risk analysis
        temporal_risk = "High" if len(motifs) > 3 else "Low"
        temporal_explanation = f"Identified {len(motifs)} suspicious temporal patterns"
        
        # Network risk analysis
        network_centrality_features = [f for f in top_features if 'centrality' in f['feature_name']]
        network_risk = "High" if len(network_centrality_features) > 0 else "Low"
        
        return {
            'overall_risk': {
                'level': risk_level,
                'score': original_score,
                'color': risk_color,
                'priority': priority
            },
            'risk_factors': risk_factors,
            'temporal_analysis': {
                'risk_level': temporal_risk,
                'explanation': temporal_explanation,
                'motif_count': len(motifs)
            },
            'network_analysis': {
                'risk_level': network_risk,
                'centrality_indicators': len(network_centrality_features)
            },
            'information_complexity': {
                'sparsity_score': bottleneck_result.get('info_loss', 0),
                'explanation_efficiency': 1.0 / (bottleneck_result.get('total_loss', 1) + 1e-6)
            }
        }
    
    def _create_compliance_report(self, target_node: int, motifs: List[Dict],
                                 feature_importance: Dict, risk_assessment: Dict) -> Dict:
        """Create compliance-ready explanation report."""
        
        report = {
            'transaction_id': f"TX_{target_node}",
            'investigation_timestamp': datetime.now().isoformat(),
            'risk_classification': risk_assessment['overall_risk'],
            'regulatory_alerts': [],
            'investigation_summary': "",
            'recommended_actions': [],
            'evidence_strength': "Strong",
            'model_confidence': feature_importance['original_prediction'],
            'regulatory_compliance': {
                'fatf_guidance': True,
                'kyc_requirements': True,
                'suspicious_activity_threshold': risk_assessment['overall_risk']['score'] > 0.5
            }
        }
        
        # Generate regulatory alerts
        if risk_assessment['overall_risk']['score'] > 0.7:
            report['regulatory_alerts'].append("HIGH RISK: Potential money laundering activity detected")
        
        if risk_assessment['temporal_analysis']['motif_count'] > 2:
            report['regulatory_alerts'].append("TEMPORAL PATTERN: Suspicious transaction sequencing identified")
        
        # Investigation summary
        top_factors = [f['factor'] for f in risk_assessment['risk_factors'][:3]]
        report['investigation_summary'] = (
            f"Transaction {target_node} flagged as {risk_assessment['overall_risk']['level']} risk "
            f"based on {len(top_factors)} primary risk factors: {', '.join(top_factors)}"
        )
        
        # Recommended actions
        risk_level = risk_assessment['overall_risk']['level']
        if risk_level == "CRITICAL":
            report['recommended_actions'] = [
                "IMMEDIATE: Freeze transaction pending investigation",
                "URGENT: Contact compliance officer",
                "REQUIRED: File Suspicious Activity Report (SAR)",
                "ESCALATE: Senior management notification"
            ]
        elif risk_level == "HIGH":
            report['recommended_actions'] = [
                "PRIORITY: Enhanced due diligence required",
                "MONITOR: Increase transaction monitoring frequency",
                "DOCUMENT: Record investigation details",
                "REVIEW: Consider SAR filing"
            ]
        else:
            report['recommended_actions'] = [
                "STANDARD: Continue routine monitoring",
                "DOCUMENT: Log explanation for audit trail"
            ]
        
        return report
    
    def _calculate_explanation_quality(self, motifs: List[Dict], 
                                     bottleneck_result: Dict) -> float:
        """Calculate overall explanation quality score."""
        
        # Motif quality (0-1 scale)
        motif_quality = min(1.0, len(motifs) / 5.0)  # Normalize by expected max motifs
        
        # Information efficiency (0-1 scale)
        info_loss = bottleneck_result.get('info_loss', 1.0)
        info_quality = 1.0 / (1.0 + info_loss)  # Higher loss = lower quality
        
        # Combined quality score
        overall_quality = (motif_quality + info_quality) / 2.0
        
        return overall_quality

class EnhancedVisualization:
    """Enhanced visualization tools for AML explanations."""
    
    def __init__(self):
        plt.style.use('seaborn-v0_8')
    
    def create_explanation_dashboard(self, explanation: Dict, save_path: str = None):
        """Create comprehensive explanation dashboard."""
        
        fig = plt.figure(figsize=(20, 16))
        
        # 1. Risk Assessment Summary
        ax1 = plt.subplot(3, 3, 1)
        self._plot_risk_gauge(explanation['risk_assessment'], ax1)
        
        # 2. Feature Importance
        ax2 = plt.subplot(3, 3, 2)
        self._plot_feature_importance(explanation['feature_importance'], ax2)
        
        # 3. Temporal Motifs
        ax3 = plt.subplot(3, 3, 3)
        self._plot_temporal_motifs(explanation['temporal_motifs'], ax3)
        
        # 4. Information Bottleneck
        ax4 = plt.subplot(3, 3, 4)
        self._plot_information_bottleneck(explanation['information_bottleneck'], ax4)
        
        # 5. Compliance Summary
        ax5 = plt.subplot(3, 3, (5, 6))
        self._plot_compliance_summary(explanation['compliance_report'], ax5)
        
        # 6. Risk Timeline
        ax6 = plt.subplot(3, 3, (7, 9))
        self._plot_risk_timeline(explanation, ax6)
        
        plt.suptitle(f"AML Transaction Analysis - {explanation['node_id']}", 
                    fontsize=16, fontweight='bold')
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            logger.info(f"Explanation dashboard saved to {save_path}")
        
        plt.show()
    
    def _plot_risk_gauge(self, risk_assessment: Dict, ax):
        """Plot risk level gauge."""
        risk_score = risk_assessment['overall_risk']['score']
        
        # Create gauge
        theta = np.linspace(0, np.pi, 100)
        r = np.ones_like(theta)
        
        ax.plot(theta, r, 'k-', linewidth=2)
        ax.fill_between(theta[:33], 0, r[:33], color='green', alpha=0.3, label='Low')
        ax.fill_between(theta[33:66], 0, r[33:66], color='yellow', alpha=0.3, label='Medium') 
        ax.fill_between(theta[66:], 0, r[66:], color='red', alpha=0.3, label='High')
        
        # Risk pointer
        risk_angle = risk_score * np.pi
        ax.arrow(0, 0, 0.8*np.cos(risk_angle), 0.8*np.sin(risk_angle), 
                head_width=0.1, head_length=0.1, fc='black', ec='black')
        
        ax.set_title(f"Risk Level: {risk_assessment['overall_risk']['level']}")
        ax.set_xlim(-0.1, np.pi + 0.1)
        ax.set_ylim(-0.1, 1.1)
        ax.axis('off')
    
    def _plot_feature_importance(self, feature_importance: Dict, ax):
        """Plot top feature importance."""
        top_features = feature_importance['top_features'][:8]
        
        features = [f['feature_name'] for f in top_features]
        importance = [f['importance_score'] for f in top_features]
        
        bars = ax.barh(range(len(features)), importance, 
                      color=['red' if imp > 0.3 else 'orange' if imp > 0.1 else 'lightblue' 
                            for imp in importance])
        
        ax.set_yticks(range(len(features)))
        ax.set_yticklabels(features, fontsize=8)
        ax.set_xlabel('Importance Score')
        ax.set_title('Top Risk Factors')
        ax.grid(axis='x', alpha=0.3)
    
    def _plot_temporal_motifs(self, motifs: List[Dict], ax):
        """Plot temporal motif analysis."""
        if not motifs:
            ax.text(0.5, 0.5, 'No motifs detected', ha='center', va='center', transform=ax.transAxes)
            ax.set_title('Temporal Motifs')
            return
        
        motif_sizes = [len(motif['nodes']) for motif in motifs]
        importance_scores = [motif.get('importance_score', 0) for motif in motifs]
        
        scatter = ax.scatter(motif_sizes, importance_scores, 
                           c=importance_scores, cmap='Reds', s=100, alpha=0.7)
        
        ax.set_xlabel('Motif Size (nodes)')
        ax.set_ylabel('Importance Score')
        ax.set_title('Temporal Motif Analysis')
        plt.colorbar(scatter, ax=ax, label='Importance')
    
    def _plot_information_bottleneck(self, bottleneck_result: Dict, ax):
        """Plot information bottleneck analysis."""
        if not bottleneck_result:
            ax.text(0.5, 0.5, 'No bottleneck analysis', ha='center', va='center', transform=ax.transAxes)
            ax.set_title('Information Bottleneck')
            return
        
        metrics = ['Prediction Loss', 'Information Loss', 'Total Loss']
        values = [
            bottleneck_result.get('prediction_loss', 0),
            bottleneck_result.get('info_loss', 0),
            bottleneck_result.get('total_loss', 0)
        ]
        
        bars = ax.bar(metrics, values, color=['blue', 'green', 'purple'], alpha=0.7)
        ax.set_ylabel('Loss Value')
        ax.set_title('Information Bottleneck Analysis')
        plt.setp(ax.get_xticklabels(), rotation=45, ha='right')
    
    def _plot_compliance_summary(self, compliance_report: Dict, ax):
        """Plot compliance summary."""
        ax.axis('off')
        
        # Title
        ax.text(0.5, 0.9, 'Compliance Summary', ha='center', va='top', 
               fontsize=14, fontweight='bold', transform=ax.transAxes)
        
        # Risk classification
        risk_info = compliance_report['risk_classification']
        ax.text(0.1, 0.7, f"Risk Level: {risk_info['level']}", 
               fontsize=12, transform=ax.transAxes)
        ax.text(0.1, 0.6, f"Risk Score: {risk_info['score']:.3f}", 
               fontsize=12, transform=ax.transAxes)
        
        # Alerts
        if compliance_report['regulatory_alerts']:
            ax.text(0.1, 0.4, 'Regulatory Alerts:', fontsize=12, fontweight='bold',
                   transform=ax.transAxes)
            for i, alert in enumerate(compliance_report['regulatory_alerts'][:3]):
                ax.text(0.1, 0.3 - i*0.08, f"• {alert}", fontsize=10,
                       transform=ax.transAxes, wrap=True)
    
    def _plot_risk_timeline(self, explanation: Dict, ax):
        """Plot risk analysis timeline."""
        ax.text(0.5, 0.9, 'Investigation Timeline & Actions', ha='center', va='top',
               fontsize=14, fontweight='bold', transform=ax.transAxes)
        
        actions = explanation['compliance_report']['recommended_actions']
        
        y_positions = np.linspace(0.7, 0.1, len(actions))
        
        for i, action in enumerate(actions):
            # Priority indicator
            if 'IMMEDIATE' in action or 'CRITICAL' in action:
                color = 'red'
                marker = '●'
            elif 'URGENT' in action or 'PRIORITY' in action:
                color = 'orange' 
                marker = '●'
            else:
                color = 'green'
                marker = '●'
            
            ax.text(0.05, y_positions[i], marker, color=color, fontsize=16,
                   transform=ax.transAxes)
            ax.text(0.1, y_positions[i], action, fontsize=10,
                   transform=ax.transAxes, va='center')
        
        ax.axis('off')

def demo_enhanced_explainer():
    """Demonstrate enhanced GNN explainer with sample data."""
    
    print("🚀 Enhanced GNN Explainer Demo for AML Detection")
    
    # Create sample data
    num_nodes = 50
    num_features = 64
    
    x = torch.randn(num_nodes, num_features)
    edge_index = torch.randint(0, num_nodes, (2, 100))
    edge_attr = torch.randn(100, 8)
    edge_time = torch.randint(1, 100, (100,))
    
    # Mock TGAT model
    class MockTGATModel(nn.Module):
        def __init__(self):
            super().__init__()
            self.linear = nn.Linear(num_features, 2)
            
        def forward(self, x, edge_index, edge_attr, edge_time):
            return self.linear(x)
    
    model = MockTGATModel()
    
    # Initialize enhanced explainer
    explainer = AdvancedAMLExplainer(model)
    visualizer = EnhancedVisualization()
    
    # Generate explanation for suspicious node
    target_node = 5
    
    print(f"\n🔍 Generating comprehensive explanation for node {target_node}...")
    explanation = explainer.generate_comprehensive_explanation(
        x, edge_index, edge_attr, edge_time, target_node
    )
    
    # Display results
    print(f"\n📊 Explanation Results:")
    print(f"   Risk Level: {explanation['risk_assessment']['overall_risk']['level']}")
    print(f"   Risk Score: {explanation['risk_assessment']['overall_risk']['score']:.3f}")
    print(f"   Temporal Motifs: {len(explanation['temporal_motifs'])}")
    print(f"   Top Risk Factors: {len(explanation['risk_assessment']['risk_factors'])}")
    print(f"   Explanation Quality: {explanation['explanation_quality']:.3f}")
    
    # Create visualization
    print(f"\n📈 Creating explanation dashboard...")
    try:
        visualizer.create_explanation_dashboard(explanation, 'aml_explanation_dashboard.png')
        print(f"   Dashboard saved successfully!")
    except Exception as e:
        print(f"   Dashboard creation failed: {e}")
    
    # Save detailed report
    report_file = f"enhanced_explanation_report_{target_node}.json"
    with open(report_file, 'w') as f:
        json.dump(explanation, f, indent=2, default=str)
    
    print(f"\n✅ Enhanced explanation demo complete!")
    print(f"   📄 Detailed report saved: {report_file}")
    print(f"   🎯 Node analyzed: {target_node}")
    print(f"   🔬 Techniques used: Temporal Motifs, Information Bottleneck, Feature Analysis")
    
    return explanation

if __name__ == "__main__":
    demo_enhanced_explainer() 