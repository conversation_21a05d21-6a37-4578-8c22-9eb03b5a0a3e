"""
GNNExplainer Layer for TGAT-based AML Detection

This module provides interpretability for TGAT model predictions by implementing
GNNExplainer techniques to understand why transactions are flagged as illicit.
"""

import numpy as np
import torch
import torch.nn as nn
import torch.optim as optim
import torch.nn.functional as F
from typing import Dict, List, Tuple, Optional
import matplotlib.pyplot as plt
import networkx as nx
import pandas as pd
import logging
from working_tgat_elliptic import WorkingTGATModel
import json
import os

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class GNNExplainer:
    """
    GNNExplainer for temporal graph neural networks.
    
    This class provides explanations for TGAT model predictions by:
    1. Learning edge masks to identify important connections
    2. Learning feature masks to identify important node features  
    3. Generating subgraph explanations for predictions
    """
    
    def __init__(self, model: nn.Module, num_hops: int = 2, device: str = 'cpu'):
        self.model = model
        self.num_hops = num_hops
        self.device = device
        self.model.eval()
        
        # Explainer parameters
        self.edge_mask = None
        self.feature_mask = None
        self.optimizer = None
        
    def _get_subgraph(self, node_idx: int, edge_index: torch.Tensor, 
                     num_hops: int) -> Tuple[torch.Tensor, torch.Tensor]:
        """Extract k-hop subgraph around a target node."""
        
        # Start with target node
        subset = torch.tensor([node_idx], dtype=torch.long)
        
        for _ in range(num_hops):
            # Find edges connected to current subset
            edge_mask = torch.isin(edge_index[0], subset) | torch.isin(edge_index[1], subset)
            
            if edge_mask.sum() == 0:
                break
                
            # Get all nodes in these edges
            connected_edges = edge_index[:, edge_mask]
            new_nodes = torch.cat([connected_edges[0], connected_edges[1]]).unique()
            
            # Add new nodes to subset
            subset = torch.cat([subset, new_nodes]).unique()
        
        # Get edges within this subgraph
        subgraph_edge_mask = torch.isin(edge_index[0], subset) & torch.isin(edge_index[1], subset)
        subgraph_edges = edge_index[:, subgraph_edge_mask]
        
        return subset, subgraph_edges
    
    def explain_node(self, node_idx: int, x: torch.Tensor, edge_index: torch.Tensor,
                    edge_attr: torch.Tensor, edge_time: torch.Tensor,
                    epochs: int = 200, lr: float = 0.01) -> Dict:
        """
        Generate explanation for a specific node's prediction.
        
        Returns:
            Dictionary containing edge importance, feature importance, and explanation scores
        """
        
        logger.info(f"Generating explanation for node {node_idx}...")
        
        # Get subgraph around target node
        subset, subgraph_edges = self._get_subgraph(node_idx, edge_index, self.num_hops)
        
        if len(subgraph_edges[0]) == 0:
            logger.warning(f"No edges found for node {node_idx}")
            return {'edge_importance': [], 'feature_importance': [], 'prediction_score': 0.0}
        
        # Initialize learnable masks
        num_edges = len(subgraph_edges[0])
        num_features = x.size(1)
        
        self.edge_mask = nn.Parameter(torch.randn(num_edges, requires_grad=True, device=self.device))
        self.feature_mask = nn.Parameter(torch.randn(num_features, requires_grad=True, device=self.device))
        
        self.optimizer = optim.Adam([self.edge_mask, self.feature_mask], lr=lr)
        
        # Get original prediction for target node
        with torch.no_grad():
            original_pred = self.model(x, edge_index, edge_attr, edge_time)
            original_score = F.softmax(original_pred[node_idx], dim=0)[1].item()  # Illicit probability
        
        # Training loop to learn masks
        for epoch in range(epochs):
            self.optimizer.zero_grad()
            
            # Apply masks
            masked_x = x * torch.sigmoid(self.feature_mask).unsqueeze(0)
            masked_edge_attr = edge_attr.clone()
            
            # Create edge mask for the subgraph edges
            edge_weights = torch.sigmoid(self.edge_mask)
            
            # Apply edge masking by modifying edge attributes
            subgraph_edge_indices = []
            for i, (src, dst) in enumerate(subgraph_edges.t()):
                # Find this edge in the original edge_index
                edge_mask = (edge_index[0] == src) & (edge_index[1] == dst)
                edge_pos = torch.where(edge_mask)[0]
                if len(edge_pos) > 0:
                    subgraph_edge_indices.append(edge_pos[0].item())
            
            if len(subgraph_edge_indices) > 0:
                for i, edge_idx in enumerate(subgraph_edge_indices):
                    if i < len(edge_weights):
                        masked_edge_attr[edge_idx] = masked_edge_attr[edge_idx] * edge_weights[i]
            
            # Forward pass with masked inputs
            masked_pred = self.model(masked_x, edge_index, masked_edge_attr, edge_time)
            masked_score = F.softmax(masked_pred[node_idx], dim=0)[1]  # Illicit probability
            
            # Loss: maximize difference from original prediction while keeping masks sparse
            prediction_loss = -torch.abs(masked_score - original_score)
            sparsity_loss = 0.1 * (torch.norm(self.edge_mask, p=1) + torch.norm(self.feature_mask, p=1))
            
            loss = prediction_loss + sparsity_loss
            loss.backward()
            self.optimizer.step()
            
            if epoch % 50 == 0:
                logger.debug(f"Epoch {epoch}: Loss = {loss.item():.4f}, Masked Score = {masked_score.item():.4f}")
        
        # Extract learned importance scores
        edge_importance = torch.sigmoid(self.edge_mask).detach().cpu().numpy()
        feature_importance = torch.sigmoid(self.feature_mask).detach().cpu().numpy()
        
        explanation = {
            'node_idx': node_idx,
            'original_prediction': original_score,
            'edge_importance': edge_importance.tolist(),
            'feature_importance': feature_importance.tolist(),
            'subgraph_nodes': subset.cpu().numpy().tolist(),
            'subgraph_edges': subgraph_edges.cpu().numpy().tolist(),
            'explanation_score': abs(original_score - 0.5) * 2  # Confidence score
        }
        
        logger.info(f"Explanation complete. Original prediction: {original_score:.4f}")
        return explanation
    
    def explain_prediction_batch(self, suspicious_nodes: List[int], 
                               x: torch.Tensor, edge_index: torch.Tensor,
                               edge_attr: torch.Tensor, edge_time: torch.Tensor) -> List[Dict]:
        """Generate explanations for multiple suspicious nodes."""
        
        explanations = []
        for node_idx in suspicious_nodes:
            try:
                explanation = self.explain_node(node_idx, x, edge_index, edge_attr, edge_time)
                explanations.append(explanation)
            except Exception as e:
                logger.warning(f"Failed to explain node {node_idx}: {e}")
                explanations.append({
                    'node_idx': node_idx,
                    'error': str(e),
                    'explanation_score': 0.0
                })
        
        return explanations

class AMLExplanationGenerator:
    """
    High-level explanation generator for AML detection results.
    
    This class provides human-readable explanations for why transactions
    are flagged as potentially illicit.
    """
    
    def __init__(self, explainer: GNNExplainer):
        self.explainer = explainer
        self.feature_names = self._get_feature_names()
        
    def _get_feature_names(self) -> List[str]:
        """Generate meaningful feature names for explanations."""
        # In a real system, these would map to actual transaction features
        base_features = [
            'transaction_amount', 'transaction_frequency', 'account_age', 'network_centrality',
            'temporal_pattern', 'counterparty_risk', 'geographic_risk', 'transaction_velocity',
            'mixing_pattern', 'clustering_coefficient', 'betweenness_centrality', 'degree_centrality'
        ]
        
        # Extend to 64 features with derived features
        feature_names = base_features.copy()
        for i in range(len(base_features), 64):
            base_idx = i % len(base_features)
            feature_names.append(f"{base_features[base_idx]}_derived_{i}")
        
        return feature_names
    
    def generate_human_readable_explanation(self, explanation: Dict) -> Dict[str, str]:
        """Convert technical explanation to human-readable format."""
        
        node_idx = explanation['node_idx']
        prediction_score = explanation['original_prediction']
        feature_importance = explanation['feature_importance']
        
        # Risk level assessment
        if prediction_score > 0.8:
            risk_level = "HIGH RISK"
            risk_color = "🔴"
        elif prediction_score > 0.5:
            risk_level = "MEDIUM RISK"
            risk_color = "🟡"
        else:
            risk_level = "LOW RISK"
            risk_color = "🟢"
        
        # Top risk factors
        top_feature_indices = np.argsort(feature_importance)[-5:][::-1]
        top_features = [(self.feature_names[i], feature_importance[i]) for i in top_feature_indices]
        
        risk_factors = []
        for feature_name, importance in top_features:
            if importance > 0.7:
                risk_factors.append(f"• **{feature_name}** (Impact: {importance:.2f})")
            elif importance > 0.5:
                risk_factors.append(f"• {feature_name} (Impact: {importance:.2f})")
        
        # Network analysis
        subgraph_size = len(explanation.get('subgraph_nodes', []))
        network_risk = "High network connectivity" if subgraph_size > 10 else "Limited network connections"
        
        human_explanation = {
            'transaction_id': f"TX_{node_idx}",
            'risk_assessment': f"{risk_color} {risk_level}",
            'confidence_score': f"{prediction_score:.1%}",
            'primary_risk_factors': '\n'.join(risk_factors[:3]) if risk_factors else "No significant risk factors identified",
            'network_analysis': f"{network_risk} ({subgraph_size} connected nodes)",
            'recommendation': self._get_recommendation(prediction_score),
            'explanation_quality': f"{explanation.get('explanation_score', 0):.2f}/1.00"
        }
        
        return human_explanation
    
    def _get_recommendation(self, prediction_score: float) -> str:
        """Generate actionable recommendations based on risk score."""
        
        if prediction_score > 0.8:
            return "🚨 IMMEDIATE ACTION: Flag for manual review and potential blocking. Contact compliance team."
        elif prediction_score > 0.6:
            return "⚠️ ENHANCED MONITORING: Increase transaction monitoring frequency. Request additional documentation."
        elif prediction_score > 0.4:
            return "👀 ROUTINE MONITORING: Continue standard monitoring procedures."
        else:
            return "✅ STANDARD PROCESSING: No additional action required."

class ExplanationVisualizer:
    """Visualization tools for GNN explanations."""
    
    def __init__(self):
        pass
    
    def plot_feature_importance(self, explanation: Dict, save_path: str = None):
        """Plot feature importance for a node explanation."""
        
        feature_names = [f"F{i}" for i in range(len(explanation['feature_importance']))]
        importance_scores = explanation['feature_importance']
        
        # Get top 15 features for readability
        top_indices = np.argsort(importance_scores)[-15:]
        top_features = [feature_names[i] for i in top_indices]
        top_scores = [importance_scores[i] for i in top_indices]
        
        plt.figure(figsize=(10, 8))
        bars = plt.barh(range(len(top_features)), top_scores, 
                       color=['red' if score > 0.7 else 'orange' if score > 0.5 else 'lightblue' 
                             for score in top_scores])
        
        plt.yticks(range(len(top_features)), top_features)
        plt.xlabel('Feature Importance Score')
        plt.title(f'Feature Importance for Node {explanation["node_idx"]}\n'
                 f'Prediction Score: {explanation["original_prediction"]:.3f}')
        plt.grid(axis='x', alpha=0.3)
        
        # Add score labels on bars
        for i, (bar, score) in enumerate(zip(bars, top_scores)):
            plt.text(score + 0.01, i, f'{score:.3f}', va='center', fontsize=8)
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            logger.info(f"Feature importance plot saved to {save_path}")
        
        plt.show()
    
    def save_explanation_report(self, explanations: List[Dict], 
                              human_explanations: List[Dict],
                              output_file: str = "aml_explanation_report.json"):
        """Save explanations to a structured report file."""
        
        report = {
            'timestamp': pd.Timestamp.now().isoformat(),
            'total_explanations': len(explanations),
            'summary_statistics': {
                'avg_prediction_score': np.mean([exp['original_prediction'] for exp in explanations]),
                'high_risk_count': sum(1 for exp in explanations if exp['original_prediction'] > 0.7),
                'medium_risk_count': sum(1 for exp in explanations if 0.4 <= exp['original_prediction'] <= 0.7),
                'low_risk_count': sum(1 for exp in explanations if exp['original_prediction'] < 0.4)
            },
            'explanations': explanations,
            'human_readable_explanations': human_explanations
        }
        
        with open(output_file, 'w') as f:
            json.dump(report, f, indent=2)
        
        logger.info(f"Explanation report saved to {output_file}")
        return report

def demo_gnn_explainer():
    """Demonstrate GNNExplainer functionality with sample data."""
    
    print("🔍 GNNExplainer Demo for AML Detection")
    
    # Create sample data
    num_nodes = 100
    num_features = 64
    
    x = torch.randn(num_nodes, num_features)
    edge_index = torch.randint(0, num_nodes, (2, 200))
    edge_attr = torch.randn(200, 8)
    edge_time = torch.randint(1, 50, (200,))
    
    # Create a mock trained model
    model = WorkingTGATModel(input_dim=num_features, hidden_dim=64, edge_dim=8, num_classes=2)
    
    # Initialize explainer
    explainer = GNNExplainer(model, num_hops=2)
    explanation_generator = AMLExplanationGenerator(explainer)
    visualizer = ExplanationVisualizer()
    
    # Generate predictions to find suspicious nodes
    with torch.no_grad():
        predictions = model(x, edge_index, edge_attr, edge_time)
        probs = F.softmax(predictions, dim=1)[:, 1]  # Illicit probabilities
        
    # Find top suspicious nodes
    suspicious_nodes = torch.topk(probs, k=5).indices.tolist()
    
    print(f"\n📊 Found {len(suspicious_nodes)} suspicious transactions:")
    for i, node_idx in enumerate(suspicious_nodes):
        print(f"   {i+1}. Node {node_idx}: Risk Score {probs[node_idx]:.3f}")
    
    # Generate explanations
    print(f"\n🔍 Generating explanations...")
    explanations = explainer.explain_prediction_batch(
        suspicious_nodes[:3], x, edge_index, edge_attr, edge_time
    )
    
    # Generate human-readable explanations
    human_explanations = []
    for explanation in explanations:
        if 'error' not in explanation:
            human_exp = explanation_generator.generate_human_readable_explanation(explanation)
            human_explanations.append(human_exp)
            
            print(f"\n🎯 Explanation for {human_exp['transaction_id']}:")
            print(f"   Risk Assessment: {human_exp['risk_assessment']}")
            print(f"   Confidence: {human_exp['confidence_score']}")
            print(f"   Key Risk Factors:\n{human_exp['primary_risk_factors']}")
            print(f"   Recommendation: {human_exp['recommendation']}")
    
    # Save report
    report = visualizer.save_explanation_report(explanations, human_explanations)
    
    print(f"\n✅ GNNExplainer demo complete!")
    print(f"   📄 Report saved with {len(explanations)} explanations")
    print(f"   📊 High-risk transactions: {report['summary_statistics']['high_risk_count']}")
    
    return explanations, human_explanations

if __name__ == "__main__":
    demo_gnn_explainer() 