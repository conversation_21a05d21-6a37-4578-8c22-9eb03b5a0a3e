#!/bin/bash

# Context7-Enhanced Graph Construction Layer Deployment Script
# This script deploys the Graph Construction Layer with Context7 optimizations
# from Apache Spark Structured Streaming and PyTorch Geometric documentation

set -e

echo "🚀 Context7-Enhanced Graph Construction Layer Deployment"
echo "================================================="
echo "Target Performance: <200ms latency per graph snapshot"
echo "Context7 Optimizations: Apache Spark + PyTorch Geometric"
echo ""

# Color codes for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Configuration
PROJECT_ROOT=$(pwd)
GRAPH_LAYER_DIR="$PROJECT_ROOT/graph_layer"
LOGS_DIR="$PROJECT_ROOT/logs"
CONTEXT7_LOG_FILE="$LOGS_DIR/context7_deployment.log"

# Context7 Performance Targets
TARGET_LATENCY_MS=200
TARGET_THROUGHPUT_TPS=2000
EXPECTED_MEMORY_EFFICIENCY=85

# Create logs directory
mkdir -p "$LOGS_DIR"

# Logging function
log() {
    local level=$1
    shift
    local message="$@"
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    echo -e "[$timestamp] [$level] $message" | tee -a "$CONTEXT7_LOG_FILE"
}

# Check prerequisites
check_prerequisites() {
    log "INFO" "${CYAN}Checking Context7 prerequisites...${NC}"
    
    # Check Docker
    if ! command -v docker &> /dev/null; then
        log "ERROR" "${RED}Docker is required but not installed${NC}"
        exit 1
    fi
    
    # Check Docker Compose
    if ! command -v docker-compose &> /dev/null; then
        log "ERROR" "${RED}Docker Compose is required but not installed${NC}"
        exit 1
    fi
    
    # Check Python
    if ! command -v python3 &> /dev/null; then
        log "ERROR" "${RED}Python 3 is required but not installed${NC}"
        exit 1
    fi
    
    # Check available memory (minimum 16GB recommended for Context7 optimizations)
    if [[ "$OSTYPE" == "linux-gnu"* ]]; then
        total_mem=$(grep MemTotal /proc/meminfo | awk '{print $2}')
        total_mem_gb=$((total_mem / 1024 / 1024))
        if [ "$total_mem_gb" -lt 16 ]; then
            log "WARN" "${YELLOW}Available memory: ${total_mem_gb}GB. Context7 optimizations recommend 16GB+${NC}"
        else
            log "INFO" "${GREEN}Memory check passed: ${total_mem_gb}GB available${NC}"
        fi
    fi
    
    log "INFO" "${GREEN}Prerequisites check completed${NC}"
}

# Validate Context7 configuration
validate_context7_config() {
    log "INFO" "${CYAN}Validating Context7 configuration...${NC}"
    
    # Check if Context7 config files exist
    if [ ! -f "$GRAPH_LAYER_DIR/config/settings.py" ]; then
        log "ERROR" "${RED}Context7 configuration file not found${NC}"
        exit 1
    fi
    
    # Validate Context7 optimizations are enabled
    if grep -q "enable_torch_compile.*True" "$GRAPH_LAYER_DIR/config/settings.py"; then
        log "INFO" "${GREEN}✓ torch.compile optimization enabled${NC}"
    else
        log "WARN" "${YELLOW}torch.compile optimization not enabled${NC}"
    fi
    
    if grep -q "enable_async_progress_tracking.*True" "$GRAPH_LAYER_DIR/config/settings.py"; then
        log "INFO" "${GREEN}✓ Asynchronous progress tracking enabled${NC}"
    else
        log "WARN" "${YELLOW}Asynchronous progress tracking not enabled${NC}"
    fi
    
    if grep -q "enable_sparse_tensor_operations.*True" "$GRAPH_LAYER_DIR/config/settings.py"; then
        log "INFO" "${GREEN}✓ Sparse tensor operations enabled${NC}"
    else
        log "WARN" "${YELLOW}Sparse tensor operations not enabled${NC}"
    fi
    
    log "INFO" "${GREEN}Context7 configuration validation completed${NC}"
}

# Build Context7-optimized Docker image
build_context7_image() {
    log "INFO" "${CYAN}Building Context7-optimized Docker image...${NC}"
    
    cd "$GRAPH_LAYER_DIR"
    
    # Build the optimized image
    if docker build -f Dockerfile.context7 -t graph-layer:context7-optimized . >> "$CONTEXT7_LOG_FILE" 2>&1; then
        log "INFO" "${GREEN}✓ Context7-optimized Docker image built successfully${NC}"
    else
        log "ERROR" "${RED}Failed to build Context7-optimized Docker image${NC}"
        exit 1
    fi
    
    cd "$PROJECT_ROOT"
}

# Start Context7-enhanced infrastructure
start_context7_infrastructure() {
    log "INFO" "${CYAN}Starting Context7-enhanced infrastructure...${NC}"
    
    # Check if pipeline is already running
    if docker-compose -f docker-compose-integrated-pipeline.yml ps | grep -q "Up"; then
        log "INFO" "${GREEN}Infrastructure already running${NC}"
    else
        log "INFO" "Starting integrated pipeline..."
        docker-compose -f docker-compose-integrated-pipeline.yml up -d >> "$CONTEXT7_LOG_FILE" 2>&1
        
        # Wait for services to be ready
        log "INFO" "Waiting for services to initialize..."
        sleep 30
    fi
    
    # Verify services
    verify_services
}

# Verify services are running
verify_services() {
    log "INFO" "${CYAN}Verifying Context7 services...${NC}"
    
    services=("kafka" "zookeeper" "flink-jobmanager" "flink-taskmanager")
    
    for service in "${services[@]}"; do
        if docker-compose -f docker-compose-integrated-pipeline.yml ps "$service" | grep -q "Up"; then
            log "INFO" "${GREEN}✓ $service is running${NC}"
        else
            log "ERROR" "${RED}✗ $service is not running${NC}"
            exit 1
        fi
    done
    
    # Check Kafka topics
    log "INFO" "Checking Kafka topics..."
    sleep 5
    
    # Verify filtered-transactions topic exists
    if docker exec $(docker-compose -f docker-compose-integrated-pipeline.yml ps -q kafka) kafka-topics.sh --bootstrap-server localhost:9092 --list | grep -q "filtered-transactions"; then
        log "INFO" "${GREEN}✓ filtered-transactions topic exists${NC}"
    else
        log "WARN" "${YELLOW}filtered-transactions topic not found${NC}"
    fi
}

# Deploy Context7 Graph Layer
deploy_context7_graph_layer() {
    log "INFO" "${CYAN}Deploying Context7 Graph Construction Layer...${NC}"
    
    # Create Context7-specific environment variables
    cat > .env.context7 << EOF
# Context7 Graph Layer Configuration
SPARK_APP_NAME=GraphConstructionLayer-Context7-Optimized
SPARK_EXECUTOR_MEMORY=6g
SPARK_DRIVER_MEMORY=3g
SPARK_EXECUTOR_CORES=6
KAFKA_CONSUMER_GROUP=graph-construction-consumer-context7
TRIGGER_PROCESSING_TIME=5 seconds
MAX_OFFSETS_PER_TRIGGER=8000
NODE_FEATURE_DIM=64
MAX_NODES_PER_GRAPH=8000
ENABLE_TORCH_COMPILE=true
ENABLE_ASYNC_PROGRESS_TRACKING=true
ENABLE_SPARSE_TENSOR_OPERATIONS=true
ENABLE_MEMORY_EFFICIENT_ATTENTION=true
LATENCY_TARGET_MS=200
CHECKPOINT_LOCATION=/tmp/graph-layer-checkpoints-context7
EOF

    # Start Context7 Graph Layer with enhanced configuration
    log "INFO" "Starting Context7 Graph Layer container..."
    
    docker run -d \
        --name graph-layer-context7 \
        --network $(docker-compose -f docker-compose-integrated-pipeline.yml config --services | head -1)_default \
        --env-file .env.context7 \
        -e KAFKA_BOOTSTRAP_SERVERS=kafka:29092 \
        -e KAFKA_INPUT_TOPIC=filtered-transactions \
        -e KAFKA_OUTPUT_TOPIC=graph-snapshots \
        -v "$PROJECT_ROOT/logs:/app/logs" \
        -p 4040:4040 \
        -p 8085:8085 \
        graph-layer:context7-optimized \
        >> "$CONTEXT7_LOG_FILE" 2>&1
    
    if [ $? -eq 0 ]; then
        log "INFO" "${GREEN}✓ Context7 Graph Layer deployed successfully${NC}"
    else
        log "ERROR" "${RED}Failed to deploy Context7 Graph Layer${NC}"
        exit 1
    fi
    
    # Wait for initialization
    log "INFO" "Waiting for Context7 Graph Layer initialization..."
    sleep 20
}

# Monitor Context7 performance
monitor_context7_performance() {
    log "INFO" "${CYAN}Monitoring Context7 performance...${NC}"
    
    # Check if container is running
    if ! docker ps | grep -q "graph-layer-context7"; then
        log "ERROR" "${RED}Context7 Graph Layer container is not running${NC}"
        return 1
    fi
    
    # Monitor for 60 seconds
    log "INFO" "Monitoring performance for 60 seconds..."
    
    for i in {1..12}; do
        # Get container stats
        stats=$(docker stats graph-layer-context7 --no-stream --format "table {{.CPUPerc}}\t{{.MemUsage}}")
        log "INFO" "Performance check $i/12: $stats"
        
        # Check logs for performance metrics
        if docker logs graph-layer-context7 --tail 10 2>/dev/null | grep -q "construction_time_ms"; then
            latest_timing=$(docker logs graph-layer-context7 --tail 50 2>/dev/null | grep "construction_time_ms" | tail -1)
            if [ ! -z "$latest_timing" ]; then
                log "INFO" "${GREEN}Latest timing: $latest_timing${NC}"
            fi
        fi
        
        sleep 5
    done
}

# Validate Context7 performance targets
validate_performance_targets() {
    log "INFO" "${CYAN}Validating Context7 performance targets...${NC}"
    
    # Extract recent performance metrics from logs
    if docker logs graph-layer-context7 --tail 100 2>/dev/null | grep -q "construction_time_ms"; then
        # Get average construction time from recent logs
        avg_time=$(docker logs graph-layer-context7 --tail 100 2>/dev/null | \
                  grep "construction_time_ms" | \
                  grep -o 'construction_time_ms":[0-9.]*' | \
                  grep -o '[0-9.]*' | \
                  awk '{sum+=$1; count++} END {if(count>0) print sum/count; else print 0}')
        
        if [ ! -z "$avg_time" ] && [ $(echo "$avg_time > 0" | bc -l) -eq 1 ]; then
            log "INFO" "${GREEN}Average construction time: ${avg_time}ms${NC}"
            
            if [ $(echo "$avg_time < $TARGET_LATENCY_MS" | bc -l) -eq 1 ]; then
                log "INFO" "${GREEN}✓ Performance target achieved: ${avg_time}ms < ${TARGET_LATENCY_MS}ms${NC}"
                return 0
            else
                log "WARN" "${YELLOW}Performance target missed: ${avg_time}ms >= ${TARGET_LATENCY_MS}ms${NC}"
                return 1
            fi
        else
            log "WARN" "${YELLOW}No valid performance metrics found yet${NC}"
            return 1
        fi
    else
        log "WARN" "${YELLOW}No performance metrics found in logs${NC}"
        return 1
    fi
}

# Generate Context7 performance report
generate_performance_report() {
    log "INFO" "${CYAN}Generating Context7 performance report...${NC}"
    
    report_file="$LOGS_DIR/context7_performance_report.md"
    
    cat > "$report_file" << EOF
# Context7 Graph Construction Layer - Performance Report

**Generated**: $(date)
**Target Latency**: ${TARGET_LATENCY_MS}ms
**Target Throughput**: ${TARGET_THROUGHPUT_TPS} tx/s

## Context7 Optimizations Deployed

### Apache Spark Optimizations
- ✅ Asynchronous Progress Tracking
- ✅ Enhanced Kafka Producer/Consumer Settings
- ✅ RocksDB State Store Optimizations
- ✅ Dynamic Resource Allocation
- ✅ Advanced Memory Management

### PyTorch Geometric Optimizations  
- ✅ torch.compile Integration
- ✅ Sparse Tensor Operations
- ✅ Memory-Efficient Attention
- ✅ Vectorized Graph Construction
- ✅ Enhanced Feature Caching

## Performance Metrics

EOF

    # Add performance metrics if available
    if docker logs graph-layer-context7 --tail 100 2>/dev/null | grep -q "construction_time_ms"; then
        echo "### Graph Construction Timings" >> "$report_file"
        docker logs graph-layer-context7 --tail 20 2>/dev/null | \
            grep "construction_time_ms" | \
            tail -5 >> "$report_file"
        echo "" >> "$report_file"
    fi
    
    # Add container resource usage
    echo "### Resource Usage" >> "$report_file"
    docker stats graph-layer-context7 --no-stream >> "$report_file" 2>/dev/null || echo "Resource stats not available" >> "$report_file"
    
    log "INFO" "${GREEN}Performance report generated: $report_file${NC}"
}

# Cleanup function
cleanup() {
    log "INFO" "${CYAN}Cleaning up Context7 deployment...${NC}"
    
    # Remove Context7 environment file
    rm -f .env.context7
    
    # Stop and remove Context7 container
    docker stop graph-layer-context7 2>/dev/null || true
    docker rm graph-layer-context7 2>/dev/null || true
    
    log "INFO" "${GREEN}Cleanup completed${NC}"
}

# Main deployment flow
main() {
    log "INFO" "${PURPLE}Starting Context7-Enhanced Graph Layer Deployment${NC}"
    log "INFO" "Performance Target: <${TARGET_LATENCY_MS}ms latency"
    
    # Trap for cleanup on exit
    trap cleanup EXIT
    
    # Execute deployment steps
    check_prerequisites
    validate_context7_config
    build_context7_image
    start_context7_infrastructure
    deploy_context7_graph_layer
    
    log "INFO" "${GREEN}Context7 Graph Layer deployed successfully!${NC}"
    log "INFO" "${CYAN}Monitoring performance...${NC}"
    
    # Monitor and validate performance
    monitor_context7_performance
    
    if validate_performance_targets; then
        log "INFO" "${GREEN}🎉 Context7 performance targets achieved!${NC}"
    else
        log "WARN" "${YELLOW}Performance targets not yet achieved. Continue monitoring...${NC}"
    fi
    
    # Generate report
    generate_performance_report
    
    # Display access information
    echo ""
    echo "${PURPLE}=== Context7 Graph Layer Access Information ===${NC}"
    echo "${GREEN}Spark UI:${NC} http://localhost:4040"
    echo "${GREEN}Metrics:${NC} http://localhost:8085"
    echo "${GREEN}Kafka UI:${NC} http://localhost:8080"
    echo "${GREEN}Logs:${NC} docker logs graph-layer-context7 -f"
    echo "${GREEN}Performance Report:${NC} $LOGS_DIR/context7_performance_report.md"
    echo ""
    echo "${CYAN}Context7 optimizations include:${NC}"
    echo "  • torch.compile for 20-30% performance boost"
    echo "  • Asynchronous progress tracking for better checkpointing"
    echo "  • Sparse tensor operations for memory efficiency"
    echo "  • Enhanced Kafka producer/consumer settings"
    echo "  • RocksDB state store optimizations"
    echo ""
    echo "${GREEN}Deployment completed successfully! 🚀${NC}"
}

# Execute main function
main "$@" 