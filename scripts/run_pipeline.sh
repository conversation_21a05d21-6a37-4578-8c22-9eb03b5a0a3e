#!/bin/bash

# Blockchain AML Pipeline Runner Script
# This script runs the entire AML blockchain pipeline

set -e  # Exit on any error

# Color codes for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "$SCRIPT_DIR/.." && pwd)"
DOCKER_COMPOSE_FILE="$PROJECT_ROOT/configs/docker/docker-compose-integrated-pipeline.yml"

echo -e "${BLUE}🚀 Blockchain AML Pipeline Runner${NC}"
echo "==============================="

# Function to print status messages
print_status() {
    echo -e "${GREEN}✓${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}⚠${NC} $1"
}

print_error() {
    echo -e "${RED}✗${NC} $1"
}

# Step 1: Check prerequisites
echo -e "\n${BLUE}📋 Checking Prerequisites${NC}"
echo "-------------------------"

# Check if Docker is installed
if ! command -v docker &> /dev/null; then
    print_error "Docker is not installed. Please install Docker first."
    exit 1
fi

# Check if Docker Compose is installed
if ! command -v docker-compose &> /dev/null; then
    print_error "Docker Compose is not installed. Please install Docker Compose first."
    exit 1
fi

print_status "Docker and Docker Compose are available"

# Step 2: Build the CEP Layer
echo -e "\n${BLUE}📦 Building CEP Layer${NC}"
echo "---------------------"

cd "$PROJECT_ROOT/src/cep_layer"
if ./build_and_deploy.sh --rebuild; then
    print_status "CEP Layer built successfully"
else
    print_error "Failed to build CEP Layer"
    exit 1
fi

cd "$PROJECT_ROOT"

# Step 3: Start the Integrated Pipeline
echo -e "\n${BLUE}🚀 Starting Integrated Pipeline${NC}"
echo "-----------------------------"

if docker-compose -f "$DOCKER_COMPOSE_FILE" up -d; then
    print_status "Pipeline started successfully"
else
    print_error "Failed to start pipeline"
    exit 1
fi

# Step 4: Wait for services to be ready
echo -e "\n${BLUE}⏳ Waiting for Services to be Ready${NC}"
echo "--------------------------------"

# Wait for Kafka
echo "Waiting for Kafka..."
sleep 15
if docker ps | grep -q "kafka-integrated"; then
    print_status "Kafka is running"
else
    print_warning "Kafka may not be running properly"
fi

# Wait for Flink
echo "Waiting for Flink..."
sleep 15
if docker ps | grep -q "flink-jobmanager-integrated"; then
    print_status "Flink JobManager is running"
else
    print_warning "Flink JobManager may not be running properly"
fi

# Wait for Graph Layer
echo "Waiting for Graph Layer..."
sleep 15
if docker ps | grep -q "graph-construction-layer"; then
    print_status "Graph Layer is running"
else
    print_warning "Graph Layer may not be running properly"
fi

# Step 5: Show access information
echo -e "\n${BLUE}🌐 Access Information${NC}"
echo "====================="
echo -e "Kafka UI:        ${GREEN}http://localhost:8080${NC}"
echo -e "Flink Web UI:    ${GREEN}http://localhost:8081${NC}"
echo -e "Spark UI:        ${GREEN}http://localhost:4040${NC}"
echo -e "Ingestion API:   ${GREEN}http://localhost:8000${NC}"

# Step 6: Show useful commands
echo -e "\n${BLUE}📝 Useful Commands${NC}"
echo "==================="
echo -e "${YELLOW}View all logs:${NC} docker-compose -f $DOCKER_COMPOSE_FILE logs -f"
echo -e "${YELLOW}View service logs:${NC} docker-compose -f $DOCKER_COMPOSE_FILE logs -f [service-name]"
echo -e "${YELLOW}Stop pipeline:${NC} docker-compose -f $DOCKER_COMPOSE_FILE down"
echo -e "${YELLOW}Restart service:${NC} docker-compose -f $DOCKER_COMPOSE_FILE restart [service-name]"
echo -e "${YELLOW}Check Kafka topics:${NC} docker exec kafka-integrated kafka-topics --list --bootstrap-server localhost:9092"

echo -e "\n${GREEN}🎉 Pipeline startup complete!${NC}"
echo "================================="
echo "The blockchain AML pipeline is now running."
echo "Use the access information above to monitor the pipeline components."
echo "Press Ctrl+C to stop viewing logs (pipeline will continue running)."

# Show logs (optional)
docker-compose -f "$DOCKER_COMPOSE_FILE" logs -f 