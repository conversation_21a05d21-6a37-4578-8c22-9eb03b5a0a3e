#!/bin/bash

# Wait for Ka<PERSON>ka to be ready
echo "Waiting for <PERSON><PERSON><PERSON> to be ready..."
until echo exit | nc kafka 29092 >/dev/null 2>&1; do
    echo "Waiting for Kafka to be available..."
    sleep 5
done
echo "Kafka is ready!"

# Create required topics with proper configurations
echo "Creating Kafka topics..."

# Create ethereum-transactions topic
kafka-topics --create \
    --if-not-exists \
    --bootstrap-server kafka:29092 \
    --topic ethereum-transactions \
    --partitions 3 \
    --replication-factor 1 \
    --config retention.ms=86400000

# Create filtered-transactions topic
kafka-topics --create \
    --if-not-exists \
    --bootstrap-server kafka:29092 \
    --topic filtered-transactions \
    --partitions 3 \
    --replication-factor 1 \
    --config retention.ms=86400000

# Create graph-snapshots topic
kafka-topics --create \
    --if-not-exists \
    --bootstrap-server kafka:29092 \
    --topic graph-snapshots \
    --partitions 3 \
    --replication-factor 1 \
    --config retention.ms=86400000

echo "Topics created successfully!"

# List all topics to verify
echo "Listing all topics:"
kafka-topics --list --bootstrap-server kafka:29092 