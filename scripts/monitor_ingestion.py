#!/usr/bin/env python3
"""
Real-time monitoring of AML transaction ingestion
Shows live data flowing from Sepolia testnet through Kafka
"""

import time
import json
from kafka import KafkaConsumer
from typing import Dict, Any

def monitor_transactions(duration_seconds: int = 60):
    """Monitor live transaction ingestion for specified duration"""
    
    print("🔍 AML Ingestion Monitor - Live Sepolia Transaction Stream")
    print("=" * 60)
    print(f"⏱️  Monitoring for {duration_seconds} seconds...")
    print(f"📡 Source: Sepolia testnet via Infura WebSocket")
    print(f"🔄 Stream: Kafka topic 'ethereum-transactions'")
    print()
    
    try:
        # Create Kafka consumer
        consumer = KafkaConsumer(
            'ethereum-transactions',
            bootstrap_servers=['localhost:9092'],
            value_deserializer=lambda x: json.loads(x.decode('utf-8')),
            auto_offset_reset='latest',  # Only new messages
            enable_auto_commit=True,
            group_id='aml_monitor'
        )
        
        print("🟢 Connected to Kafka - Waiting for live transactions...")
        print()
        
        start_time = time.time()
        tx_count = 0
        total_value = 0.0
        
        # Monitor for specified duration
        for message in consumer:
            if time.time() - start_time > duration_seconds:
                break
                
            tx_data = message.value
            tx_count += 1
            
            # Extract key transaction details
            tx_hash = tx_data.get('hash', 'Unknown')
            block_number = tx_data.get('block_number', 0)
            from_addr = tx_data.get('from_address', 'Unknown')
            to_addr = tx_data.get('to_address', 'Contract Creation' if not tx_data.get('to_address') else 'Unknown')
            value_wei = tx_data.get('value', 0)
            value_eth = value_wei / 10**18 if value_wei else 0.0
            gas_used = tx_data.get('gas_used', 0)
            timestamp = tx_data.get('ingestion_timestamp', time.time())
            
            total_value += value_eth
            
            # Display transaction info
            print(f"📦 Block {block_number} | TX #{tx_count}")
            print(f"   🔗 Hash: {tx_hash[:10]}...{tx_hash[-8:] if len(tx_hash) > 18 else tx_hash}")
            print(f"   💰 Value: {value_eth:.6f} ETH")
            print(f"   ⛽ Gas Used: {gas_used:,}")
            print(f"   📤 From: {from_addr[:8]}...{from_addr[-6:]}")
            print(f"   📥 To: {to_addr[:8]}...{to_addr[-6:] if to_addr != 'Contract Creation' else to_addr}")
            print(f"   ⏰ Ingested: {time.strftime('%H:%M:%S', time.localtime(timestamp))}")
            print()
            
        # Summary
        elapsed = time.time() - start_time
        print("=" * 60)
        print("📊 INGESTION SUMMARY")
        print(f"⏱️  Duration: {elapsed:.1f} seconds")
        print(f"🔢 Transactions: {tx_count}")
        print(f"💰 Total Value: {total_value:.6f} ETH")
        print(f"⚡ Rate: {tx_count/elapsed:.2f} tx/sec")
        print(f"🌐 Network: Sepolia testnet")
        print("=" * 60)
        
        if tx_count > 0:
            print("✅ SUCCESS: Live transaction ingestion is working!")
        else:
            print("⚠️  No transactions received during monitoring period")
            print("   (This is normal for Sepolia testnet during quiet periods)")
            
    except Exception as e:
        print(f"❌ Error: {e}")
        print("Make sure Kafka is running and transactions topic exists")
    
    finally:
        if 'consumer' in locals():
            consumer.close()

if __name__ == "__main__":
    # Monitor for 30 seconds by default
    monitor_transactions(30) 