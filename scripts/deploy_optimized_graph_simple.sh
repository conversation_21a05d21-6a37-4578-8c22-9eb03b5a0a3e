#!/bin/bash

# Deploy Optimized Graph Construction Layer - Simple Version
# This script deploys the performance-optimized Graph Construction Layer

echo "🚀 Deploying Optimized Graph Construction Layer..."
echo "=================================================="

# Check if Docker is running
if ! docker info >/dev/null 2>&1; then
    echo "❌ Docker is not running. Please start Docker first."
    exit 1
fi

# Stop existing containers
echo "🛑 Stopping existing containers..."
docker-compose -f docker-compose-integrated-pipeline.yml down -v 2>/dev/null || true

# Create optimized docker-compose file
echo "📝 Creating optimized docker-compose configuration..."
cat > docker-compose-optimized-graph.yml << 'EOF'
version: '3.8'

services:
  zookeeper:
    image: confluentinc/cp-zookeeper:7.4.0
    environment:
      ZOOKEEPER_CLIENT_PORT: 2181
      ZOOKEEPER_TICK_TIME: 2000
    ports:
      - "2181:2181"
    networks:
      - graph-network

  kafka:
    image: confluentinc/cp-kafka:7.4.0
    depends_on:
      - zookeeper
    ports:
      - "29092:29092"
    environment:
      KAFKA_BROKER_ID: 1
      KAFKA_ZOOKEEPER_CONNECT: zookeeper:2181
      KAFKA_ADVERTISED_LISTENERS: PLAINTEXT://kafka:29092
      KAFKA_OFFSETS_TOPIC_REPLICATION_FACTOR: 1
      KAFKA_AUTO_CREATE_TOPICS_ENABLE: 'true'
      KAFKA_NUM_PARTITIONS: 8
      KAFKA_DEFAULT_REPLICATION_FACTOR: 1
    networks:
      - graph-network

  # Sepolia Ingestion Service
  sepolia-ingestion:
    build:
      context: .
      dockerfile: ingestion/Dockerfile
    environment:
      - INFURA_PROJECT_ID=${INFURA_PROJECT_ID:-your_infura_project_id}
      - KAFKA_BOOTSTRAP_SERVERS=kafka:29092
      - KAFKA_TOPIC=raw-transactions
      - NETWORK_NAME=sepolia
      - BATCH_SIZE=100
      - PROCESSING_INTERVAL=10
    depends_on:
      - kafka
    volumes:
      - ./logs:/app/logs
    networks:
      - graph-network
    restart: unless-stopped

  # Enhanced CEP Layer
  cep-layer:
    build:
      context: .
      dockerfile: cep_layer/Dockerfile
    environment:
      - KAFKA_BOOTSTRAP_SERVERS=kafka:29092
      - INPUT_TOPIC=raw-transactions
      - OUTPUT_TOPIC=filtered-transactions
      - FLINK_PARALLELISM=4
      - FLINK_CHECKPOINT_INTERVAL=30000
    depends_on:
      - kafka
    volumes:
      - ./logs:/app/logs
    networks:
      - graph-network
    restart: unless-stopped

  # Optimized Graph Construction Layer
  graph-layer:
    build:
      context: .
      dockerfile: graph_layer/Dockerfile.optimized
    environment:
      # Kafka Configuration
      - KAFKA_BOOTSTRAP_SERVERS=kafka:29092
      - KAFKA_INPUT_TOPIC=filtered-transactions
      - KAFKA_OUTPUT_TOPIC=graph-snapshots
      - KAFKA_CONSUMER_GROUP=graph-construction-consumer-v2
      
      # Spark Performance Configuration
      - SPARK_EXECUTOR_MEMORY=4g
      - SPARK_DRIVER_MEMORY=2g
      - SPARK_EXECUTOR_CORES=4
      - SPARK_SQL_ADAPTIVE_ENABLED=true
      
      # Streaming Performance Settings
      - TRIGGER_PROCESSING_TIME=10 seconds
      - MAX_OFFSETS_PER_TRIGGER=5000
      - WINDOW_DURATION=5 minutes
      - SLIDING_DURATION=30 seconds
      - WATERMARK_DELAY=1 minute
      
      # Graph Construction Optimization
      - MAX_NODES_PER_GRAPH=5000
      - EDGE_WEIGHT_THRESHOLD=0.001
      - NODE_FEATURE_DIM=32
      - BATCH_SIZE=2000
      - PARALLELISM=8
      
      # Performance Features
      - ENABLE_VECTORIZED_OPERATIONS=true
      - ENABLE_FEATURE_CACHING=true
      - ENABLE_GRAPH_PRUNING=true
      - ENABLE_DETAILED_METRICS=true
      - ENABLE_PERFORMANCE_LOGGING=true
      - ENABLE_COMPRESSION=true
      
      # Resource Management
      - JAVA_OPTS=-XX:+UseG1GC -XX:+UnlockDiagnosticVMOptions -Xmx4g
      
    depends_on:
      - kafka
      - cep-layer
    volumes:
      - ./logs:/app/logs
      - ./data:/app/data
    ports:
      - "4040:4040"  # Spark UI
      - "8085:8085"  # Metrics port
    networks:
      - graph-network
    restart: unless-stopped

networks:
  graph-network:
    driver: bridge

volumes:
  kafka-data:
  zookeeper-data:
  graph-logs:
  graph-checkpoints:
EOF

# Create optimized Dockerfile for Graph Layer
echo "🐳 Creating optimized Dockerfile..."
cat > graph_layer/Dockerfile.optimized << 'EOF'
# Multi-stage build for optimized Graph Construction Layer
FROM openjdk:11-jdk-slim as spark-base

# Install Python and system dependencies
RUN apt-get update && apt-get install -y \
    python3 \
    python3-pip \
    python3-dev \
    build-essential \
    curl \
    wget \
    procps \
    && rm -rf /var/lib/apt/lists/*

# Install Spark
ENV SPARK_VERSION=3.5.0
ENV HADOOP_VERSION=3
RUN wget -q https://archive.apache.org/dist/spark/spark-${SPARK_VERSION}/spark-${SPARK_VERSION}-bin-hadoop${HADOOP_VERSION}.tgz \
    && tar -xzf spark-${SPARK_VERSION}-bin-hadoop${HADOOP_VERSION}.tgz \
    && mv spark-${SPARK_VERSION}-bin-hadoop${HADOOP_VERSION} /opt/spark \
    && rm spark-${SPARK_VERSION}-bin-hadoop${HADOOP_VERSION}.tgz

ENV SPARK_HOME=/opt/spark
ENV PATH=$PATH:$SPARK_HOME/bin:$SPARK_HOME/sbin
ENV PYTHONPATH=$SPARK_HOME/python:$SPARK_HOME/python/lib/py4j-********-src.zip

# Production stage
FROM spark-base as production

# Set working directory
WORKDIR /app

# Copy requirements and install Python dependencies
COPY graph_layer/requirements.txt .
RUN pip3 install --no-cache-dir -r requirements.txt

# Copy application code
COPY graph_layer/ ./graph_layer/
COPY ingestion/config/ ./ingestion/config/ 2>/dev/null || true

# Set environment variables for optimization
ENV PYTHONUNBUFFERED=1
ENV SPARK_LOCAL_IP=0.0.0.0
ENV JAVA_OPTS="-XX:+UseG1GC -XX:+UnlockDiagnosticVMOptions -Xmx4g"

# Create directories
RUN mkdir -p /app/logs /app/data /tmp/graph-layer-checkpoints-v2 \
    && chmod -R 755 /app

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
    CMD python3 -c "import socket; socket.create_connection(('localhost', 4040), timeout=5)" || exit 1

# Default command
CMD ["python3", "graph_layer/spark_graph_builder.py"]
EOF

# Update requirements.txt for optimized performance
echo "📦 Updating requirements.txt..."
cat > graph_layer/requirements.txt << 'EOF'
# Core dependencies
pyspark==3.5.0
kafka-python==2.0.2
pandas==2.0.3
numpy==1.24.3

# PyTorch and PyTorch Geometric (optimized versions)
torch==2.0.1
torch-geometric==2.3.1
torch-sparse==0.6.17
torch-scatter==2.1.1

# Configuration and validation
pydantic==2.0.3
python-dotenv==1.0.0

# Monitoring and logging
prometheus-client==0.17.1
psutil==5.9.5

# Performance optimization
pyarrow==12.0.1
fastparquet==0.8.3

# Data processing
networkx==3.1
scikit-learn==1.3.0

# Utilities
requests==2.31.0
tqdm==4.65.0
EOF

# Build and deploy
echo "🔨 Building optimized Graph Construction Layer..."
docker-compose -f docker-compose-optimized-graph.yml build graph-layer

echo "🚀 Starting optimized infrastructure..."
docker-compose -f docker-compose-optimized-graph.yml up -d

echo "⏳ Waiting for services to start..."
sleep 30

echo "📊 Checking service status..."
docker-compose -f docker-compose-optimized-graph.yml ps

echo ""
echo "✅ Optimized Graph Construction Layer deployed successfully!"
echo ""
echo "📈 Performance Monitoring:"
echo "  • Spark UI: http://localhost:4040"
echo "  • Metrics: http://localhost:8085"
echo ""
echo "🔍 Check logs:"
echo "  • Graph Layer: docker logs fyp-2-copy_graph-layer_1 -f"
echo "  • CEP Layer: docker logs fyp-2-copy_cep-layer_1 -f"
echo "  • Ingestion: docker logs fyp-2-copy_sepolia-ingestion_1 -f"
echo ""
echo "📊 Monitor Kafka topics:"
echo "  • Raw transactions: docker exec fyp-2-copy_kafka_1 kafka-console-consumer --bootstrap-server localhost:9092 --topic raw-transactions --from-beginning"
echo "  • Filtered transactions: docker exec fyp-2-copy_kafka_1 kafka-console-consumer --bootstrap-server localhost:9092 --topic filtered-transactions --from-beginning"
echo "  • Graph snapshots: docker exec fyp-2-copy_kafka_1 kafka-console-consumer --bootstrap-server localhost:9092 --topic graph-snapshots --from-beginning"
echo ""
echo "🧪 Run performance tests:"
echo "  • python3 graph_layer/test_optimized_performance.py"
echo ""

# Wait a bit more and show initial logs
echo "📋 Initial Graph Layer logs:"
sleep 10
docker logs fyp-2-copy_graph-layer_1 --tail 20 2>/dev/null || echo "Graph layer starting..."

echo ""
echo "🎉 Deployment complete! The system is now processing live Sepolia transactions with optimized performance." 