#!/bin/bash

# Hybrid AML Pipeline Runner
# Architecture: Spark = Detection + Neo4j = Graph Persistence

set -e

echo "=== Starting Hybrid AML Pipeline ==="
echo "Architecture: Spark = Detection + Neo4j = Graph Persistence"

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
DOCKER_COMPOSE_FILE="$PROJECT_ROOT/configs/docker/docker-compose-integrated-pipeline.yml"

echo "Project root: $PROJECT_ROOT"
echo "Docker compose file: $DOCKER_COMPOSE_FILE"

# Function to check if service is running
check_service() {
    local service_name=$1
    if docker ps --format "table {{.Names}}" | grep -q "^${service_name}$"; then
        echo "✓ $service_name is running"
        return 0
    else
        echo "✗ $service_name is not running"
        return 1
    fi
}

# Function to wait for service to be ready
wait_for_service() {
    local service_name=$1
    local max_attempts=${2:-30}
    local attempt=1
    
    echo "Waiting for $service_name to be ready..."
    
    while [ $attempt -le $max_attempts ]; do
        if check_service "$service_name"; then
            echo "$service_name is ready!"
            return 0
        fi
        
        echo "Attempt $attempt/$max_attempts: $service_name not ready yet..."
        sleep 5
        ((attempt++))
    done
    
    echo "ERROR: $service_name failed to start within expected time"
    return 1
}

# Step 1: Stop any existing services
echo "=== Step 1: Stopping existing services ==="
cd "$PROJECT_ROOT"
docker-compose -f "$DOCKER_COMPOSE_FILE" down --remove-orphans || true

# Step 2: Build CEP layer
echo "=== Step 2: Building CEP layer ==="
cd "$PROJECT_ROOT/src/cep_layer"
chmod +x build_and_deploy.sh
./build_and_deploy.sh

# Step 3: Start infrastructure services
echo "=== Step 3: Starting infrastructure services ==="
cd "$PROJECT_ROOT"

# Start Kafka and Zookeeper first
echo "Starting Kafka infrastructure..."
docker-compose -f "$DOCKER_COMPOSE_FILE" up -d zookeeper kafka

# Wait for Kafka to be ready
wait_for_service "kafka-integrated"

# Start Neo4j
echo "Starting Neo4j..."
docker-compose -f "$DOCKER_COMPOSE_FILE" up -d neo4j

# Wait for Neo4j to be ready
wait_for_service "neo4j-graph-store"

# Step 4: Start Flink services
echo "=== Step 4: Starting Flink services ==="
docker-compose -f "$DOCKER_COMPOSE_FILE" up -d flink-jobmanager flink-taskmanager

# Wait for Flink to be ready
wait_for_service "flink-jobmanager-integrated"
wait_for_service "flink-taskmanager-integrated"

# Step 5: Test Neo4j connection
echo "=== Step 5: Testing Neo4j connection ==="
cd "$PROJECT_ROOT/src/graph_layer"

# Install Neo4j driver if not present
if ! python3 -c "import neo4j" 2>/dev/null; then
    echo "Installing Neo4j driver..."
    pip3 install neo4j==5.15.0
fi

# Test connection
echo "Testing Neo4j connection..."
export NEO4J_URI="bolt://localhost:7687"
export NEO4J_USER="neo4j"
export NEO4J_PASSWORD="password123"

python3 test_neo4j_connection.py

if [ $? -eq 0 ]; then
    echo "✓ Neo4j connection test passed"
else
    echo "✗ Neo4j connection test failed"
    exit 1
fi

# Step 6: Start ingestion service (with rate limiting)
echo "=== Step 6: Starting ingestion service ==="
docker-compose -f "$DOCKER_COMPOSE_FILE" up -d sepolia-ingestion

# Wait for ingestion to be ready
wait_for_service "sepolia-ingestion-integrated"

# Step 7: Submit Flink CEP job
echo "=== Step 7: Submitting Flink CEP job ==="
sleep 10  # Give Flink time to fully initialize

# Submit the job
docker exec flink-jobmanager-integrated flink run \
    --class com.blockchain.aml.cep.LiveAMLCEP \
    --parallelism 2 \
    /opt/flink/jars/aml-cep-1.0-SNAPSHOT.jar

if [ $? -eq 0 ]; then
    echo "✓ Flink CEP job submitted successfully"
else
    echo "✗ Failed to submit Flink CEP job"
    echo "Checking Flink logs..."
    docker logs flink-jobmanager-integrated --tail 50
    exit 1
fi

# Step 8: Start hybrid graph construction layer
echo "=== Step 8: Starting hybrid graph construction layer ==="
docker-compose -f "$DOCKER_COMPOSE_FILE" up -d graph-construction-layer

# Wait for graph layer to be ready
wait_for_service "graph-construction-layer"

# Step 9: Show status
echo "=== Step 9: Pipeline Status ==="
echo "Checking all services..."

services=("zookeeper-integrated" "kafka-integrated" "neo4j-graph-store" "flink-jobmanager-integrated" "flink-taskmanager-integrated" "sepolia-ingestion-integrated" "graph-construction-layer")

all_running=true
for service in "${services[@]}"; do
    if check_service "$service"; then
        continue
    else
        all_running=false
    fi
done

if [ "$all_running" = true ]; then
    echo ""
    echo "🎉 Hybrid AML Pipeline started successfully!"
    echo ""
    echo "Architecture Overview:"
    echo "  📊 Kafka: Message streaming"
    echo "  🔍 Flink CEP: Pattern detection"
    echo "  ⚡ Spark: High-throughput detection & scoring"
    echo "  🗄️  Neo4j: Graph persistence & analysis"
    echo ""
    echo "Access Points:"
    echo "  • Neo4j Browser: http://localhost:7474 (neo4j/password123)"
    echo "  • Flink Dashboard: http://localhost:8081"
    echo "  • Kafka Topics: localhost:9092"
    echo ""
    echo "Monitoring:"
    echo "  docker-compose -f $DOCKER_COMPOSE_FILE logs -f"
    echo ""
else
    echo "❌ Some services failed to start. Check logs:"
    echo "  docker-compose -f $DOCKER_COMPOSE_FILE logs"
    exit 1
fi
