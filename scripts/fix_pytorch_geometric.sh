#!/bin/bash
# Fix PyTorch Geometric Compatibility Issues on macOS

echo "🔧 Fixing PyTorch Geometric Compatibility Issues..."

# Method 1: Reinstall with compatible versions
echo "📦 Method 1: Reinstall compatible versions"
pip uninstall torch-scatter torch-cluster torch-geometric -y
pip install torch-geometric torch-scatter torch-cluster -f https://data.pyg.org/whl/torch-2.1.0+cpu.html

# Method 2: Create clean conda environment (alternative)
echo "🌟 Method 2: Clean conda environment (run manually if needed)"
echo "conda create -n clean_pytorch python=3.9"
echo "conda activate clean_pytorch" 
echo "conda install pytorch pytorch-geometric -c pytorch -c pyg"

# Method 3: CPU-only installation
echo "💻 Method 3: CPU-only installation"
pip install torch-geometric --no-deps
pip install torch-scatter torch-cluster --no-deps -f https://data.pyg.org/whl/torch-2.1.0+cpu.html

echo "✅ PyTorch Geometric fix attempt complete!"
echo "🧪 Test with: python -c 'import torch_geometric; print(\"Success!\")'" 