#!/usr/bin/env python3
"""
Kafka Status Checker - Verify topics and show recent transaction data
"""

import json
from kafka import KafkaConsumer, KafkaAdminClient
from kafka.structs import TopicPartition

def check_kafka_status():
    """Check Kafka cluster status and topics"""
    
    print("🔍 AML Kafka Cluster Status Check")
    print("=" * 50)
    
    try:
        # Check admin connection
        admin_client = KafkaAdminClient(
            bootstrap_servers=['localhost:9092'],
            client_id='aml_status_checker'
        )
        
        # List topics
        topics = admin_client.list_topics()
        print(f"📋 Available Topics: {len(topics)}")
        for topic in sorted(topics):
            print(f"   • {topic}")
        print()
        
        # Check ethereum-transactions topic specifically
        if 'ethereum-transactions' in topics:
            print("✅ Target topic 'ethereum-transactions' exists!")
            
            # Get recent messages
            consumer = KafkaConsumer(
                'ethereum-transactions',
                bootstrap_servers=['localhost:9092'],
                value_deserializer=lambda x: json.loads(x.decode('utf-8')),
                auto_offset_reset='latest',
                enable_auto_commit=False,
                consumer_timeout_ms=5000
            )
            
            # Get partition info
            partitions = consumer.partitions_for_topic('ethereum-transactions')
            print(f"📊 Partitions: {len(partitions) if partitions else 0}")
            
            # Try to get the latest message
            print("🔍 Checking for recent messages...")
            message_count = 0
            for message in consumer:
                message_count += 1
                if message_count <= 3:  # Show first 3 messages
                    tx_data = message.value
                    print(f"   📦 Message {message_count}:")
                    print(f"      Block: {tx_data.get('block_number', 'N/A')}")
                    print(f"      Hash: {tx_data.get('hash', 'N/A')[:20]}...")
                    print(f"      Value: {tx_data.get('value', 0) / 10**18:.6f} ETH")
                    print()
                
                if message_count >= 3:
                    break
            
            if message_count == 0:
                print("   ℹ️  No recent messages (normal during quiet periods)")
            
            consumer.close()
            
        else:
            print("❌ Topic 'ethereum-transactions' not found!")
            print("   Make sure the ingestion service has started and created the topic")
        
        print("=" * 50)
        print("📊 Kafka Status Summary:")
        print(f"   🟢 Kafka Connection: Working")
        print(f"   📋 Total Topics: {len(topics)}")
        print(f"   🎯 Target Topic: {'✅ Found' if 'ethereum-transactions' in topics else '❌ Missing'}")
        print(f"   🔗 Dashboard: http://localhost:8080/kafka-ui")
        
    except Exception as e:
        print(f"❌ Error connecting to Kafka: {e}")
        print("   Make sure Kafka is running: docker-compose -f docker-compose-ingestion.yml ps")

if __name__ == "__main__":
    check_kafka_status() 