#!/bin/bash

# AML Blockchain Ingestion Layer Setup Script
# This script sets up the data ingestion infrastructure

set -e

echo "🚀 Setting up AML Blockchain Data Ingestion Layer"
echo "=================================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if Docker is installed
check_docker() {
    if ! command -v docker &> /dev/null; then
        print_error "Docker is not installed. Please install Docker first."
        exit 1
    fi
    
    if ! command -v docker-compose &> /dev/null; then
        print_error "Docker Compose is not installed. Please install Docker Compose first."
        exit 1
    fi
    
    print_success "Docker and Docker Compose are available"
}

# Check if .env file exists
check_env_file() {
    if [ ! -f ".env" ]; then
        print_warning ".env file not found. Creating from example..."
        if [ -f "ingestion/.env.example" ]; then
            cp ingestion/.env.example .env
            print_warning "Please edit .env file with your Infura credentials"
            echo ""
            echo "Required configuration:"
            echo "  - INFURA_PROJECT_ID: Your Infura project ID"
            echo "  - INFURA_PROJECT_SECRET: Your Infura project secret"
            echo "  - ETHEREUM_NETWORK: sepolia (recommended for testing)"
            echo ""
            read -p "Press Enter to continue after configuring .env file..."
        else
            print_error "No .env.example file found"
            exit 1
        fi
    else
        print_success ".env file found"
    fi
}

# Create necessary directories
create_directories() {
    print_status "Creating necessary directories..."
    
    mkdir -p logs
    mkdir -p data
    mkdir -p ingestion/logs
    mkdir -p ingestion/data
    
    print_success "Directories created"
}

# Build Docker images
build_images() {
    print_status "Building Docker images..."
    
    # Build ingestion service image
    cd ingestion
    docker build -t aml-ingestion:latest .
    cd ..
    
    print_success "Docker images built successfully"
}

# Start infrastructure services
start_infrastructure() {
    print_status "Starting Kafka infrastructure..."
    
    # Start Zookeeper and Kafka
    docker-compose -f docker-compose-ingestion.yml up -d zookeeper kafka kafka-ui
    
    # Wait for Kafka to be ready
    print_status "Waiting for Kafka to be ready..."
    sleep 30
    
    # Check if Kafka is running
    if docker-compose -f docker-compose-ingestion.yml ps kafka | grep -q "Up"; then
        print_success "Kafka infrastructure is running"
    else
        print_error "Failed to start Kafka infrastructure"
        exit 1
    fi
}

# Start ingestion service
start_ingestion() {
    print_status "Starting ingestion service..."
    
    docker-compose -f docker-compose-ingestion.yml up -d ingestion-service
    
    # Wait a moment for service to start
    sleep 10
    
    # Check service status
    if docker-compose -f docker-compose-ingestion.yml ps ingestion-service | grep -q "Up"; then
        print_success "Ingestion service is running"
    else
        print_warning "Ingestion service may have issues. Check logs with:"
        echo "docker-compose -f docker-compose-ingestion.yml logs ingestion-service"
    fi
}

# Show service status
show_status() {
    echo ""
    echo "📊 Service Status"
    echo "=================="
    
    docker-compose -f docker-compose-ingestion.yml ps
    
    echo ""
    echo "🌐 Web Interfaces"
    echo "=================="
    echo "Kafka UI: http://localhost:8080"
    echo ""
    
    echo "📝 Useful Commands"
    echo "=================="
    echo "View logs: docker-compose -f docker-compose-ingestion.yml logs -f [service-name]"
    echo "Stop all: docker-compose -f docker-compose-ingestion.yml down"
    echo "Restart: docker-compose -f docker-compose-ingestion.yml restart [service-name]"
    echo ""
}

# Test ingestion service
test_ingestion() {
    print_status "Testing ingestion service configuration..."
    
    # Test with dry-run mode
    docker-compose -f docker-compose-ingestion.yml exec ingestion-service python main.py --dry-run
    
    if [ $? -eq 0 ]; then
        print_success "Ingestion service configuration test passed"
    else
        print_warning "Ingestion service configuration test failed"
    fi
}

# Main execution
main() {
    echo "Starting setup process..."
    echo ""
    
    # Check prerequisites
    check_docker
    check_env_file
    
    # Setup
    create_directories
    build_images
    start_infrastructure
    start_ingestion
    
    # Test and show status
    test_ingestion
    show_status
    
    print_success "AML Blockchain Data Ingestion Layer setup complete!"
    echo ""
    echo "Next steps:"
    echo "1. Monitor the ingestion service logs"
    echo "2. Check Kafka UI for incoming transaction data"
    echo "3. Proceed with CEP layer implementation"
}

# Parse command line arguments
case "${1:-setup}" in
    "setup")
        main
        ;;
    "start")
        print_status "Starting services..."
        docker-compose -f docker-compose-ingestion.yml up -d
        show_status
        ;;
    "stop")
        print_status "Stopping services..."
        docker-compose -f docker-compose-ingestion.yml down
        print_success "Services stopped"
        ;;
    "status")
        show_status
        ;;
    "logs")
        service=${2:-ingestion-service}
        docker-compose -f docker-compose-ingestion.yml logs -f $service
        ;;
    "test")
        test_ingestion
        ;;
    *)
        echo "Usage: $0 {setup|start|stop|status|logs [service]|test}"
        echo ""
        echo "Commands:"
        echo "  setup   - Full setup and start (default)"
        echo "  start   - Start all services"
        echo "  stop    - Stop all services"
        echo "  status  - Show service status"
        echo "  logs    - Show logs (optionally for specific service)"
        echo "  test    - Test service configuration"
        exit 1
        ;;
esac 