---
description: 
globs: "cep/**/*", "**/flink*", "**/cep*"
alwaysApply: false
---
---
description: Flink SQL CEP patterns for transaction filtering
globs: ["cep/**/*", "**/flink*", "**/cep*"]
alwaysApply: false
---

# Flink CEP Guidelines

## CEP Pattern Design
- Use time-based windows for pattern matching
- Implement proper watermarking for late data
- Design patterns for 85% data reduction target
- Use quantifiers for flexible pattern matching

## SQL Query Optimization
- Leverage temporal joins for efficiency
- Use proper indexing on timestamp fields
- Implement sliding windows for real-time detection
- Optimize state backend configuration

## Performance Monitoring
- Track processing latency and throughput
- Monitor checkpoint completion times
- Alert on backpressure situations
- Measure pattern match accuracy

@cep/rules/config.ym
@cep/queries/suspicious_patterns.sql
