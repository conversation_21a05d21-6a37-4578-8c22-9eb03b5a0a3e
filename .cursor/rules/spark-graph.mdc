---
description: 
globs: "graph/**/*", "**/spark*", "**/streaming*"
alwaysApply: false
---
---
description: Spark Structured Streaming for graph construction and transaction analysis
globs: ["graph/**/*", "**/spark*", "**/streaming*"]
alwaysApply: false
---

# Spark Graph Construction Guidelines

## Streaming Configuration
- Use 5-minute micro-batches for graph windows
- Configure checkpointing for fault tolerance
- Set appropriate trigger intervals
- Use append output mode for efficiency

## Graph Building
- Build transaction graphs with weighted edges
- Include temporal features (recency, frequency)
- Calculate network metrics (degree, centrality)
- Implement efficient graph serialization

## Data Processing
- Deduplicate transactions within windows
- Handle late-arriving data gracefully
- Aggregate features at multiple time scales
- Maintain graph state across windows

## Integration Patterns
- Consume from Kafka with offset management
- Produce to GNN service via message queue
- Handle backpressure from downstream services
- Implement exactly-once processing semantics

@graph/spark_graph_builder.py
@config/spark_config.yml
