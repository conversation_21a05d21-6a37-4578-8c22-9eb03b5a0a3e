---
description: 
globs: "scripts/**/*", "config/**/*", "*.yml", "*.yaml", "README.md"
alwaysApply: false
---
---
description: End-to-end pipeline integration, monitoring, and system architecture
globs: ["scripts/**/*", "config/**/*", "*.yml", "*.yaml", "README.md"]
alwaysApply: false
---

# Pipeline Integration Guidelines

## System Architecture
- Follow event-driven architecture patterns
- Implement proper service boundaries
- Use asynchronous communication between services
- Design for horizontal scalability

## Configuration Management
- Use environment-specific config files
- Implement configuration validation
- Support hot-reloading where possible
- Use secrets management for sensitive data

## Monitoring and Observability
- Implement distributed tracing
- Use structured logging with correlation IDs
- Monitor key business metrics (accuracy, latency)
- Set up alerting for system health

## Error Handling
- Implement circuit breaker patterns
- Use exponential backoff for retries
- Handle graceful degradation scenarios
- Maintain detailed error documentation

## Development Workflow
- Use Docker Compose for local development
- Implement comprehensive testing strategy
- Document setup and deployment procedures
- Follow GitOps practices for deployments

@config/pipeline_config.yml
@scripts/setup_environment.py
@docker-compose.yml
