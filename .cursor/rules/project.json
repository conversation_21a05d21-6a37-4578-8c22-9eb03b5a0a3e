{"name": "Blockchain AML Pipeline Rules", "description": "Rules for the Real-Time Spatio-Temporal GNN + CEP Pipeline for Blockchain AML", "rules": {"python": {"include": ["**/*.py"], "exclude": ["**/venv/**", "**/__pycache__/**", "**/build/**", "**/dist/**", "**/.eggs/**"], "linting": {"enabled": true, "pylint": {"enabled": true, "rcfile": ".pyl<PERSON><PERSON>", "score": {"minimum": 8.0}}, "flake8": {"enabled": true, "max-line-length": 100, "ignore": ["E203", "W503"]}, "black": {"line-length": 100, "target-version": ["py38"]}, "mypy": {"enabled": true, "strict": true}}, "formatting": {"enabled": true, "formatter": "black", "on_save": true}, "imports": {"sort": true, "useIsort": true, "settings": {"profile": "black", "multi_line_output": 3, "include_trailing_comma": true, "force_grid_wrap": 0, "use_parentheses": true, "ensure_newline_before_comments": true, "line_length": 100}}, "docstring": {"style": "google", "required": true}}, "docker": {"include": ["**/Dockerfile*", "docker-compose*.yml"], "linting": {"enabled": true, "hadolint": {"ignore": ["DL3008", "DL3013"]}}, "best_practices": {"multi_stage_builds": true, "cache_optimization": true, "size_optimization": true}}, "markdown": {"include": ["**/*.md"], "linting": {"enabled": true, "markdownlint": {"rules": {"MD013": false, "MD033": false}}}, "spell_check": true}, "components": {"ingestion": {"path": "ingestion/", "description": "Ethereum blockchain data ingestion service", "dependencies": [], "required_files": ["main.py", "requirements.txt", "Dockerfile", "README.md"], "metrics": {"throughput": "transactions_per_second", "latency": "ms_per_transaction"}}, "cep": {"path": "cep/", "description": "Complex Event Processing using Flink SQL", "dependencies": ["ingestion"], "required_files": ["flink-sql-job.py", "requirements.txt", "Dockerfile", "README.md"], "metrics": {"processing_time": "ms_per_event", "filter_rate": "events_filtered_percentage"}}, "graph": {"path": "graph/", "description": "Graph construction using Spark Streaming", "dependencies": ["cep"], "required_files": ["graph_builder.py", "requirements.txt", "Dockerfile", "README.md"], "metrics": {"graph_construction_time": "ms_per_window", "nodes_per_window": "count"}}, "gnn": {"path": "gnn/", "description": "TGAT model implementation and training", "dependencies": ["graph"], "required_files": ["model.py", "train.py", "requirements.txt", "Dockerfile", "README.md"], "metrics": {"training_time": "seconds_per_epoch", "inference_time": "ms_per_batch", "accuracy": "percentage"}}, "explainer": {"path": "explainer/", "description": "GNNExplainer implementation", "dependencies": ["gnn"], "required_files": ["explainer.py", "requirements.txt", "Dockerfile", "README.md"], "metrics": {"explanation_time": "ms_per_prediction", "fidelity": "percentage"}}, "dashboard": {"path": "dashboard/", "description": "Web dashboard for visualization", "dependencies": ["explainer"], "required_files": ["app.py", "requirements.txt", "Dockerfile", "README.md"], "metrics": {"response_time": "ms_per_request", "concurrent_users": "count"}}}, "testing": {"python": {"framework": "pytest", "coverage": {"enabled": true, "minimum": 80, "exclude": ["tests/*", "setup.py"]}, "requirements": {"unit_tests": true, "integration_tests": true, "performance_tests": true}, "fixtures": {"path": "tests/fixtures", "naming": "test_*.py"}}, "performance": {"benchmarks": {"enabled": true, "tool": "pytest-benchmark", "threshold": {"max_regression": "5%"}}}}, "documentation": {"required": {"components": ["README.md"], "api": ["docs/api.md"], "deployment": ["docs/deployment.md"], "architecture": ["docs/architecture.md"], "monitoring": ["docs/monitoring.md"], "troubleshooting": ["docs/troubleshooting.md"]}, "api_documentation": {"tool": "sphinx", "format": "OpenAPI", "version": "3.0.0"}, "diagrams": {"required": ["architecture", "data-flow"], "format": "mermaid"}}, "security": {"scanning": {"enabled": true, "tools": ["bandit", "safety", "trivy", "snyk"], "schedule": "daily"}, "secrets": {"patterns": ["INFURA_API_KEY", "KAFKA_PASSWORD", "DATABASE_URL", "API_KEY", "SECRET_KEY", "ACCESS_TOKEN", "PRIVATE_KEY", "PASSWORD"], "scanning": {"pre_commit": true, "ci": true}}, "dependencies": {"check": {"enabled": true, "schedule": "daily", "auto_update": "patch"}}}, "git": {"commit": {"conventional": true, "types": ["feat", "fix", "docs", "style", "refactor", "perf", "test", "build", "ci", "chore", "security"], "scope": {"required": true, "allowed": ["ingestion", "cep", "graph", "gnn", "explainer", "dashboard", "deps", "docs"]}}, "branch": {"naming": {"pattern": "^(feature|bugfix|hotfix|release)\\/[a-z0-9-]+$", "protected": ["main", "develop"]}, "merge": {"require_review": true, "require_tests": true}}}, "ci_cd": {"required_checks": ["lint", "test", "security-scan", "build", "integration-test"], "environments": ["development", "staging", "production"], "deployment": {"strategy": "blue-green", "rollback": {"enabled": true, "automatic": true}}}, "monitoring": {"metrics": {"collection": {"tool": "prometheus", "interval": "10s"}, "visualization": {"tool": "grafana", "dashboards": ["system-metrics", "application-metrics", "business-metrics"]}}, "logging": {"format": "json", "level": "INFO", "storage": {"retention": "30d", "rotation": "1d"}}, "alerts": {"latency": ">500ms", "error_rate": ">1%", "memory_usage": ">85%", "cpu_usage": ">80%"}}}}