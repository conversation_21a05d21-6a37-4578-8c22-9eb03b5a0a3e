---
description: 
globs: "ingestion/**/*", "**/kafka*", "**/producer*"
alwaysApply: false
---
---
description: Kafka producer patterns and Ethereum data ingestion using Infura API
globs: ["ingestion/**/*", "**/kafka*", "**/producer*"]
alwaysApply: false
---

# Kafka Ingestion Guidelines

## Producer Configuration
- Use idempotent producers for exactly-once semantics
- Set appropriate batch size and linger.ms for throughput
- Configure retries and retry backoff for reliability
- Use compression (snappy or lz4) for efficiency

## Infura API Integration
- Implement exponential backoff for rate limiting
- Use connection pooling for HTTP requests
- Handle WebSocket reconnection gracefully
- Monitor API quota usage

## Error Handling
- Dead letter queues for failed messages
- Circuit breaker pattern for external APIs
- Comprehensive logging with correlation IDs
- Graceful shutdown handling

## Data Validation
- Validate transaction schemas before sending
- Sanitize and normalize addresses
- Check for duplicate transactions
- Handle malformed blockchain data

@ingestion/kafka_producer.py
@config/kafka_topics.yml
