---
description: 
globs: "gnn/**/*", "**/tgat*", "**/torch*", "**/model*"
alwaysApply: false
---
---
description: PyTorch Geometric TGAT implementation with ensemble methods and explainability
globs: ["gnn/**/*", "**/tgat*", "**/torch*", "**/model*"]
alwaysApply: false
---

# PyTorch GNN Guidelines

## TGAT Implementation
- Use temporal attention for time-aware embeddings
- Implement memory modules for long-term patterns
- Apply dropout and batch normalization
- Use gradient clipping for training stability

## Ensemble Methods
- Combine multiple model predictions
- Use weighted voting based on confidence scores
- Implement model diversity through different architectures
- Apply ensemble pruning for efficiency

## Loss Functions
- Use focal loss for class imbalance
- Implement custom loss for temporal consistency
- Add regularization terms for stability
- Monitor convergence with multiple metrics

## Adaptive Thresholds
- Adjust thresholds based on recent performance
- Use sliding window for threshold computation
- Implement different thresholds per transaction type
- Handle concept drift detection

## Explainability
- Generate attention-based explanations
- Provide feature importance scores
- Create subgraph visualizations
- Output human-readable reasoning

@gnn/models/tgat_ensemble.py
@gnn/training/focal_loss.py
@gnn/explainer/attention_explainer.py
