---
description: 
globs: "docker-compose.yml", "Dockerfile*", "docker/**/*"
alwaysApply: false
---
---
description: Docker orchestration and containerization patterns for the blockchain AML pipeline
globs: ["docker-compose.yml", "Dockerfile*", "docker/**/*"]
alwaysApply: false
---

# Docker Orchestration Guidelines

## Service Configuration
- Use health checks for all services
- Set appropriate resource limits and reservations
- Configure proper logging drivers
- Use environment variables for configuration

## Multi-stage Dockerfiles
- Optimize for production with multi-stage builds
- Use Alpine Linux for smaller image sizes
- Copy only necessary files to reduce image size
- Set non-root users for security

## Network and Volume Management
- Use custom networks for service isolation
- Mount configuration files as volumes
- Persist data with named volumes
- Use secrets for sensitive data

## Key Commands
```bash
# Start all services
docker-compose up -d

# View logs
docker-compose logs -f [service]

# Scale services
docker-compose up -d --scale spark-worker=3
```

@docker-compose.yml
@docker/kafka/Dockerfile
@docker/spark/spark-defaults.conf
