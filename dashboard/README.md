# Dashboard

This directory will contain the web dashboard components for monitoring and managing the AML detection pipeline.

## Planned Components

### Frontend Dashboard
- **Real-time Monitoring**: Live transaction processing status
- **Detection Results**: Flagged transactions with risk scores
- **Model Explanations**: Interactive visualizations of why transactions were flagged
- **Compliance Reports**: Audit trails and regulatory reporting
- **System Health**: Pipeline performance and monitoring metrics

### Backend API
- **REST API**: Endpoints for dashboard data
- **WebSocket**: Real-time updates for live monitoring
- **Authentication**: User management and access control
- **Data Export**: Compliance reporting and data export

## Planned Features

### Main Dashboard
- 📊 **Real-time Metrics**: Transaction throughput, detection rates, system health
- 🚨 **Alert System**: Immediate notifications for high-risk transactions
- 📈 **Trend Analysis**: Historical patterns and detection trends
- 🔍 **Transaction Search**: Search and filter detected transactions

### Investigation Interface
- 🕵️ **Transaction Details**: Full context for flagged transactions
- 🧠 **Explanation Views**: GNN explainer results with visualizations
- 📊 **Risk Assessment**: Detailed risk scoring and confidence levels
- 🔗 **Network Analysis**: Transaction flow and connection analysis

### Compliance Module
- 📋 **Audit Trails**: Complete transaction investigation history
- 📊 **Regulatory Reports**: Automated compliance reporting
- ✅ **Human Verification**: Interface for compliance officer review
- 📈 **Performance Tracking**: Model accuracy and effectiveness metrics

## Technology Stack (Planned)

### Frontend
- **Framework**: React.js with TypeScript
- **UI Library**: Material-UI or Ant Design
- **Visualization**: D3.js, Chart.js for graphs and network visualization
- **Real-time**: WebSocket integration for live updates

### Backend
- **Framework**: FastAPI (Python) or Express.js (Node.js)
- **Database**: Integration with existing SQLite AML database
- **Authentication**: JWT-based authentication
- **API Documentation**: OpenAPI/Swagger

## Integration Points

The dashboard will integrate with:
- **Database Layer**: Transaction data and detection results
- **TGAT Model**: Live prediction results
- **GNN Explainer**: Explanation data and visualizations
- **Pipeline Components**: System health and performance metrics

## Development Status

🚧 **Status**: Planned - Not yet implemented

This directory is prepared for future dashboard development. The backend database schema and API endpoints are designed to support dashboard requirements.

## Quick Start (Future)

```bash
# Install dependencies
npm install  # Frontend
pip install -r requirements.txt  # Backend

# Start development servers
npm start  # Frontend (React)
python app.py  # Backend (FastAPI)

# Access dashboard
http://localhost:3000
``` 