"""
Time Encoding Layer for TGAT

Implements functional time representation learning for temporal graph neural networks.
Based on the self-attention with functional time representation learning approach.
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np


class TimeEncodingLayer(nn.Module):
    """
    Time encoding layer that learns to represent time as a continuous function.
    
    This implementation uses a combination of basis functions and learned parameters
    to encode temporal information in a way that captures both absolute and relative
    time relationships.
    """
    
    def __init__(self, time_dim: int, basis_func: str = 'cosine', device: str = 'cpu'):
        """
        Initialize the time encoding layer.
        
        Args:
            time_dim: Dimension of the time encoding
            basis_func: Type of basis function ('cosine', 'time', 'learned')
            device: Device to run the model on
        """
        super(TimeEncodingLayer, self).__init__()
        
        self.time_dim = time_dim
        self.basis_func = basis_func
        self.device = device
        
        if basis_func == 'cosine':
            # Cosine basis functions with learnable frequencies
            self.w = nn.Linear(1, time_dim)
            self.reset_parameters()
            
        elif basis_func == 'time':
            # Time basis functions (as used in original TGAT)
            self.w = nn.Linear(1, time_dim)
            self.reset_parameters()
            
        elif basis_func == 'learned':
            # Fully learned time encoding with MLP
            self.time_encoder = nn.Sequential(
                nn.Linear(1, time_dim // 2),
                nn.ReLU(),
                nn.Linear(time_dim // 2, time_dim),
                nn.ReLU()
            )
            
        self.to(device)
    
    def reset_parameters(self):
        """Reset parameters for the linear layers."""
        if hasattr(self, 'w'):
            nn.init.xavier_uniform_(self.w.weight)
            nn.init.zeros_(self.w.bias)
    
    def forward(self, ts: torch.Tensor) -> torch.Tensor:
        """
        Encode timestamps into time embeddings.
        
        Args:
            ts: Tensor of timestamps, shape [batch_size] or [batch_size, 1]
            
        Returns:
            Time embeddings, shape [batch_size, time_dim]
        """
        # Ensure ts is 2D
        if ts.dim() == 1:
            ts = ts.unsqueeze(-1)
        
        # Normalize timestamps to [0, 1] range for stability
        ts_normalized = self._normalize_timestamps(ts)
        
        if self.basis_func == 'cosine':
            return self._cosine_encoding(ts_normalized)
        elif self.basis_func == 'time':
            return self._time_encoding(ts_normalized)
        elif self.basis_func == 'learned':
            return self.time_encoder(ts_normalized)
        else:
            # Default fallback
            return torch.zeros(ts.size(0), self.time_dim, device=self.device)
    
    def _normalize_timestamps(self, ts: torch.Tensor) -> torch.Tensor:
        """Normalize timestamps to [0, 1] range."""
        # Use min-max normalization
        ts_min = torch.min(ts)
        ts_max = torch.max(ts)
        
        if ts_max - ts_min > 0:
            return (ts - ts_min) / (ts_max - ts_min)
        else:
            return torch.zeros_like(ts)
    
    def _cosine_encoding(self, ts: torch.Tensor) -> torch.Tensor:
        """Apply cosine basis function encoding."""
        # Generate frequencies
        freqs = torch.arange(1, self.time_dim + 1, dtype=torch.float32, device=self.device)
        freqs = freqs.unsqueeze(0)  # [1, time_dim]
        
        # Apply cosine encoding
        ts_expanded = ts * freqs  # [batch_size, time_dim]
        cosine_encoding = torch.cos(ts_expanded)
        
        # Apply learnable linear transformation
        return self.w(ts) + cosine_encoding
    
    def _time_encoding(self, ts: torch.Tensor) -> torch.Tensor:
        """Apply time basis function encoding (original TGAT approach)."""
        # Simple linear transformation with non-linearity
        time_encoding = self.w(ts)
        return torch.tanh(time_encoding)


class MultiHeadTimeAttention(nn.Module):
    """
    Multi-head attention mechanism for temporal graphs.
    
    This layer computes attention weights based on both node features and
    temporal relationships, allowing the model to focus on the most relevant
    temporal neighbors.
    """
    
    def __init__(self, 
                 node_dim: int, 
                 time_dim: int, 
                 num_heads: int = 8,
                 dropout: float = 0.1,
                 device: str = 'cpu'):
        """
        Initialize the multi-head time attention layer.
        
        Args:
            node_dim: Dimension of node features
            time_dim: Dimension of time encoding
            num_heads: Number of attention heads
            dropout: Dropout probability
            device: Device to run the model on
        """
        super(MultiHeadTimeAttention, self).__init__()
        
        self.node_dim = node_dim
        self.time_dim = time_dim
        self.num_heads = num_heads
        self.dropout = dropout
        self.d_k = (node_dim + time_dim) // num_heads
        
        # Attention projections
        self.query_projection = nn.Linear(node_dim + time_dim, node_dim + time_dim)
        self.key_projection = nn.Linear(node_dim + time_dim, node_dim + time_dim)
        self.value_projection = nn.Linear(node_dim + time_dim, node_dim + time_dim)
        
        # Output projection
        self.output_projection = nn.Linear(node_dim + time_dim, node_dim)
        
        # Dropout
        self.dropout_layer = nn.Dropout(dropout)
        
        self.to(device)
    
    def forward(self, 
                node_features: torch.Tensor,
                time_features: torch.Tensor,
                edge_index: torch.Tensor,
                                 attention_mask: torch.Tensor | None = None) -> tuple[torch.Tensor, torch.Tensor]:
        """
        Compute multi-head temporal attention.
        
        Args:
            node_features: Node feature tensor, shape [num_nodes, node_dim]
            time_features: Time feature tensor, shape [num_nodes, time_dim]
            edge_index: Edge index tensor, shape [2, num_edges]
            attention_mask: Optional attention mask
            
        Returns:
            Updated node features, shape [num_nodes, node_dim]
        """
        batch_size = node_features.size(0)
        
        # Concatenate node and time features
        combined_features = torch.cat([node_features, time_features], dim=-1)
        
        # Compute Q, K, V
        Q = self.query_projection(combined_features)
        K = self.key_projection(combined_features)
        V = self.value_projection(combined_features)
        
        # Reshape for multi-head attention
        Q = Q.view(batch_size, self.num_heads, self.d_k)
        K = K.view(batch_size, self.num_heads, self.d_k)
        V = V.view(batch_size, self.num_heads, self.d_k)
        
        # Compute attention weights
        attention_weights = torch.matmul(Q, K.transpose(-2, -1)) / np.sqrt(self.d_k)
        
        # Apply mask if provided
        if attention_mask is not None:
            attention_weights = attention_weights.masked_fill(attention_mask == 0, -1e9)
        
        # Apply softmax
        attention_weights = F.softmax(attention_weights, dim=-1)
        attention_weights = self.dropout_layer(attention_weights)
        
        # Apply attention to values
        attended_values = torch.matmul(attention_weights, V)
        
        # Concatenate heads and apply output projection
        attended_values = attended_values.view(batch_size, -1)
        output = self.output_projection(attended_values)
        
        return output, attention_weights


class TemporalMessagePassing(nn.Module):
    """
    Temporal message passing for TGAT layers.
    
    This module aggregates messages from temporal neighbors using attention
    mechanisms that consider both spatial and temporal relationships.
    """
    
    def __init__(self, 
                 feature_dim: int,
                 time_dim: int,
                 num_heads: int = 4,
                 dropout: float = 0.1,
                 device: str = 'cpu'):
        """
        Initialize temporal message passing.
        
        Args:
            feature_dim: Dimension of node features
            time_dim: Dimension of time encoding
            num_heads: Number of attention heads
            dropout: Dropout probability
            device: Device to run the model on
        """
        super(TemporalMessagePassing, self).__init__()
        
        self.feature_dim = feature_dim
        self.time_dim = time_dim
        self.num_heads = num_heads
        
        # Time encoding
        self.time_encoder = TimeEncodingLayer(time_dim, device=device)
        
        # Multi-head temporal attention
        self.temporal_attention = MultiHeadTimeAttention(
            feature_dim, time_dim, num_heads, dropout, device
        )
        
        # Message transformation
        self.message_mlp = nn.Sequential(
            nn.Linear(feature_dim * 2 + time_dim, feature_dim),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(feature_dim, feature_dim)
        )
        
        self.to(device)
    
    def forward(self, 
                x: torch.Tensor,
                edge_index: torch.Tensor,
                edge_time: torch.Tensor,
                node_time: torch.Tensor) -> torch.Tensor:
        """
        Perform temporal message passing.
        
        Args:
            x: Node features, shape [num_nodes, feature_dim]
            edge_index: Edge indices, shape [2, num_edges]
            edge_time: Edge timestamps, shape [num_edges]
            node_time: Node timestamps, shape [num_nodes]
            
        Returns:
            Updated node features, shape [num_nodes, feature_dim]
        """
        # Encode time features
        edge_time_encoded = self.time_encoder(edge_time)
        node_time_encoded = self.time_encoder(node_time)
        
        # Get source and target nodes
        src_nodes, dst_nodes = edge_index[0], edge_index[1]
        
        # Get source and destination features
        src_features = x[src_nodes]
        dst_features = x[dst_nodes]
        
        # Create messages
        messages = torch.cat([
            src_features,
            dst_features,
            edge_time_encoded
        ], dim=-1)
        
        # Transform messages
        transformed_messages = self.message_mlp(messages)
        
        # Aggregate messages using temporal attention
        aggregated_features, attention_weights = self.temporal_attention(
            x, node_time_encoded, edge_index
        )
        
        return aggregated_features 