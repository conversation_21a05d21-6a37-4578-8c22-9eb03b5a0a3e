"""
TGAT Layer Implementation

Implements the core TGAT (Temporal Graph Attention Networks) layer for
temporal graph neural networks. This layer performs temporal message
passing with attention mechanisms.
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
from typing import Tuple, Optional

from .time_encoding import TimeEncodingLayer, MultiHeadTimeAttention
from .memory_module import MemoryModule
from .message_aggregator import MessageAggregator


class TGATLayer(nn.Module):
    """
    Individual TGAT layer implementing temporal graph attention.
    
    This layer performs one step of temporal message passing with attention
    mechanisms, combining node features, temporal information, and graph structure.
    """
    
    def __init__(self, 
                 input_dim: int,
                 hidden_dim: int,
                 time_dim: int,
                 num_heads: int = 8,
                 dropout: float = 0.1,
                 use_memory: bool = True,
                 memory_dim: int | None = None,
                 device: str = 'cpu'):
        """
        Initialize TGAT layer.
        
        Args:
            input_dim: Input feature dimension
            hidden_dim: Hidden dimension for computations
            time_dim: Time encoding dimension
            num_heads: Number of attention heads
            dropout: Dropout probability
            use_memory: Whether to use memory module
            memory_dim: Memory dimension (defaults to hidden_dim)
            device: Device to run the model on
        """
        super(<PERSON><PERSON><PERSON><PERSON><PERSON>, self).__init__()
        
        self.input_dim = input_dim
        self.hidden_dim = hidden_dim
        self.time_dim = time_dim
        self.num_heads = num_heads
        self.dropout = dropout
        self.use_memory = use_memory
        self.memory_dim = memory_dim or hidden_dim
        self.device = device
        
        # Time encoding
        self.time_encoder = TimeEncodingLayer(time_dim, device=device)
        
        # Multi-head temporal attention
        self.temporal_attention = MultiHeadTimeAttention(
            input_dim, time_dim, num_heads, dropout, device
        )
        
        # Message aggregation
        self.message_aggregator = MessageAggregator(
            input_dim + time_dim, hidden_dim, num_heads, 'attention', dropout, device
        )
        
        # Feature transformation
        self.node_transformer = nn.Sequential(
            nn.Linear(input_dim, hidden_dim),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(hidden_dim, hidden_dim)
        )
        
        # Output projection
        self.output_projection = nn.Linear(hidden_dim * 2, hidden_dim)
        
        # Normalization layers
        self.layer_norm1 = nn.LayerNorm(hidden_dim)
        self.layer_norm2 = nn.LayerNorm(hidden_dim)
        
        # Memory module (optional)
        if use_memory:
            # Note: Memory module will be initialized externally with proper num_nodes
            self.memory_projection = nn.Linear(self.memory_dim, hidden_dim)
        
        self.to(device)
    
    def forward(self, 
                x: torch.Tensor,
                edge_index: torch.Tensor,
                edge_time: torch.Tensor,
                node_time: torch.Tensor,
                memory_module: MemoryModule | None = None) -> torch.Tensor:
        """
        Forward pass of TGAT layer.
        
        Args:
            x: Node features, shape [num_nodes, input_dim]
            edge_index: Edge indices, shape [2, num_edges]
            edge_time: Edge timestamps, shape [num_edges]
            node_time: Node timestamps, shape [num_nodes]
            memory_module: Optional memory module
            
        Returns:
            Updated node features, shape [num_nodes, hidden_dim]
        """
        # Get source and target nodes
        src_nodes, dst_nodes = edge_index[0], edge_index[1]
        
        # Encode temporal information
        edge_time_encoded = self.time_encoder(edge_time)
        node_time_encoded = self.time_encoder(node_time)
        
        # Transform node features
        x_transformed = self.node_transformer(x)
        
        # Get memory if available
        if self.use_memory and memory_module is not None:
            memory_features = memory_module.get_updated_memory(
                torch.arange(x.size(0), device=self.device), 
                node_time.max()
            )
            memory_features = self.memory_projection(memory_features)
        else:
            memory_features = torch.zeros_like(x_transformed)
        
        # Combine features with memory
        enhanced_features = x_transformed + memory_features
        enhanced_features = self.layer_norm1(enhanced_features)
        
        # Prepare messages
        src_features = enhanced_features[src_nodes]
        
        # Combine source features with temporal encoding
        temporal_messages = torch.cat([src_features, edge_time_encoded], dim=-1)
        
        # Aggregate messages using attention
        aggregated_messages = self.message_aggregator(
            temporal_messages, dst_nodes
        )
        
        # Ensure output matches input size
        if aggregated_messages.size(0) != x.size(0):
            # Pad with zeros for nodes without incoming edges
            full_messages = torch.zeros(x.size(0), aggregated_messages.size(-1), 
                                      device=self.device)
            unique_dst = torch.unique(dst_nodes)
            full_messages[unique_dst] = aggregated_messages
            aggregated_messages = full_messages
        
        # Combine original features with aggregated messages
        combined_features = torch.cat([enhanced_features, aggregated_messages], dim=-1)
        output = self.output_projection(combined_features)
        
        # Residual connection and normalization
        output = output + enhanced_features
        output = self.layer_norm2(output)
        
        return output


class MultiLayerTGAT(nn.Module):
    """
    Multi-layer TGAT implementation with residual connections.
    
    This module stacks multiple TGAT layers to create a deeper temporal
    graph neural network with enhanced representational capacity.
    """
    
    def __init__(self, 
                 input_dim: int,
                 hidden_dim: int,
                 time_dim: int,
                 num_layers: int = 2,
                 num_heads: int = 8,
                 dropout: float = 0.1,
                 use_memory: bool = True,
                 memory_dim: int = None,
                 device: str = 'cpu'):
        """
        Initialize multi-layer TGAT.
        
        Args:
            input_dim: Input feature dimension
            hidden_dim: Hidden dimension for each layer
            time_dim: Time encoding dimension
            num_layers: Number of TGAT layers
            num_heads: Number of attention heads per layer
            dropout: Dropout probability
            use_memory: Whether to use memory module
            memory_dim: Memory dimension
            device: Device to run the model on
        """
        super(MultiLayerTGAT, self).__init__()
        
        self.input_dim = input_dim
        self.hidden_dim = hidden_dim
        self.time_dim = time_dim
        self.num_layers = num_layers
        self.use_memory = use_memory
        
        # Input projection
        self.input_projection = nn.Linear(input_dim, hidden_dim)
        
        # TGAT layers
        self.tgat_layers = nn.ModuleList()
        for i in range(num_layers):
            layer_input_dim = hidden_dim  # All layers use hidden_dim after input projection
            layer = TGATLayer(
                layer_input_dim, hidden_dim, time_dim, num_heads, 
                dropout, use_memory, memory_dim, device
            )
            self.tgat_layers.append(layer)
        
        # Layer-wise normalization
        self.layer_norms = nn.ModuleList([
            nn.LayerNorm(hidden_dim) for _ in range(num_layers)
        ])
        
        self.to(device)
    
    def forward(self, 
                x: torch.Tensor,
                edge_index: torch.Tensor,
                edge_time: torch.Tensor,
                node_time: torch.Tensor,
                memory_module: MemoryModule = None) -> torch.Tensor:
        """
        Forward pass through multiple TGAT layers.
        
        Args:
            x: Node features, shape [num_nodes, input_dim]
            edge_index: Edge indices, shape [2, num_edges]
            edge_time: Edge timestamps, shape [num_edges]
            node_time: Node timestamps, shape [num_nodes]
            memory_module: Optional memory module
            
        Returns:
            Final node representations, shape [num_nodes, hidden_dim]
        """
        # Project input to hidden dimension
        h = self.input_projection(x)
        
        # Apply TGAT layers
        for i, (tgat_layer, layer_norm) in enumerate(zip(self.tgat_layers, self.layer_norms)):
            # Apply TGAT layer
            h_new = tgat_layer(h, edge_index, edge_time, node_time, memory_module)
            
            # Residual connection (skip connection)
            h = h + h_new
            h = layer_norm(h)
        
        return h


class TGATWithEdgeFeatures(nn.Module):
    """
    TGAT layer variant that incorporates edge features.
    
    This variant extends the basic TGAT layer to handle edge features
    in addition to temporal information, making it suitable for graphs
    with rich edge attributes.
    """
    
    def __init__(self, 
                 input_dim: int,
                 edge_dim: int,
                 hidden_dim: int,
                 time_dim: int,
                 num_heads: int = 8,
                 dropout: float = 0.1,
                 device: str = 'cpu'):
        """
        Initialize TGAT with edge features.
        
        Args:
            input_dim: Node feature dimension
            edge_dim: Edge feature dimension
            hidden_dim: Hidden dimension
            time_dim: Time encoding dimension
            num_heads: Number of attention heads
            dropout: Dropout probability
            device: Device to run the model on
        """
        super(TGATWithEdgeFeatures, self).__init__()
        
        self.input_dim = input_dim
        self.edge_dim = edge_dim
        self.hidden_dim = hidden_dim
        self.time_dim = time_dim
        self.num_heads = num_heads
        
        # Time encoding
        self.time_encoder = TimeEncodingLayer(time_dim, device=device)
        
        # Edge feature transformation
        self.edge_transformer = nn.Sequential(
            nn.Linear(edge_dim + time_dim, hidden_dim),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(hidden_dim, hidden_dim)
        )
        
        # Node feature transformation
        self.node_transformer = nn.Sequential(
            nn.Linear(input_dim, hidden_dim),
            nn.ReLU(),
            nn.Dropout(dropout)
        )
        
        # Combined attention mechanism
        self.attention = nn.MultiheadAttention(
            embed_dim=hidden_dim,
            num_heads=num_heads,
            dropout=dropout,
            batch_first=True
        )
        
        # Message computation
        self.message_mlp = nn.Sequential(
            nn.Linear(hidden_dim * 3, hidden_dim * 2),  # src + dst + edge
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(hidden_dim * 2, hidden_dim)
        )
        
        # Output layers
        self.output_mlp = nn.Sequential(
            nn.Linear(hidden_dim * 2, hidden_dim),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(hidden_dim, hidden_dim)
        )
        
        self.layer_norm = nn.LayerNorm(hidden_dim)
        
        self.to(device)
    
    def forward(self, 
                x: torch.Tensor,
                edge_index: torch.Tensor,
                edge_attr: torch.Tensor,
                edge_time: torch.Tensor) -> torch.Tensor:
        """
        Forward pass with edge features.
        
        Args:
            x: Node features, shape [num_nodes, input_dim]
            edge_index: Edge indices, shape [2, num_edges]
            edge_attr: Edge attributes, shape [num_edges, edge_dim]
            edge_time: Edge timestamps, shape [num_edges]
            
        Returns:
            Updated node features, shape [num_nodes, hidden_dim]
        """
        # Transform node features
        x_transformed = self.node_transformer(x)
        
        # Encode temporal information
        edge_time_encoded = self.time_encoder(edge_time)
        
        # Transform edge features
        edge_features_combined = torch.cat([edge_attr, edge_time_encoded], dim=-1)
        edge_transformed = self.edge_transformer(edge_features_combined)
        
        # Get source and target nodes
        src_nodes, dst_nodes = edge_index[0], edge_index[1]
        
        # Get source and destination features
        src_features = x_transformed[src_nodes]
        dst_features = x_transformed[dst_nodes]
        
        # Compute messages
        messages = torch.cat([src_features, dst_features, edge_transformed], dim=-1)
        messages = self.message_mlp(messages)
        
        # Aggregate messages by destination node
        num_nodes = x.size(0)
        aggregated_messages = torch.zeros(num_nodes, self.hidden_dim, device=x.device)
        
        for i in range(num_nodes):
            # Find messages for this node
            mask = (dst_nodes == i)
            if mask.sum() > 0:
                node_messages = messages[mask]
                # Use mean aggregation (could be enhanced with attention)
                aggregated_messages[i] = torch.mean(node_messages, dim=0)
        
        # Combine original features with aggregated messages
        combined = torch.cat([x_transformed, aggregated_messages], dim=-1)
        output = self.output_mlp(combined)
        
        # Residual connection and normalization
        output = output + x_transformed
        output = self.layer_norm(output)
        
        return output


class TGATConv(nn.Module):
    """
    Simplified TGAT convolution layer compatible with PyTorch Geometric.
    
    This is a streamlined version of TGAT that can be easily integrated
    with PyTorch Geometric's MessagePassing framework.
    """
    
    def __init__(self, 
                 in_channels: int,
                 out_channels: int,
                 time_dim: int = 32,
                 heads: int = 1,
                 dropout: float = 0.0,
                 bias: bool = True,
                 device: str = 'cpu'):
        """
        Initialize TGAT convolution layer.
        
        Args:
            in_channels: Input feature dimension
            out_channels: Output feature dimension
            time_dim: Time encoding dimension
            heads: Number of attention heads
            dropout: Dropout probability
            bias: Whether to use bias
            device: Device to run the model on
        """
        super(TGATConv, self).__init__()
        
        self.in_channels = in_channels
        self.out_channels = out_channels
        self.time_dim = time_dim
        self.heads = heads
        self.dropout = dropout
        
        # Time encoder
        self.time_encoder = TimeEncodingLayer(time_dim, device=device)
        
        # Linear transformations for Q, K, V
        self.lin_q = nn.Linear(in_channels, out_channels * heads, bias=False)
        self.lin_k = nn.Linear(in_channels + time_dim, out_channels * heads, bias=False)
        self.lin_v = nn.Linear(in_channels + time_dim, out_channels * heads, bias=False)
        
        # Output projection
        self.lin_out = nn.Linear(out_channels * heads, out_channels, bias=bias)
        
        # Dropout
        self.dropout_layer = nn.Dropout(dropout)
        
        self.reset_parameters()
        self.to(device)
    
    def reset_parameters(self):
        """Reset parameters."""
        nn.init.xavier_uniform_(self.lin_q.weight)
        nn.init.xavier_uniform_(self.lin_k.weight)
        nn.init.xavier_uniform_(self.lin_v.weight)
        nn.init.xavier_uniform_(self.lin_out.weight)
        if self.lin_out.bias is not None:
            nn.init.zeros_(self.lin_out.bias)
    
    def forward(self, 
                x: torch.Tensor,
                edge_index: torch.Tensor,
                edge_time: torch.Tensor) -> torch.Tensor:
        """
        Forward pass of TGAT convolution.
        
        Args:
            x: Node features, shape [num_nodes, in_channels]
            edge_index: Edge indices, shape [2, num_edges]
            edge_time: Edge timestamps, shape [num_edges]
            
        Returns:
            Updated node features, shape [num_nodes, out_channels]
        """
        # Encode time
        time_encoded = self.time_encoder(edge_time)
        
        # Get source and target nodes
        src_nodes, dst_nodes = edge_index[0], edge_index[1]
        
        # Get source features
        x_src = x[src_nodes]
        
        # Combine source features with time encoding
        x_src_time = torch.cat([x_src, time_encoded], dim=-1)
        
        # Compute Q, K, V
        q = self.lin_q(x).view(-1, self.heads, self.out_channels)
        k = self.lin_k(x_src_time).view(-1, self.heads, self.out_channels)
        v = self.lin_v(x_src_time).view(-1, self.heads, self.out_channels)
        
        # Compute attention weights
        q_dst = q[dst_nodes]  # [num_edges, heads, out_channels]
        
        # Attention scores
        attention_scores = (q_dst * k).sum(dim=-1)  # [num_edges, heads]
        attention_scores = attention_scores / (self.out_channels ** 0.5)
        
        # Apply softmax per destination node
        attention_weights = torch.zeros_like(attention_scores)
        unique_dst = torch.unique(dst_nodes)
        
        for dst_node in unique_dst:
            mask = (dst_nodes == dst_node)
            attention_weights[mask] = F.softmax(attention_scores[mask], dim=0)
        
        # Apply dropout
        attention_weights = self.dropout_layer(attention_weights)
        
        # Weighted aggregation
        out = torch.zeros(x.size(0), self.heads, self.out_channels, device=x.device)
        
        for i, dst_node in enumerate(dst_nodes):
            out[dst_node] += attention_weights[i].unsqueeze(-1) * v[i]
        
        # Reshape and apply output projection
        out = out.view(-1, self.heads * self.out_channels)
        out = self.lin_out(out)
        
        return out 