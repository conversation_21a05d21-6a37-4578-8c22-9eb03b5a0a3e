"""
Memory Module for TGAT

Implements memory mechanism for storing and updating node embeddings over time.
This allows the model to maintain historical information about nodes and their interactions.
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
from typing import Dict, Tuple, Optional


class MemoryModule(nn.Module):
    """
    Memory module for temporal graph networks.
    
    This module maintains embeddings for nodes that are updated over time
    based on the node's interactions and temporal patterns.
    """
    
    def __init__(self, 
                 num_nodes: int,
                 memory_dim: int,
                 time_dim: int,
                 message_dim: int,
                 device: str = 'cpu'):
        """
        Initialize the memory module.
        
        Args:
            num_nodes: Number of nodes in the graph
            memory_dim: Dimension of memory embeddings
            time_dim: Dimension of time encoding
            message_dim: Dimension of messages
            device: Device to run the model on
        """
        super(MemoryModule, self).__init__()
        
        self.num_nodes = num_nodes
        self.memory_dim = memory_dim
        self.time_dim = time_dim
        self.message_dim = message_dim
        self.device = device
        
        # Initialize memory and last update time
        self.memory = nn.Parameter(torch.randn(num_nodes, memory_dim))
        self.last_update_time = nn.Parameter(torch.zeros(num_nodes), requires_grad=False)
        
        # Memory updater (GRU-based)
        self.memory_updater = nn.GRUCell(message_dim, memory_dim)
        
        # Message function
        self.message_function = MessageFunction(memory_dim, time_dim, message_dim, device)
        
        # Memory initialization
        self.reset_parameters()
        self.to(device)
    
    def reset_parameters(self):
        """Initialize memory parameters."""
        nn.init.xavier_uniform_(self.memory)
        nn.init.zeros_(self.last_update_time)
    
    def get_memory(self, node_ids: torch.Tensor) -> torch.Tensor:
        """
        Get memory for specified nodes.
        
        Args:
            node_ids: Node IDs to retrieve memory for
            
        Returns:
            Memory embeddings for the specified nodes
        """
        return self.memory[node_ids]
    
    def update_memory(self, 
                     node_ids: torch.Tensor,
                     messages: torch.Tensor,
                     timestamps: torch.Tensor):
        """
        Update memory for specified nodes.
        
        Args:
            node_ids: Node IDs to update
            messages: Messages for updating memory
            timestamps: Timestamps for the updates
        """
        with torch.no_grad():
            # Get current memory for nodes
            current_memory = self.memory[node_ids]
            
            # Update memory using GRU
            updated_memory = self.memory_updater(messages, current_memory)
            
            # Update memory and timestamps
            self.memory[node_ids] = updated_memory
            self.last_update_time[node_ids] = timestamps
    
    def compute_messages(self, 
                        src_nodes: torch.Tensor,
                        dst_nodes: torch.Tensor,
                        edge_features: torch.Tensor,
                        edge_timestamps: torch.Tensor) -> torch.Tensor:
        """
        Compute messages for memory update.
        
        Args:
            src_nodes: Source node IDs
            dst_nodes: Destination node IDs  
            edge_features: Edge features
            edge_timestamps: Edge timestamps
            
        Returns:
            Messages for memory update
        """
        # Get memory for source and destination nodes
        src_memory = self.get_memory(src_nodes)
        dst_memory = self.get_memory(dst_nodes)
        
        # Compute messages using message function
        messages = self.message_function(
            src_memory, dst_memory, edge_features, edge_timestamps
        )
        
        return messages
    
    def get_updated_memory(self, 
                          node_ids: torch.Tensor,
                          current_time: torch.Tensor) -> torch.Tensor:
        """
        Get updated memory considering temporal decay.
        
        Args:
            node_ids: Node IDs
            current_time: Current timestamp
            
        Returns:
            Updated memory with temporal decay
        """
        # Get base memory
        base_memory = self.get_memory(node_ids)
        
        # Compute time differences
        last_times = self.last_update_time[node_ids]
        time_diffs = current_time - last_times
        
        # Apply temporal decay (simple exponential decay)
        decay_factor = torch.exp(-time_diffs.unsqueeze(-1) * 0.01)  # Adjustable decay rate
        
        return base_memory * decay_factor
    
    def reset_memory(self, node_ids: torch.Tensor | None = None):
        """
        Reset memory for specified nodes or all nodes.
        
        Args:
            node_ids: Node IDs to reset (None for all nodes)
        """
        with torch.no_grad():
            if node_ids is None:
                nn.init.xavier_uniform_(self.memory)
                nn.init.zeros_(self.last_update_time)
            else:
                self.memory[node_ids] = torch.randn_like(self.memory[node_ids])
                self.last_update_time[node_ids] = 0.0


class MessageFunction(nn.Module):
    """
    Message function for computing messages between nodes.
    
    This function takes memory states of source and destination nodes
    along with edge features and computes messages for memory updates.
    """
    
    def __init__(self, 
                 memory_dim: int,
                 time_dim: int,
                 message_dim: int,
                 device: str = 'cpu'):
        """
        Initialize the message function.
        
        Args:
            memory_dim: Dimension of memory embeddings
            time_dim: Dimension of time encoding
            message_dim: Dimension of output messages
            device: Device to run the model on
        """
        super(MessageFunction, self).__init__()
        
        self.memory_dim = memory_dim
        self.time_dim = time_dim
        self.message_dim = message_dim
        
        # Time encoder for edge timestamps
        from .time_encoding import TimeEncodingLayer
        self.time_encoder = TimeEncodingLayer(time_dim, device=device)
        
        # Message computation MLP
        input_dim = memory_dim * 2 + time_dim  # src_memory + dst_memory + time_encoding
        
        self.message_mlp = nn.Sequential(
            nn.Linear(input_dim, message_dim * 2),
            nn.ReLU(),
            nn.Dropout(0.1),
            nn.Linear(message_dim * 2, message_dim),
            nn.ReLU()
        )
        
        self.to(device)
    
    def forward(self, 
                src_memory: torch.Tensor,
                dst_memory: torch.Tensor,
                edge_features: torch.Tensor,
                edge_timestamps: torch.Tensor) -> torch.Tensor:
        """
        Compute messages between nodes.
        
        Args:
            src_memory: Source node memory, shape [num_edges, memory_dim]
            dst_memory: Destination node memory, shape [num_edges, memory_dim]
            edge_features: Edge features, shape [num_edges, edge_feat_dim]
            edge_timestamps: Edge timestamps, shape [num_edges]
            
        Returns:
            Messages, shape [num_edges, message_dim]
        """
        # Encode timestamps
        time_encoding = self.time_encoder(edge_timestamps)
        
        # Concatenate features
        combined_features = torch.cat([
            src_memory,
            dst_memory, 
            time_encoding
        ], dim=-1)
        
        # Compute messages
        messages = self.message_mlp(combined_features)
        
        return messages


class MemoryAggregator(nn.Module):
    """
    Aggregator for combining multiple messages for a single node.
    
    When a node receives multiple messages (from multiple edges),
    this module aggregates them into a single message for memory update.
    """
    
    def __init__(self, 
                 message_dim: int,
                 aggregation_method: str = 'mean',
                 device: str = 'cpu'):
        """
        Initialize the memory aggregator.
        
        Args:
            message_dim: Dimension of messages
            aggregation_method: Aggregation method ('mean', 'sum', 'max', 'attention')
            device: Device to run the model on
        """
        super(MemoryAggregator, self).__init__()
        
        self.message_dim = message_dim
        self.aggregation_method = aggregation_method
        
        if aggregation_method == 'attention':
            # Attention-based aggregation
            self.attention_weights = nn.Sequential(
                nn.Linear(message_dim, message_dim // 2),
                nn.ReLU(),
                nn.Linear(message_dim // 2, 1),
                nn.Softmax(dim=0)
            )
        
        self.to(device)
    
    def forward(self, 
                messages: torch.Tensor,
                node_ids: torch.Tensor) -> Tuple[torch.Tensor, torch.Tensor]:
        """
        Aggregate messages for each unique node.
        
        Args:
            messages: All messages, shape [num_messages, message_dim]
            node_ids: Node IDs for each message, shape [num_messages]
            
        Returns:
            Tuple of (aggregated_messages, unique_node_ids)
        """
        unique_nodes = torch.unique(node_ids)
        aggregated_messages = []
        
        for node_id in unique_nodes:
            # Get messages for this node
            node_mask = (node_ids == node_id)
            node_messages = messages[node_mask]
            
            if self.aggregation_method == 'mean':
                aggregated = torch.mean(node_messages, dim=0)
            elif self.aggregation_method == 'sum':
                aggregated = torch.sum(node_messages, dim=0)
            elif self.aggregation_method == 'max':
                aggregated = torch.max(node_messages, dim=0)[0]
            elif self.aggregation_method == 'attention':
                # Compute attention weights
                weights = self.attention_weights(node_messages)
                aggregated = torch.sum(weights * node_messages, dim=0)
            else:
                # Default to mean
                aggregated = torch.mean(node_messages, dim=0)
            
            aggregated_messages.append(aggregated)
        
        aggregated_messages = torch.stack(aggregated_messages)
        
        return aggregated_messages, unique_nodes


class TemporalMemoryBank(nn.Module):
    """
    Advanced memory bank that supports temporal querying and efficient updates.
    
    This module provides more sophisticated memory management including
    temporal indexing and efficient batch updates.
    """
    
    def __init__(self, 
                 num_nodes: int,
                 memory_dim: int,
                 time_window: float = 1e6,  # Time window for temporal queries
                 device: str = 'cpu'):
        """
        Initialize the temporal memory bank.
        
        Args:
            num_nodes: Number of nodes
            memory_dim: Memory dimension
            time_window: Time window for temporal queries
            device: Device to run the model on
        """
        super(TemporalMemoryBank, self).__init__()
        
        self.num_nodes = num_nodes
        self.memory_dim = memory_dim
        self.time_window = time_window
        self.device = device
        
        # Memory storage
        self.memory = nn.Parameter(torch.randn(num_nodes, memory_dim))
        self.last_update_time = nn.Parameter(torch.zeros(num_nodes), requires_grad=False)
        
        # Memory update count for each node
        self.update_count = nn.Parameter(torch.zeros(num_nodes), requires_grad=False)
        
        self.to(device)
    
    def temporal_query(self, 
                      node_ids: torch.Tensor,
                      query_time: torch.Tensor,
                      time_threshold: float | None = None) -> torch.Tensor:
        """
        Query memory with temporal constraints.
        
        Args:
            node_ids: Node IDs to query
            query_time: Query timestamp
            time_threshold: Maximum time difference (None for no threshold)
            
        Returns:
            Memory embeddings for valid temporal queries
        """
        # Get memory and last update times
        memory = self.memory[node_ids]
        last_times = self.last_update_time[node_ids]
        
        if time_threshold is not None:
            # Check temporal validity
            time_diffs = query_time - last_times
            valid_mask = time_diffs <= time_threshold
            
            # Zero out invalid memories
            memory = memory * valid_mask.unsqueeze(-1).float()
        
        return memory
    
    def batch_update(self, 
                    node_ids: torch.Tensor,
                    new_memories: torch.Tensor,
                    timestamps: torch.Tensor):
        """
        Efficiently update memory for multiple nodes.
        
        Args:
            node_ids: Node IDs to update
            new_memories: New memory values
            timestamps: Update timestamps
        """
        with torch.no_grad():
            self.memory[node_ids] = new_memories
            self.last_update_time[node_ids] = timestamps
            self.update_count[node_ids] += 1
    
    def get_memory_statistics(self) -> Dict[str, torch.Tensor]:
        """
        Get statistics about memory usage.
        
        Returns:
            Dictionary with memory statistics
        """
        return {
            'mean_memory_norm': torch.norm(self.memory, dim=1).mean(),
            'max_update_count': self.update_count.max(),
            'mean_update_count': self.update_count.float().mean(),
            'last_update_time_range': self.last_update_time.max() - self.last_update_time.min()
        } 