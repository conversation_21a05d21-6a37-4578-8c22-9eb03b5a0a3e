"""
Message Aggregator for TGAT

Implements temporal message aggregation with attention mechanisms.
This module handles the aggregation of temporal messages from neighbors.
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
from typing import Tuple, Optional


class MessageAggregator(nn.Module):
    """
    Temporal message aggregator with attention mechanisms.
    
    This module aggregates messages from temporal neighbors using various
    aggregation strategies including attention-based approaches.
    """
    
    def __init__(self, 
                 input_dim: int,
                 hidden_dim: int,
                 num_heads: int = 4,
                 aggregation_method: str = 'attention',
                 dropout: float = 0.1,
                 device: str = 'cpu'):
        """
        Initialize the message aggregator.
        
        Args:
            input_dim: Input feature dimension
            hidden_dim: Hidden dimension for computations
            num_heads: Number of attention heads
            aggregation_method: Aggregation method ('attention', 'mean', 'sum', 'max')
            dropout: Dropout probability
            device: Device to run the model on
        """
        super(MessageAggregator, self).__init__()
        
        self.input_dim = input_dim
        self.hidden_dim = hidden_dim
        self.num_heads = num_heads
        self.aggregation_method = aggregation_method
        self.dropout = dropout
        
        if aggregation_method == 'attention':
            # Multi-head attention for message aggregation
            self.attention_layers = nn.ModuleList([
                AttentionAggregation(input_dim, hidden_dim, device)
                for _ in range(num_heads)
            ])
            
            # Output projection
            self.output_projection = nn.Linear(hidden_dim * num_heads, hidden_dim)
            
        elif aggregation_method == 'lstm':
            # LSTM-based aggregation
            self.lstm_aggregator = nn.LSTM(
                input_dim, hidden_dim, batch_first=True, dropout=dropout
            )
            
        elif aggregation_method == 'transformer':
            # Transformer-based aggregation
            encoder_layer = nn.TransformerEncoderLayer(
                d_model=input_dim,
                nhead=num_heads,
                dim_feedforward=hidden_dim,
                dropout=dropout,
                batch_first=True
            )
            self.transformer = nn.TransformerEncoder(encoder_layer, num_layers=2)
            self.output_projection = nn.Linear(input_dim, hidden_dim)
        
        # Common layers
        self.dropout_layer = nn.Dropout(dropout)
        self.layer_norm = nn.LayerNorm(hidden_dim)
        
        self.to(device)
    
    def forward(self, 
                messages: torch.Tensor,
                neighbor_indices: torch.Tensor,
                temporal_weights: torch.Tensor | None = None,
                attention_mask: torch.Tensor | None = None) -> torch.Tensor:
        """
        Aggregate messages from temporal neighbors.
        
        Args:
            messages: Messages from neighbors, shape [num_messages, input_dim]
            neighbor_indices: Indices indicating which node each message belongs to
            temporal_weights: Optional temporal weights for messages
            attention_mask: Optional attention mask
            
        Returns:
            Aggregated messages, shape [num_nodes, hidden_dim]
        """
        if self.aggregation_method == 'attention':
            return self._attention_aggregation(
                messages, neighbor_indices, temporal_weights, attention_mask
            )
        elif self.aggregation_method == 'lstm':
            return self._lstm_aggregation(messages, neighbor_indices)
        elif self.aggregation_method == 'transformer':
            return self._transformer_aggregation(messages, neighbor_indices, attention_mask)
        else:
            return self._simple_aggregation(messages, neighbor_indices)
    
    def _attention_aggregation(self, 
                             messages: torch.Tensor,
                             neighbor_indices: torch.Tensor,
                             temporal_weights: torch.Tensor | None,
                             attention_mask: torch.Tensor | None) -> torch.Tensor:
        """Attention-based message aggregation."""
        # Apply each attention head
        head_outputs = []
        for attention_layer in self.attention_layers:
            head_output = attention_layer(
                messages, neighbor_indices, temporal_weights, attention_mask
            )
            head_outputs.append(head_output)
        
        # Concatenate heads
        multi_head_output = torch.cat(head_outputs, dim=-1)
        
        # Apply output projection
        output = self.output_projection(multi_head_output)
        output = self.dropout_layer(output)
        output = self.layer_norm(output)
        
        return output
    
    def _lstm_aggregation(self, 
                         messages: torch.Tensor,
                         neighbor_indices: torch.Tensor) -> torch.Tensor:
        """LSTM-based message aggregation."""
        # Group messages by node
        unique_nodes = torch.unique(neighbor_indices)
        aggregated_messages = []
        
        for node_id in unique_nodes:
            # Get messages for this node
            node_mask = (neighbor_indices == node_id)
            node_messages = messages[node_mask].unsqueeze(0)  # [1, seq_len, input_dim]
            
            # Apply LSTM
            lstm_output, _ = self.lstm_aggregator(node_messages)
            
            # Take the last output
            aggregated = lstm_output[0, -1, :]  # [hidden_dim]
            aggregated_messages.append(aggregated)
        
        output = torch.stack(aggregated_messages)
        output = self.layer_norm(output)
        
        return output
    
    def _transformer_aggregation(self, 
                                messages: torch.Tensor,
                                neighbor_indices: torch.Tensor,
                                attention_mask: torch.Tensor | None) -> torch.Tensor:
        """Transformer-based message aggregation."""
        # Group messages by node
        unique_nodes = torch.unique(neighbor_indices)
        aggregated_messages = []
        
        for node_id in unique_nodes:
            # Get messages for this node
            node_mask = (neighbor_indices == node_id)
            node_messages = messages[node_mask].unsqueeze(0)  # [1, seq_len, input_dim]
            
            # Create attention mask if not provided
            if attention_mask is not None:
                node_attention_mask = attention_mask[node_mask].unsqueeze(0)
            else:
                node_attention_mask = None
            
            # Apply transformer
            transformer_output = self.transformer(node_messages, src_key_padding_mask=node_attention_mask)
            
            # Apply mean pooling
            aggregated = torch.mean(transformer_output[0], dim=0)  # [input_dim]
            aggregated_messages.append(aggregated)
        
        output = torch.stack(aggregated_messages)
        output = self.output_projection(output)
        output = self.layer_norm(output)
        
        return output
    
    def _simple_aggregation(self, 
                           messages: torch.Tensor,
                           neighbor_indices: torch.Tensor) -> torch.Tensor:
        """Simple aggregation methods (mean, sum, max)."""
        num_nodes = int(neighbor_indices.max().item()) + 1
        
        # Manual scatter operations without torch_scatter dependency
        aggregated = torch.zeros(num_nodes, messages.size(-1), device=messages.device)
        
        for i in range(num_nodes):
            mask = (neighbor_indices == i)
            if mask.sum() > 0:
                node_messages = messages[mask]
                
                if self.aggregation_method == 'mean':
                    aggregated[i] = torch.mean(node_messages, dim=0)
                elif self.aggregation_method == 'sum':
                    aggregated[i] = torch.sum(node_messages, dim=0)
                elif self.aggregation_method == 'max':
                    aggregated[i] = torch.max(node_messages, dim=0)[0]
                else:
                    # Default to mean
                    aggregated[i] = torch.mean(node_messages, dim=0)
        
        # Project to hidden dimension if needed
        if aggregated.size(-1) != self.hidden_dim:
            if not hasattr(self, 'projection'):
                self.projection = nn.Linear(aggregated.size(-1), self.hidden_dim).to(aggregated.device)
            aggregated = self.projection(aggregated)
        
        aggregated = self.layer_norm(aggregated)
        
        return aggregated


class AttentionAggregation(nn.Module):
    """
    Single attention head for message aggregation.
    """
    
    def __init__(self, input_dim: int, hidden_dim: int, device: str = 'cpu'):
        """
        Initialize attention aggregation.
        
        Args:
            input_dim: Input feature dimension
            hidden_dim: Hidden dimension
            device: Device to run the model on
        """
        super(AttentionAggregation, self).__init__()
        
        self.input_dim = input_dim
        self.hidden_dim = hidden_dim
        
        # Attention components
        self.query_projection = nn.Linear(input_dim, hidden_dim)
        self.key_projection = nn.Linear(input_dim, hidden_dim)
        self.value_projection = nn.Linear(input_dim, hidden_dim)
        
        # Temporal attention
        self.temporal_projection = nn.Linear(1, hidden_dim)
        
        self.to(device)
    
    def forward(self, 
                messages: torch.Tensor,
                neighbor_indices: torch.Tensor,
                temporal_weights: torch.Tensor | None = None,
                attention_mask: torch.Tensor | None = None) -> torch.Tensor:
        """
        Apply attention-based aggregation.
        
        Args:
            messages: Messages to aggregate
            neighbor_indices: Node indices for messages
            temporal_weights: Optional temporal weights
            attention_mask: Optional attention mask
            
        Returns:
            Aggregated messages
        """
        unique_nodes = torch.unique(neighbor_indices)
        aggregated_messages = []
        
        for node_id in unique_nodes:
            # Get messages for this node
            node_mask = (neighbor_indices == node_id)
            node_messages = messages[node_mask]  # [num_neighbors, input_dim]
            
            if node_messages.size(0) == 1:
                # Only one neighbor, no need for attention
                aggregated = self.value_projection(node_messages[0])
                aggregated_messages.append(aggregated)
                continue
            
            # Compute queries, keys, values
            queries = self.query_projection(node_messages)  # [num_neighbors, hidden_dim]
            keys = self.key_projection(node_messages)
            values = self.value_projection(node_messages)
            
            # Compute attention weights
            attention_scores = torch.matmul(queries, keys.transpose(-2, -1))
            attention_scores = attention_scores / (self.hidden_dim ** 0.5)
            
            # Add temporal weights if provided
            if temporal_weights is not None:
                node_temporal_weights = temporal_weights[node_mask]
                temporal_attention = self.temporal_projection(
                    node_temporal_weights.unsqueeze(-1)
                )
                attention_scores = attention_scores + temporal_attention
            
            # Apply attention mask if provided
            if attention_mask is not None:
                node_attention_mask = attention_mask[node_mask]
                attention_scores = attention_scores.masked_fill(
                    node_attention_mask.unsqueeze(-1) == 0, -1e9
                )
            
            # Apply softmax
            attention_weights = F.softmax(attention_scores, dim=-1)
            
            # Aggregate values
            aggregated = torch.sum(attention_weights * values, dim=0)
            aggregated_messages.append(aggregated)
        
        return torch.stack(aggregated_messages)


class TemporalAggregationLayer(nn.Module):
    """
    Advanced temporal aggregation layer with multiple aggregation strategies.
    
    This layer combines different aggregation methods and includes temporal
    decay mechanisms for more sophisticated temporal modeling.
    """
    
    def __init__(self, 
                 feature_dim: int,
                 time_dim: int,
                 hidden_dim: int,
                 num_heads: int = 4,
                 aggregation_methods: list[str] = ['attention', 'mean'],
                 temporal_decay: bool = True,
                 device: str = 'cpu'):
        """
        Initialize temporal aggregation layer.
        
        Args:
            feature_dim: Feature dimension
            time_dim: Time encoding dimension
            hidden_dim: Hidden dimension
            num_heads: Number of attention heads
            aggregation_methods: List of aggregation methods to combine
            temporal_decay: Whether to apply temporal decay
            device: Device to run the model on
        """
        super(TemporalAggregationLayer, self).__init__()
        
        self.feature_dim = feature_dim
        self.time_dim = time_dim
        self.hidden_dim = hidden_dim
        self.aggregation_methods = aggregation_methods
        self.temporal_decay = temporal_decay
        
        # Create aggregators for each method
        self.aggregators = nn.ModuleDict()
        for method in aggregation_methods:
            self.aggregators[method] = MessageAggregator(
                feature_dim, hidden_dim, num_heads, method, device=device
            )
        
        # Combination layer
        if len(aggregation_methods) > 1:
            self.combination_layer = nn.Sequential(
                nn.Linear(hidden_dim * len(aggregation_methods), hidden_dim),
                nn.ReLU(),
                nn.Dropout(0.1),
                nn.Linear(hidden_dim, hidden_dim)
            )
        
        # Temporal decay mechanism
        if temporal_decay:
            self.decay_function = TemporalDecayFunction(time_dim, device)
        
        self.to(device)
    
    def forward(self, 
                node_features: torch.Tensor,
                neighbor_features: torch.Tensor,
                neighbor_indices: torch.Tensor,
                edge_times: torch.Tensor,
                current_time: torch.Tensor) -> torch.Tensor:
        """
        Perform temporal aggregation.
        
        Args:
            node_features: Features of target nodes
            neighbor_features: Features of neighbor nodes
            neighbor_indices: Indices of neighbors for each message
            edge_times: Timestamps of edges
            current_time: Current timestamp
            
        Returns:
            Aggregated features
        """
        # Apply temporal decay if enabled
        if self.temporal_decay:
            time_diffs = current_time - edge_times
            decay_weights = self.decay_function(time_diffs)
            neighbor_features = neighbor_features * decay_weights.unsqueeze(-1)
        
        # Apply each aggregation method
        aggregated_results = []
        for method_name, aggregator in self.aggregators.items():
            result = aggregator(neighbor_features, neighbor_indices)
            aggregated_results.append(result)
        
        # Combine results if multiple methods
        if len(aggregated_results) == 1:
            final_result = aggregated_results[0]
        else:
            combined = torch.cat(aggregated_results, dim=-1)
            final_result = self.combination_layer(combined)
        
        return final_result


class TemporalDecayFunction(nn.Module):
    """
    Learnable temporal decay function.
    
    This module learns how to weight historical information based on
    temporal distance, allowing the model to adaptively determine
    the importance of events at different time scales.
    """
    
    def __init__(self, time_dim: int, device: str = 'cpu'):
        """
        Initialize temporal decay function.
        
        Args:
            time_dim: Time encoding dimension
            device: Device to run the model on
        """
        super(TemporalDecayFunction, self).__init__()
        
        self.time_dim = time_dim
        
        # Learnable decay parameters
        self.decay_mlp = nn.Sequential(
            nn.Linear(1, time_dim),
            nn.ReLU(),
            nn.Linear(time_dim, time_dim // 2),
            nn.ReLU(),
            nn.Linear(time_dim // 2, 1),
            nn.Sigmoid()
        )
        
        self.to(device)
    
    def forward(self, time_diffs: torch.Tensor) -> torch.Tensor:
        """
        Compute temporal decay weights.
        
        Args:
            time_diffs: Time differences, shape [num_edges]
            
        Returns:
            Decay weights, shape [num_edges]
        """
        # Normalize time differences
        time_diffs_normalized = torch.clamp(time_diffs / (time_diffs.max() + 1e-8), 0, 1)
        
        # Compute decay weights
        decay_weights = self.decay_mlp(time_diffs_normalized.unsqueeze(-1))
        
        return decay_weights.squeeze(-1) 