"""
Main TGAT Model Implementation

This module implements the complete TGAT model for blockchain AML anomaly detection.
It combines all components (time encoding, memory, attention, layers) into a unified model.
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
from typing import Dict, Tuple, Optional, List
import numpy as np

from .tgat_layer import T<PERSON><PERSON>ayer, MultiLayerTGAT
from .memory_module import MemoryModule
from .time_encoding import TimeEncodingLayer


class TGATModel(nn.Module):
    """
    Complete TGAT model for blockchain transaction anomaly detection.
    
    This model processes temporal blockchain transaction graphs and predicts
    whether transactions are anomalous (potentially AML violations).
    """
    
    def __init__(self, 
                 node_feat_dim: int,
                 edge_feat_dim: int,
                 hidden_dim: int = 128,
                 time_dim: int = 32,
                 num_layers: int = 2,
                 num_heads: int = 8,
                 dropout: float = 0.1,
                 num_classes: int = 2,  # Binary classification: normal vs anomalous
                 max_nodes: int = 10000,
                 use_memory: bool = True,
                 memory_dim: int = 64,
                 aggregation_method: str = 'attention',
                 device: str = 'cpu'):
        """
        Initialize TGAT model.
        
        Args:
            node_feat_dim: Node feature dimension
            edge_feat_dim: Edge feature dimension  
            hidden_dim: Hidden dimension for TGAT layers
            time_dim: Time encoding dimension
            num_layers: Number of TGAT layers
            num_heads: Number of attention heads
            dropout: Dropout probability
            num_classes: Number of output classes
            max_nodes: Maximum number of nodes (for memory initialization)
            use_memory: Whether to use memory module
            memory_dim: Memory dimension
            aggregation_method: Message aggregation method
            device: Device to run the model on
        """
        super(TGATModel, self).__init__()
        
        self.node_feat_dim = node_feat_dim
        self.edge_feat_dim = edge_feat_dim
        self.hidden_dim = hidden_dim
        self.time_dim = time_dim
        self.num_layers = num_layers
        self.num_classes = num_classes
        self.use_memory = use_memory
        self.device = device
        
        # Node feature embedding
        self.node_embedding = nn.Sequential(
            nn.Linear(node_feat_dim, hidden_dim),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(hidden_dim, hidden_dim)
        )
        
        # Edge feature embedding (for blockchain transaction features)
        self.edge_embedding = nn.Sequential(
            nn.Linear(edge_feat_dim, hidden_dim // 2),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(hidden_dim // 2, hidden_dim // 2)
        )
        
        # Time encoding for temporal features
        self.time_encoder = TimeEncodingLayer(time_dim, device=device)
        
        # Memory module for historical information
        if use_memory:
            self.memory_module = MemoryModule(
                num_nodes=max_nodes,
                memory_dim=memory_dim,
                time_dim=time_dim,
                message_dim=hidden_dim,
                device=device
            )
        
        # Multi-layer TGAT
        self.tgat_layers = MultiLayerTGAT(
            input_dim=hidden_dim,
            hidden_dim=hidden_dim,
            time_dim=time_dim,
            num_layers=num_layers,
            num_heads=num_heads,
            dropout=dropout,
            use_memory=use_memory,
            memory_dim=memory_dim,
            device=device
        )
        
        # Transaction-level classifier
        self.transaction_classifier = nn.Sequential(
            nn.Linear(hidden_dim * 2, hidden_dim),  # src + dst node embeddings
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(hidden_dim, hidden_dim // 2),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(hidden_dim // 2, num_classes)
        )
        
        # Node-level classifier (optional)
        self.node_classifier = nn.Sequential(
            nn.Linear(hidden_dim, hidden_dim // 2),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(hidden_dim // 2, num_classes)
        )
        
        # Graph-level pooling and classifier
        self.graph_pooling = nn.Sequential(
            nn.Linear(hidden_dim, hidden_dim // 2),
            nn.ReLU(),
            nn.Dropout(dropout)
        )
        
        self.graph_classifier = nn.Sequential(
            nn.Linear(hidden_dim // 2, hidden_dim // 4),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(hidden_dim // 4, num_classes)
        )
        
        self.to(device)
    
    def forward(self, 
                x: torch.Tensor,
                edge_index: torch.Tensor,
                edge_attr: torch.Tensor,
                edge_time: torch.Tensor,
                node_time: torch.Tensor,
                transaction_edges: torch.Tensor | None = None) -> Dict[str, torch.Tensor]:
        """
        Forward pass of TGAT model.
        
        Args:
            x: Node features, shape [num_nodes, node_feat_dim]
            edge_index: Edge indices, shape [2, num_edges]
            edge_attr: Edge attributes, shape [num_edges, edge_feat_dim]
            edge_time: Edge timestamps, shape [num_edges]
            node_time: Node timestamps, shape [num_nodes]
            transaction_edges: Specific transaction edges to classify, shape [2, num_transactions]
            
        Returns:
            Dictionary containing:
            - node_embeddings: Node representations [num_nodes, hidden_dim]
            - transaction_predictions: Transaction anomaly scores [num_transactions, num_classes]
            - node_predictions: Node anomaly scores [num_nodes, num_classes]
            - graph_prediction: Graph-level anomaly score [1, num_classes]
        """
        # Embed node features
        node_embeddings = self.node_embedding(x)
        
        # Process through TGAT layers
        memory_module = self.memory_module if self.use_memory else None
        enhanced_embeddings = self.tgat_layers(
            node_embeddings, edge_index, edge_time, node_time, memory_module
        )
        
        # Update memory if using memory module
        if self.use_memory and memory_module is not None:
            # Compute messages for memory update
            src_nodes, dst_nodes = edge_index[0], edge_index[1]
            messages = memory_module.compute_messages(
                src_nodes, dst_nodes, edge_attr, edge_time
            )
            
            # Update memory for all involved nodes
            all_nodes = torch.cat([src_nodes, dst_nodes])
            all_messages = torch.cat([messages, messages])  # Same message for src and dst
            all_times = torch.cat([edge_time, edge_time])
            
            memory_module.update_memory(all_nodes, all_messages, all_times)
        
        # Transaction-level predictions
        transaction_predictions = None
        if transaction_edges is not None:
            src_embed = enhanced_embeddings[transaction_edges[0]]  # [num_transactions, hidden_dim]
            dst_embed = enhanced_embeddings[transaction_edges[1]]  # [num_transactions, hidden_dim]
            
            # Combine source and destination embeddings
            transaction_features = torch.cat([src_embed, dst_embed], dim=-1)
            transaction_predictions = self.transaction_classifier(transaction_features)
        
        # Node-level predictions
        node_predictions = self.node_classifier(enhanced_embeddings)
        
        # Graph-level prediction (global pooling)
        pooled_features = self.graph_pooling(enhanced_embeddings)
        graph_features = torch.mean(pooled_features, dim=0, keepdim=True)  # [1, hidden_dim//2]
        graph_prediction = self.graph_classifier(graph_features)
        
        return {
            'node_embeddings': enhanced_embeddings,
            'transaction_predictions': transaction_predictions,
            'node_predictions': node_predictions,
            'graph_prediction': graph_prediction
        }
    
    def predict_anomaly(self, 
                       x: torch.Tensor,
                       edge_index: torch.Tensor,
                       edge_attr: torch.Tensor,
                       edge_time: torch.Tensor,
                       node_time: torch.Tensor,
                       transaction_edges: torch.Tensor | None = None,
                       threshold: float = 0.5) -> Dict[str, torch.Tensor]:
        """
        Predict anomalies with probability scores.
        
        Args:
            x, edge_index, edge_attr, edge_time, node_time: Graph data
            transaction_edges: Specific transactions to evaluate
            threshold: Classification threshold
            
        Returns:
            Dictionary with anomaly predictions and scores
        """
        self.eval()
        with torch.no_grad():
            outputs = self.forward(x, edge_index, edge_attr, edge_time, node_time, transaction_edges)
            
            results = {}
            
            # Transaction anomaly predictions
            if outputs['transaction_predictions'] is not None:
                tx_probs = F.softmax(outputs['transaction_predictions'], dim=-1)
                tx_anomaly_scores = tx_probs[:, 1]  # Probability of being anomalous
                tx_predictions = (tx_anomaly_scores > threshold).long()
                
                results['transaction_anomaly_scores'] = tx_anomaly_scores
                results['transaction_predictions'] = tx_predictions
            
            # Node anomaly predictions
            node_probs = F.softmax(outputs['node_predictions'], dim=-1)
            node_anomaly_scores = node_probs[:, 1]
            node_predictions = (node_anomaly_scores > threshold).long()
            
            results['node_anomaly_scores'] = node_anomaly_scores
            results['node_predictions'] = node_predictions
            
            # Graph anomaly prediction
            graph_probs = F.softmax(outputs['graph_prediction'], dim=-1)
            graph_anomaly_score = graph_probs[0, 1]
            graph_prediction = (graph_anomaly_score > threshold).long()
            
            results['graph_anomaly_score'] = graph_anomaly_score
            results['graph_prediction'] = graph_prediction
            
            return results
    
    def get_attention_weights(self, 
                             x: torch.Tensor,
                             edge_index: torch.Tensor,
                             edge_time: torch.Tensor,
                             node_time: torch.Tensor) -> List[torch.Tensor]:
        """
        Extract attention weights for interpretability.
        
        Args:
            x, edge_index, edge_time, node_time: Graph data
            
        Returns:
            List of attention weight tensors for each layer
        """
        # This would need to be implemented by modifying the forward pass
        # to return attention weights from each layer
        pass
    
    def reset_memory(self):
        """Reset the memory module."""
        if self.use_memory and hasattr(self, 'memory_module'):
            self.memory_module.reset_memory()


class TGATForBlockchainAML(TGATModel):
    """
    Specialized TGAT model for blockchain AML detection.
    
    This model includes blockchain-specific features and preprocessing
    tailored for anti-money laundering detection in cryptocurrency transactions.
    """
    
    def __init__(self, 
                 node_feat_dim: int = 10,  # Address features
                 edge_feat_dim: int = 8,   # Transaction features
                 hidden_dim: int = 128,
                 time_dim: int = 32,
                 num_layers: int = 3,
                 num_heads: int = 8,
                 dropout: float = 0.1,
                 max_nodes: int = 50000,  # Large for blockchain networks
                 device: str = 'cpu'):
        """
        Initialize blockchain AML TGAT model.
        
        Args:
            node_feat_dim: Dimension of address features
            edge_feat_dim: Dimension of transaction features
            hidden_dim: Hidden dimension
            time_dim: Time encoding dimension
            num_layers: Number of TGAT layers
            num_heads: Number of attention heads
            dropout: Dropout probability
            max_nodes: Maximum number of addresses
            device: Device to run the model on
        """
        super(TGATForBlockchainAML, self).__init__(
            node_feat_dim=node_feat_dim,
            edge_feat_dim=edge_feat_dim,
            hidden_dim=hidden_dim,
            time_dim=time_dim,
            num_layers=num_layers,
            num_heads=num_heads,
            dropout=dropout,
            num_classes=2,  # Binary: normal vs suspicious
            max_nodes=max_nodes,
            use_memory=True,
            memory_dim=hidden_dim // 2,
            device=device
        )
        
        # Blockchain-specific feature processors
        self.address_feature_processor = self._create_address_processor()
        self.transaction_feature_processor = self._create_transaction_processor()
        
        # Risk scoring components
        self.risk_scorer = nn.Sequential(
            nn.Linear(hidden_dim, hidden_dim // 2),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(hidden_dim // 2, 1),
            nn.Sigmoid()
        )
    
    def _create_address_processor(self) -> nn.Module:
        """Create address feature processor."""
        return nn.Sequential(
            nn.Linear(self.node_feat_dim, self.hidden_dim // 2),
            nn.BatchNorm1d(self.hidden_dim // 2),
            nn.ReLU(),
            nn.Dropout(0.1),
            nn.Linear(self.hidden_dim // 2, self.hidden_dim // 2)
        )
    
    def _create_transaction_processor(self) -> nn.Module:
        """Create transaction feature processor."""
        return nn.Sequential(
            nn.Linear(self.edge_feat_dim, self.hidden_dim // 4),
            nn.BatchNorm1d(self.hidden_dim // 4),
            nn.ReLU(),
            nn.Dropout(0.1),
            nn.Linear(self.hidden_dim // 4, self.hidden_dim // 4)
        )
    
    def forward_blockchain_specific(self, 
                                   address_features: torch.Tensor,
                                   transaction_features: torch.Tensor,
                                   edge_index: torch.Tensor,
                                   timestamps: torch.Tensor,
                                   transaction_edges: torch.Tensor | None = None) -> Dict[str, torch.Tensor]:
        """
        Forward pass with blockchain-specific preprocessing.
        
        Args:
            address_features: Raw address features [num_addresses, node_feat_dim]
            transaction_features: Raw transaction features [num_transactions, edge_feat_dim]
            edge_index: Transaction graph edges [2, num_transactions]
            timestamps: Transaction timestamps [num_transactions]
            transaction_edges: Specific transactions to classify
            
        Returns:
            Model outputs with blockchain-specific insights
        """
        # Process features
        processed_addresses = self.address_feature_processor(address_features)
        processed_transactions = self.transaction_feature_processor(transaction_features)
        
        # Create node timestamps (use max timestamp for each address)
        num_nodes = address_features.size(0)
        node_time = torch.zeros(num_nodes, device=self.device)
        
        # Assign timestamps to nodes based on their latest transaction
        for i, (src, dst) in enumerate(edge_index.t()):
            node_time[src] = max(node_time[src].item(), timestamps[i].item())
            node_time[dst] = max(node_time[dst].item(), timestamps[i].item())
        
        # Run main TGAT forward pass
        outputs = self.forward(
            processed_addresses, edge_index, processed_transactions,
            timestamps, node_time, transaction_edges
        )
        
        # Add blockchain-specific risk scoring
        if outputs['node_embeddings'] is not None:
            risk_scores = self.risk_scorer(outputs['node_embeddings'])
            outputs['address_risk_scores'] = risk_scores.squeeze(-1)
        
        return outputs
    
    def detect_suspicious_patterns(self, 
                                  outputs: Dict[str, torch.Tensor],
                                  edge_index: torch.Tensor,
                                  transaction_amounts: torch.Tensor,
                                  threshold: float = 0.7) -> Dict[str, List[int]]:
        """
        Detect specific suspicious patterns in blockchain transactions.
        
        Args:
            outputs: Model outputs
            edge_index: Transaction edges
            transaction_amounts: Transaction amounts
            threshold: Suspicion threshold
            
        Returns:
            Dictionary of detected suspicious patterns
        """
        suspicious_patterns = {
            'high_risk_addresses': [],
            'suspicious_transactions': [],
            'potential_mixing': [],
            'large_value_suspicious': []
        }
        
        # High-risk addresses
        if 'address_risk_scores' in outputs:
            high_risk_mask = outputs['address_risk_scores'] > threshold
            suspicious_patterns['high_risk_addresses'] = torch.where(high_risk_mask)[0].tolist()
        
        # Suspicious transactions
        if 'transaction_predictions' in outputs and outputs['transaction_predictions'] is not None:
            tx_probs = F.softmax(outputs['transaction_predictions'], dim=-1)
            suspicious_tx_mask = tx_probs[:, 1] > threshold
            suspicious_patterns['suspicious_transactions'] = torch.where(suspicious_tx_mask)[0].tolist()
        
        # Large value suspicious transactions
        if 'transaction_predictions' in outputs and outputs['transaction_predictions'] is not None:
            tx_probs = F.softmax(outputs['transaction_predictions'], dim=-1)
            large_value_mask = transaction_amounts > transaction_amounts.median() * 5
            suspicious_large = (tx_probs[:, 1] > 0.5) & large_value_mask
            suspicious_patterns['large_value_suspicious'] = torch.where(suspicious_large)[0].tolist()
        
        return suspicious_patterns


def create_tgat_model_for_pipeline(device: str = 'cpu') -> TGATForBlockchainAML:
    """
    Create a TGAT model configured for the blockchain AML pipeline.
    
    Args:
        device: Device to run the model on
        
    Returns:
        Configured TGAT model ready for training/inference
    """
    model = TGATForBlockchainAML(
        node_feat_dim=10,    # Address features from graph construction layer
        edge_feat_dim=8,     # Transaction features from graph construction layer
        hidden_dim=128,      # Good balance of capacity and efficiency
        time_dim=32,         # Sufficient for temporal patterns
        num_layers=3,        # Deep enough for complex patterns
        num_heads=8,         # Multi-head attention
        dropout=0.15,        # Regularization for blockchain data
        max_nodes=100000,    # Large blockchain networks
        device=device
    )
    
    return model 