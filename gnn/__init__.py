"""
TGAT Model Layer for Blockchain AML Pipeline

This module implements Temporal Graph Attention Networks (TGAT) for anomaly detection
in blockchain transaction data. The implementation is based on the original TGAT paper:
"Inductive representation learning on temporal graphs" (ICLR 2020)

Components:
- TGATModel: Main TGAT model for temporal graph anomaly detection
- TGATLayer: Individual TGAT attention layer
- TimeEncodingLayer: Time encoding for temporal features
- MemoryModule: Memory module for node embeddings
- MessageAggregator: Message aggregation with temporal attention
"""

from .tgat_model import T<PERSON>TModel, TGATForBlockchainAML, create_tgat_model_for_pipeline
from .tgat_layer import TGATLayer
from .time_encoding import TimeEncodingLayer
from .memory_module import MemoryModule
from .message_aggregator import MessageAggregator

__all__ = [
    'TGATModel',
    'TGATForBlockchainAML',
    'create_tgat_model_for_pipeline',
    'TGATLayer', 
    'TimeEncodingLayer',
    'MemoryModule',
    'MessageAggregator'
]

__version__ = '1.0.0' 