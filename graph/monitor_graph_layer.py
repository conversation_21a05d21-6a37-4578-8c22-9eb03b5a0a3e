#!/usr/bin/env python3
"""
Monitor Graph Layer Processing

This script monitors the graph construction layer by:
1. Checking Kafka consumer group status
2. Monitoring input/output topics
3. Checking Spark UI metrics
4. Verifying graph construction
"""

import os
import sys
import json
import time
import logging
from datetime import datetime
from kafka import KafkaConsumer, KafkaAdminClient
from kafka.admin import NewTopic
from kafka.errors import TopicAlreadyExistsError
import requests

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def check_kafka_topics():
    """Check Kafka topics exist and have messages"""
    try:
        admin_client = KafkaAdminClient(
            bootstrap_servers='localhost:9092',
            client_id='graph-monitor'
        )
        
        # Get topic list
        topics = admin_client.list_topics()
        logger.info(f"Available topics: {topics}")
        
        # Check required topics
        required_topics = ['raw-transactions', 'filtered-transactions', 'graph-snapshots']
        missing_topics = [topic for topic in required_topics if topic not in topics]
        
        if missing_topics:
            logger.error(f"Missing topics: {missing_topics}")
            # Create missing topics
            for topic in missing_topics:
                try:
                    new_topic = NewTopic(
                        name=topic,
                        num_partitions=1,
                        replication_factor=1
                    )
                    admin_client.create_topics([new_topic])
                    logger.info(f"Created topic: {topic}")
                except TopicAlreadyExistsError:
                    pass
        
        admin_client.close()
        return True
        
    except Exception as e:
        logger.error(f"Error checking Kafka topics: {str(e)}")
        return False

def monitor_consumer_group():
    """Monitor the graph layer consumer group"""
    try:
        admin_client = KafkaAdminClient(
            bootstrap_servers='localhost:9092',
            client_id='graph-monitor'
        )
        
        # Get consumer group info
        consumer_groups = admin_client.list_consumer_groups()
        logger.info(f"Consumer groups: {consumer_groups}")
        
        # Check if our group exists
        group_id = 'graph-construction-group-1'
        group_found = any(group[0] == group_id for group in consumer_groups)
        
        if not group_found:
            logger.error(f"Consumer group {group_id} not found!")
            return False
            
        admin_client.close()
        return True
        
    except Exception as e:
        logger.error(f"Error monitoring consumer group: {str(e)}")
        return False

def check_spark_ui():
    """Check Spark UI metrics"""
    try:
        response = requests.get('http://localhost:4040/api/v1/applications')
        if response.status_code == 200:
            apps = response.json()
            logger.info(f"Active Spark applications: {apps}")
            return True
        else:
            logger.error(f"Failed to get Spark metrics: {response.status_code}")
            return False
            
    except Exception as e:
        logger.error(f"Error checking Spark UI: {str(e)}")
        return False

def monitor_graph_output():
    """Monitor graph construction output"""
    try:
        consumer = KafkaConsumer(
            'graph-snapshots',
            bootstrap_servers='localhost:9092',
            auto_offset_reset='latest',
            enable_auto_commit=True,
            group_id='graph-monitor',
            value_deserializer=lambda x: json.loads(x.decode('utf-8'))
        )
        
        logger.info("Monitoring graph snapshots...")
        for message in consumer:
            snapshot = message.value
            logger.info(f"Received graph snapshot:")
            logger.info(f"  Nodes: {snapshot['num_nodes']}")
            logger.info(f"  Edges: {snapshot['num_edges']}")
            logger.info(f"  Construction time: {snapshot['construction_time_ms']}ms")
            logger.info(f"  Window: {snapshot['window_start']} to {snapshot['window_end']}")
            
    except KeyboardInterrupt:
        logger.info("Stopping monitoring...")
        consumer.close()
    except Exception as e:
        logger.error(f"Error monitoring graph output: {str(e)}")
        return False

def main():
    """Main monitoring loop"""
    try:
        # Initial checks
        if not check_kafka_topics():
            logger.error("Failed to verify Kafka topics")
            sys.exit(1)
            
        if not monitor_consumer_group():
            logger.error("Failed to verify consumer group")
            sys.exit(1)
            
        if not check_spark_ui():
            logger.warning("Could not verify Spark UI - continuing anyway")
            
        # Monitor graph output
        monitor_graph_output()
        
    except KeyboardInterrupt:
        logger.info("Monitoring stopped by user")
    except Exception as e:
        logger.error(f"Fatal error: {str(e)}")
        sys.exit(1)

if __name__ == "__main__":
    main() 