#!/usr/bin/env python3
"""
Graph Snapshot Visualizer

This script reads graph snapshots from a Kafka topic and visualizes them using NetworkX and Matplotlib.
"""

import argparse
import json
import os
import subprocess
import time
from datetime import datetime
from typing import Dict, Any, List, Tuple

import matplotlib.pyplot as plt
import networkx as nx

def parse_args():
    """Parse command line arguments"""
    parser = argparse.ArgumentParser(description='Visualize graph snapshots from Kafka')
    parser.add_argument('--container', default='kafka-integrated', help='Kafka container name')
    parser.add_argument('--topic', default='graph-snapshots', help='Kafka topic to read from')
    parser.add_argument('--output-dir', default='./graph_visualizations', help='Directory to save visualizations')
    parser.add_argument('--max-snapshots', type=int, default=5, help='Maximum number of snapshots to visualize')
    parser.add_argument('--from-beginning', action='store_true', help='Read from the beginning of the topic')
    parser.add_argument('--interactive', action='store_true', help='Show interactive plots instead of saving to files')
    return parser.parse_args()

def get_snapshots_from_kafka(container: str, topic: str, from_beginning: bool, max_messages: int) -> List[Dict[str, Any]]:
    """Get graph snapshots from Kafka using docker exec"""
    cmd = [
        "docker", "exec", container, "kafka-console-consumer",
        "--bootstrap-server", "localhost:9092",
        "--topic", topic,
        "--max-messages", str(max_messages)
    ]
    
    if from_beginning:
        cmd.append("--from-beginning")
    
    try:
        result = subprocess.run(cmd, check=True, capture_output=True, text=True)
        lines = result.stdout.strip().split('\n')
        snapshots = []
        
        for line in lines:
            if line and not line.startswith('Processed a total of'):
                try:
                    snapshot = json.loads(line)
                    snapshots.append(snapshot)
                except json.JSONDecodeError as e:
                    print(f"Error parsing JSON: {str(e)}")
                    print(f"Line: {line}")
        
        return snapshots
    except subprocess.CalledProcessError as e:
        print(f"Error executing command: {str(e)}")
        print(f"stderr: {e.stderr}")
        return []

def create_graph_from_snapshot(snapshot: Dict[str, Any]) -> nx.Graph:
    """Create NetworkX graph from snapshot data"""
    G = nx.Graph()
    
    # Extract nodes and edges
    address_mapping = snapshot.get('address_mapping', {})
    
    # If address_mapping is empty, try to extract nodes from node_features
    if not address_mapping:
        print("No address mapping found, attempting to extract nodes from graph_data")
        node_features = snapshot.get('graph_data', {}).get('node_features', [])
        if node_features:
            for i in range(len(node_features)):
                address_mapping[str(i)] = f"Node_{i}"
    
    # Add nodes
    for idx, address in address_mapping.items():
        G.add_node(address, id=idx)
    
    # Add edges
    edge_index = snapshot.get('graph_data', {}).get('edge_index', [[], []])
    if len(edge_index) == 2 and len(edge_index[0]) == len(edge_index[1]):
        for i in range(len(edge_index[0])):
            if i < len(edge_index[0]) and i < len(edge_index[1]):
                source_idx = str(edge_index[0][i])
                target_idx = str(edge_index[1][i])
                if source_idx in address_mapping and target_idx in address_mapping:
                    source = address_mapping[source_idx]
                    target = address_mapping[target_idx]
                    G.add_edge(source, target)
    
    # If no edges were added but we know there should be edges, try direct indices
    if len(list(G.edges())) == 0 and snapshot.get('num_edges', 0) > 0:
        print("No edges mapped correctly, attempting to use direct indices")
        for i in range(len(edge_index[0])):
            if i < len(edge_index[0]) and i < len(edge_index[1]):
                source_idx = edge_index[0][i]
                target_idx = edge_index[1][i]
                source = f"Node_{source_idx}"
                target = f"Node_{target_idx}"
                G.add_node(source, id=source_idx)
                G.add_node(target, id=target_idx)
                G.add_edge(source, target)
    
    return G

def visualize_graph(G: nx.Graph, snapshot: Dict[str, Any], output_path=None, interactive=False):
    """Visualize the graph and save to file or display interactively"""
    plt.figure(figsize=(12, 8))
    
    # Get graph metadata
    window_start = snapshot.get('window_start', '')
    window_end = snapshot.get('window_end', '')
    num_nodes = snapshot.get('num_nodes', 0)
    num_edges = snapshot.get('num_edges', 0)
    
    # Set title
    plt.title(f"Transaction Graph Snapshot\n{window_start} to {window_end}\n{num_nodes} nodes, {num_edges} edges")
    
    if len(list(G.nodes())) == 0:
        plt.text(0.5, 0.5, "Empty Graph", horizontalalignment='center', verticalalignment='center', fontsize=20)
    else:
        # Use spring layout for node positioning
        pos = nx.spring_layout(G)
        
        # Draw nodes
        nx.draw_networkx_nodes(G, pos, node_size=300, node_color='skyblue')
        
        # Draw edges
        nx.draw_networkx_edges(G, pos, width=1.0, alpha=0.5)
        
        # Draw labels (shortened addresses)
        labels = {}
        for node in G.nodes():
            if isinstance(node, str) and len(node) > 10:
                labels[node] = node[:8] + '...'
            else:
                labels[node] = str(node)
        nx.draw_networkx_labels(G, pos, labels=labels, font_size=8)
    
    # Save figure or display interactively
    plt.tight_layout()
    if interactive:
        plt.show()
    elif output_path:
        plt.savefig(output_path, dpi=300)
        plt.close()
        print(f"Saved visualization to {output_path}")
    else:
        plt.close()

def main():
    """Main entry point"""
    args = parse_args()
    
    # Create output directory if it doesn't exist and not in interactive mode
    if not args.interactive:
        os.makedirs(args.output_dir, exist_ok=True)
    
    print(f"Getting graph snapshots from topic {args.topic}...")
    
    # Get snapshots from Kafka
    snapshots = get_snapshots_from_kafka(
        args.container, 
        args.topic, 
        args.from_beginning, 
        args.max_snapshots
    )
    
    if not snapshots:
        print("No snapshots received. Try using --from-beginning to read all snapshots.")
        return
    
    print(f"Retrieved {len(snapshots)} snapshots")
    
    # Process snapshots
    for i, snapshot in enumerate(snapshots):
        try:
            print(f"Processing snapshot {i+1}/{len(snapshots)} with {snapshot.get('num_nodes', 0)} nodes and {snapshot.get('num_edges', 0)} edges")
            
            # Create graph
            G = create_graph_from_snapshot(snapshot)
            print(f"Created graph with {len(list(G.nodes()))} nodes and {len(list(G.edges()))} edges")
            
            # Generate output filename
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            output_path = os.path.join(args.output_dir, f"graph_snapshot_{i+1}_{timestamp}.png") if not args.interactive else None
            
            # Visualize and save or display
            visualize_graph(G, snapshot, output_path, args.interactive)
            
        except Exception as e:
            print(f"Error processing snapshot: {str(e)}")
            import traceback
            traceback.print_exc()
    
    print(f"Processed {len(snapshots)} snapshots")

if __name__ == "__main__":
    main() 