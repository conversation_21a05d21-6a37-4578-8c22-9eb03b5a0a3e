#!/usr/bin/env python3
"""
Enhanced Multiple Graph Snapshots Visualizer

This script reads multiple graph snapshots from a JSON file and visualizes them using NetworkX and Matplotlib.
Features:
- Risk score-based node coloring (red for high risk, yellow for medium, green for low)
- Transaction amount-based edge thickness and color
- Detection of circular transactions (A->B->C->A)
- Detection of star patterns (one node connected to many others)
"""

import argparse
import json
import os
import random
from datetime import datetime
from typing import Dict, Any, List, Tuple, Set

import matplotlib.pyplot as plt
import matplotlib.cm as cm
import networkx as nx
import numpy as np

def parse_args():
    """Parse command line arguments"""
    parser = argparse.ArgumentParser(description='Visualize multiple graph snapshots from a file')
    parser.add_argument('--input-file', default='multiple_snapshots.json', help='Input JSON file with multiple graph snapshots')
    parser.add_argument('--output-dir', default='./graph_visualizations', help='Directory to save visualizations')
    parser.add_argument('--interactive', action='store_true', help='Show interactive plots instead of saving to files')
    parser.add_argument('--max-snapshots', type=int, default=5, help='Maximum number of snapshots to visualize')
    parser.add_argument('--highlight-patterns', action='store_true', help='Highlight suspicious patterns')
    parser.add_argument('--min-cycle-length', type=int, default=3, help='Minimum cycle length to detect')
    parser.add_argument('--max-cycle-length', type=int, default=5, help='Maximum cycle length to detect')
    parser.add_argument('--star-min-connections', type=int, default=5, help='Minimum connections to consider a node as star center')
    return parser.parse_args()

def load_snapshots_from_file(file_path: str) -> List[Dict[str, Any]]:
    """Load multiple graph snapshots from a JSON file"""
    try:
        with open(file_path, 'r') as f:
            content = f.read()
            lines = content.strip().split('\n')
            snapshots = []
            
            for line in lines:
                if line and not line.startswith('Processed a total of'):
                    try:
                        snapshot = json.loads(line)
                        snapshots.append(snapshot)
                    except json.JSONDecodeError as e:
                        print(f"Error parsing JSON: {str(e)}")
                        print(f"Line: {line}")
            
            return snapshots
    except Exception as e:
        print(f"Error loading snapshots from file: {str(e)}")
        return []

def create_graph_from_snapshot(snapshot: Dict[str, Any]) -> nx.DiGraph:
    """Create NetworkX directed graph from snapshot data with additional attributes"""
    G = nx.DiGraph()
    
    # Extract nodes and edges
    address_mapping = snapshot.get('address_mapping', {})
    
    # If address_mapping is empty, try to extract nodes from node_features
    if not address_mapping:
        print("No address mapping found, attempting to extract nodes from graph_data")
        node_features = snapshot.get('graph_data', {}).get('node_features', [])
        if node_features:
            for i in range(len(node_features)):
                address_mapping[str(i)] = f"Node_{i}"
    
    # Extract node features if available
    node_features = snapshot.get('graph_data', {}).get('node_features', [])
    
    # Add nodes with risk scores (either from features or random for demo)
    for idx, address in address_mapping.items():
        # Generate random risk score for demonstration (0-1)
        risk_score = random.uniform(0, 1)
        
        # Try to extract real features if available
        if node_features and int(idx) < len(node_features):
            # In a real system, we would extract risk score from node features
            # For now, we'll just use a random value
            pass
            
        G.add_node(address, id=idx, risk_score=risk_score)
    
    # Add edges with transaction amounts
    edge_index = snapshot.get('graph_data', {}).get('edge_index', [[], []])
    edge_attr = snapshot.get('graph_data', {}).get('edge_attr', [])
    
    if len(edge_index) == 2 and len(edge_index[0]) == len(edge_index[1]):
        for i in range(len(edge_index[0])):
            if i < len(edge_index[0]) and i < len(edge_index[1]):
                source_idx = str(edge_index[0][i])
                target_idx = str(edge_index[1][i])
                
                # Generate random transaction amount for demonstration
                tx_amount = random.uniform(0.1, 10)
                
                # Try to extract real transaction amount if available
                if edge_attr and i < len(edge_attr):
                    # In a real system, we would extract amount from edge_attr
                    # For now, we'll just use a random value
                    pass
                
                if source_idx in address_mapping and target_idx in address_mapping:
                    source = address_mapping[source_idx]
                    target = address_mapping[target_idx]
                    G.add_edge(source, target, weight=tx_amount)
    
    # If no edges were added but we know there should be edges, try direct indices
    if len(list(G.edges())) == 0 and snapshot.get('num_edges', 0) > 0:
        print("No edges mapped correctly, attempting to use direct indices")
        for i in range(len(edge_index[0])):
            if i < len(edge_index[0]) and i < len(edge_index[1]):
                source_idx = edge_index[0][i]
                target_idx = edge_index[1][i]
                source = f"Node_{source_idx}"
                target = f"Node_{target_idx}"
                
                # Generate random transaction amount for demonstration
                tx_amount = random.uniform(0.1, 10)
                
                G.add_node(source, id=source_idx, risk_score=random.uniform(0, 1))
                G.add_node(target, id=target_idx, risk_score=random.uniform(0, 1))
                G.add_edge(source, target, weight=tx_amount)
    
    return G

def detect_cycles(G: nx.DiGraph, min_length: int = 3, max_length: int = 5) -> List[List[str]]:
    """Detect cycles in the graph (circular transactions)"""
    cycles = []
    
    # Find all simple cycles
    try:
        for cycle in nx.simple_cycles(G):
            if min_length <= len(cycle) <= max_length:
                cycles.append(cycle)
    except nx.NetworkXNoCycle:
        # No cycles found
        pass
    
    return cycles

def detect_star_patterns(G: nx.DiGraph, min_connections: int = 5) -> List[str]:
    """Detect star patterns (one node connected to many others)"""
    star_centers = []
    
    for node in G.nodes():
        # Count both incoming and outgoing connections
        in_degree = G.in_degree(node)
        out_degree = G.out_degree(node)
        
        # Convert to int if needed
        if not isinstance(in_degree, int):
            in_degree = in_degree[1] if isinstance(in_degree, tuple) else 0
        if not isinstance(out_degree, int):
            out_degree = out_degree[1] if isinstance(out_degree, tuple) else 0
            
        total_connections = in_degree + out_degree
        
        if total_connections >= min_connections:
            star_centers.append(node)
    
    return star_centers

def detect_rapid_transactions(G: nx.DiGraph) -> List[Tuple[str, str]]:
    """
    In a real system, this would detect transactions happening in rapid succession.
    For this demo, we'll just randomly flag some edges.
    """
    rapid_txs = []
    edges = list(G.edges())
    
    # Randomly flag ~10% of edges as rapid transactions
    num_to_flag = max(1, int(len(edges) * 0.1))
    for _ in range(num_to_flag):
        if edges:
            edge = random.choice(edges)
            rapid_txs.append(edge)
            edges.remove(edge)
    
    return rapid_txs

def visualize_graph(G: nx.DiGraph, snapshot: Dict[str, Any], output_path=None, interactive=False, 
                   highlight_patterns=False, min_cycle_length=3, max_cycle_length=5, star_min_connections=5):
    """Visualize the graph with enhanced features and save to file or display interactively"""
    plt.figure(figsize=(14, 10))
    
    # Get graph metadata
    window_start = snapshot.get('window_start', '')
    window_end = snapshot.get('window_end', '')
    num_nodes = snapshot.get('num_nodes', 0)
    num_edges = snapshot.get('num_edges', 0)
    
    # Set title
    plt.title(f"Enhanced Transaction Graph Snapshot\n{window_start} to {window_end}\n{num_nodes} nodes, {num_edges} edges")
    
    if len(list(G.nodes())) == 0:
        plt.text(0.5, 0.5, "Empty Graph", horizontalalignment='center', verticalalignment='center', fontsize=20)
    else:
        # Use spring layout for node positioning
        pos = nx.spring_layout(G)
        
        # Prepare node colors based on risk scores
        node_colors = []
        for node in G.nodes():
            risk_score = G.nodes[node].get('risk_score', 0)
            if risk_score > 0.7:  # High risk
                node_colors.append('red')
            elif risk_score > 0.4:  # Medium risk
                node_colors.append('yellow')
            else:  # Low risk
                node_colors.append('green')
        
        # Draw nodes with risk-based colors
        # NetworkX accepts a list of colors for node_color parameter
        nx.draw_networkx_nodes(G, pos, node_size=300, node_color=node_colors, alpha=0.8)
        
        # Prepare edge attributes based on transaction amounts
        edge_weights = [G[u][v].get('weight', 1) for u, v in G.edges()]
        if edge_weights:
            max_weight = max(edge_weights) if edge_weights else 1
            normalized_weights = [w / max_weight * 5 for w in edge_weights]
        else:
            normalized_weights = [1.0] * len(G.edges())
        
        # Draw edges with varying thickness based on transaction amount
        # Only draw if there are edges
        if G.edges():
            nx.draw_networkx_edges(G, pos, width=normalized_weights, alpha=0.6, 
                              edge_color='blue', arrowsize=15)
        
        # Highlight suspicious patterns if requested
        if highlight_patterns:
            # Detect and highlight cycles (circular transactions)
            cycles = detect_cycles(G, min_cycle_length, max_cycle_length)
            for i, cycle in enumerate(cycles):
                cycle_edges = [(cycle[i], cycle[(i+1) % len(cycle)]) for i in range(len(cycle))]
                nx.draw_networkx_edges(G, pos, edgelist=cycle_edges, width=3.0, 
                                      edge_color='purple', style='dashed', alpha=0.8,
                                      label=f"Cycle {i+1}" if i == 0 else "")
            
            # Detect and highlight star patterns
            star_centers = detect_star_patterns(G, star_min_connections)
            nx.draw_networkx_nodes(G, pos, nodelist=star_centers, node_size=500,
                                  node_color='orange', node_shape='*', alpha=0.8,
                                  label="Star Center" if star_centers else "")
            
            # Detect and highlight rapid transactions
            rapid_txs = detect_rapid_transactions(G)
            nx.draw_networkx_edges(G, pos, edgelist=rapid_txs, width=2.0,
                                  edge_color='red', style='dotted', alpha=0.8,
                                  label="Rapid Tx" if rapid_txs else "")
        
        # Draw labels (shortened addresses)
        labels = {}
        for node in G.nodes():
            if isinstance(node, str) and len(node) > 10:
                labels[node] = node[:8] + '...'
            else:
                labels[node] = str(node)
        nx.draw_networkx_labels(G, pos, labels=labels, font_size=8)
        
        # Add legend
        legend_elements = [
            plt.Line2D([0], [0], marker='o', color='w', markerfacecolor='red', markersize=10, label='High Risk Node'),
            plt.Line2D([0], [0], marker='o', color='w', markerfacecolor='yellow', markersize=10, label='Medium Risk Node'),
            plt.Line2D([0], [0], marker='o', color='w', markerfacecolor='green', markersize=10, label='Low Risk Node')
        ]
        
        if highlight_patterns:
            legend_elements.extend([
                plt.Line2D([0], [0], color='purple', lw=2, linestyle='dashed', label='Circular Transaction'),
                plt.Line2D([0], [0], marker='*', color='w', markerfacecolor='orange', markersize=10, label='Star Pattern'),
                plt.Line2D([0], [0], color='red', lw=2, linestyle='dotted', label='Rapid Transaction')
            ])
        
        plt.legend(handles=legend_elements, loc='upper right')
    
    # Add summary of detected patterns
    if highlight_patterns and len(list(G.nodes())) > 0:
        cycles = detect_cycles(G, min_cycle_length, max_cycle_length)
        star_centers = detect_star_patterns(G, star_min_connections)
        rapid_txs = detect_rapid_transactions(G)
        
        summary_text = f"Detected Patterns:\n"
        summary_text += f"- {len(cycles)} circular transaction patterns\n"
        summary_text += f"- {len(star_centers)} star patterns\n"
        summary_text += f"- {len(rapid_txs)} rapid transactions"
        
        plt.figtext(0.02, 0.02, summary_text, wrap=True, horizontalalignment='left',
                   fontsize=10, bbox=dict(facecolor='white', alpha=0.8))
    
    # Save figure or display interactively
    plt.tight_layout()
    if interactive:
        plt.show()
    elif output_path:
        plt.savefig(output_path, dpi=300)
        plt.close()
        print(f"Saved visualization to {output_path}")
    else:
        plt.close()

def main():
    """Main entry point"""
    args = parse_args()
    
    # Create output directory if it doesn't exist and not in interactive mode
    if not args.interactive:
        os.makedirs(args.output_dir, exist_ok=True)
    
    print(f"Loading graph snapshots from file: {args.input_file}")
    
    # Load snapshots from file
    snapshots = load_snapshots_from_file(args.input_file)
    
    if not snapshots:
        print("Failed to load valid snapshots from file.")
        return
    
    print(f"Loaded {len(snapshots)} snapshots")
    
    # Limit the number of snapshots to process
    snapshots = snapshots[:args.max_snapshots]
    
    # Process snapshots
    for i, snapshot in enumerate(snapshots):
        try:
            print(f"Processing snapshot {i+1}/{len(snapshots)} with {snapshot.get('num_nodes', 0)} nodes and {snapshot.get('num_edges', 0)} edges")
            
            # Create graph
            G = create_graph_from_snapshot(snapshot)
            print(f"Created graph with {len(list(G.nodes()))} nodes and {len(list(G.edges()))} edges")
            
            # Generate output filename
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            output_path = os.path.join(args.output_dir, f"enhanced_graph_snapshot_{i+1}_{timestamp}.png") if not args.interactive else None
            
            # Visualize and save or display
            visualize_graph(G, snapshot, output_path, args.interactive,
                           args.highlight_patterns, args.min_cycle_length,
                           args.max_cycle_length, args.star_min_connections)
            
        except Exception as e:
            print(f"Error processing snapshot {i+1}: {str(e)}")
            import traceback
            traceback.print_exc()
    
    print(f"Processed {len(snapshots)} snapshots")

if __name__ == "__main__":
    main() 