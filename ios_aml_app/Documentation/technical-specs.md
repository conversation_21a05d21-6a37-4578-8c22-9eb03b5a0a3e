# iOS AML App - Technical Specifications

## 📋 Table of Contents
1. [Technology Stack](#technology-stack)
2. [Data Models](#data-models)
3. [API Specifications](#api-specifications)
4. [Performance Requirements](#performance-requirements)
5. [Security Specifications](#security-specifications)

---

## Technology Stack

### iOS Development Framework
- **Platform**: iOS 15.0+
- **Language**: Swift 5.7+
- **UI Framework**: SwiftUI 4.0+
- **Architecture**: MVVM (Model-View-ViewModel)
- **Reactive Framework**: Combine
- **Development Environment**: Xcode 14.0+

### Key Libraries & Frameworks
```swift
// Core iOS Frameworks
import Foundation
import SwiftUI
import Combine
import CoreData
import Network
import UserNotifications
import Security

// Third-party Dependencies
import Charts          // For data visualization
import WebSocket       // For real-time connections
import CryptoKit       // For encryption
import OSLog          // For structured logging
```

### Backend Integration
- **Authentication**: JWT tokens with Keychain storage
- **Networking**: URLSession with custom authentication interceptors
- **Real-time**: WebSocket connections with automatic reconnection
- **Local Storage**: Core Data with encryption
- **Push Notifications**: Apple Push Notification Service (APNs)

---

## Data Models

### Core Data Entities

#### Transaction Entity
```swift
@Model
class Transaction {
    @Attribute(.unique) var id: String
    var hash: String
    var fromAddress: String
    var toAddress: String
    var amount: Decimal
    var timestamp: Date
    var blockNumber: Int64
    var gasUsed: Int64
    var gasPrice: Decimal
    var riskScore: Double
    var isProcessed: Bool
    var metadata: Data? // JSON metadata
    
    // Relationships
    @Relationship(deleteRule: .cascade)
    var alerts: [Alert] = []
    
    @Relationship
    var connectedTransactions: [Transaction] = []
    
    init(id: String, hash: String, fromAddress: String, toAddress: String, 
         amount: Decimal, timestamp: Date) {
        self.id = id
        self.hash = hash
        self.fromAddress = fromAddress
        self.toAddress = toAddress
        self.amount = amount
        self.timestamp = timestamp
        self.blockNumber = 0
        self.gasUsed = 0
        self.gasPrice = 0
        self.riskScore = 0.0
        self.isProcessed = false
    }
}
```

#### Alert Entity
```swift
@Model
class Alert {
    @Attribute(.unique) var id: String
    var title: String
    var description: String
    var riskLevel: AlertRiskLevel
    var alertType: AlertType
    var timestamp: Date
    var isRead: Bool
    var isResolved: Bool
    var investigationNotes: String?
    var severity: AlertSeverity
    
    // Relationships
    @Relationship(inverse: \Transaction.alerts)
    var relatedTransactions: [Transaction] = []
    
    init(id: String, title: String, description: String, 
         riskLevel: AlertRiskLevel, alertType: AlertType) {
        self.id = id
        self.title = title
        self.description = description
        self.riskLevel = riskLevel
        self.alertType = alertType
        self.timestamp = Date()
        self.isRead = false
        self.isResolved = false
        self.severity = .medium
    }
}

enum AlertRiskLevel: String, CaseIterable, Codable {
    case low = "low"
    case medium = "medium"
    case high = "high"
    case critical = "critical"
}

enum AlertType: String, CaseIterable, Codable {
    case suspiciousPattern = "suspicious_pattern"
    case highRiskTransaction = "high_risk_transaction"
    case rapidSuccession = "rapid_succession"
    case unusualAmount = "unusual_amount"
    case blacklistedAddress = "blacklisted_address"
}

enum AlertSeverity: String, CaseIterable, Codable {
    case info = "info"
    case medium = "medium"
    case urgent = "urgent"
    case critical = "critical"
}
```

### API Response Models

#### Dashboard Metrics
```swift
struct DashboardMetrics: Codable {
    let transactionsPerSecond: Double
    let activeAlerts: Int
    let systemHealth: SystemHealthStatus
    let mlAccuracy: Double
    let cepThroughput: Double
    let timestamp: Date
    
    enum SystemHealthStatus: String, Codable {
        case healthy = "healthy"
        case degraded = "degraded"
        case critical = "critical"
    }
}

struct SystemMetrics: Codable {
    let cpuUsage: Double
    let memoryUsage: Double
    let networkLatency: Double
    let kafkaLag: Int
    let flinkUptime: TimeInterval
    let sparkJobsActive: Int
}
```

#### WebSocket Message Types
```swift
struct WebSocketMessage: Codable {
    let type: MessageType
    let timestamp: Date
    let data: Data
    
    enum MessageType: String, Codable {
        case transaction = "transaction"
        case alert = "alert"
        case metrics = "metrics"
        case heartbeat = "heartbeat"
        case system = "system"
    }
}

struct TransactionUpdate: Codable {
    let transaction: Transaction
    let graphContext: GraphContext?
    let riskAnalysis: RiskAnalysis?
}

struct AlertUpdate: Codable {
    let alert: Alert
    let relatedTransactionIds: [String]
    let priority: NotificationPriority
    
    enum NotificationPriority: String, Codable {
        case silent = "silent"
        case normal = "normal"
        case high = "high"
        case urgent = "urgent"
    }
}
```

---

## API Specifications

### Authentication Endpoints

#### Login
```http
POST /api/v1/auth/login
Content-Type: application/json

{
    "username": "<EMAIL>",
    "password": "secure_password",
    "device_id": "ios_device_uuid"
}

Response:
{
    "access_token": "eyJhbGciOiJIUzI1NiIs...",
    "refresh_token": "dGhpcyBpcyBhIHJlZnJlc2ggdG9rZW4...",
    "expires_in": 3600,
    "user": {
        "id": "user_123",
        "username": "<EMAIL>",
        "role": "investigator",
        "permissions": ["read_transactions", "manage_alerts"]
    }
}
```

#### Token Refresh
```http
POST /api/v1/auth/refresh
Content-Type: application/json
Authorization: Bearer <refresh_token>

Response:
{
    "access_token": "eyJhbGciOiJIUzI1NiIs...",
    "expires_in": 3600
}
```

### Dashboard Endpoints

#### Get Dashboard Metrics
```http
GET /api/v1/dashboard/metrics
Authorization: Bearer <access_token>

Response:
{
    "transactions_per_second": 150.5,
    "active_alerts": 23,
    "system_health": "healthy",
    "ml_accuracy": 0.94,
    "cep_throughput": 1200.0,
    "timestamp": "2024-01-15T10:30:00Z"
}
```

#### Get Recent Transactions
```http
GET /api/v1/transactions?limit=50&offset=0&filter=suspicious
Authorization: Bearer <access_token>

Response:
{
    "transactions": [
        {
            "id": "tx_123",
            "hash": "0xabc123...",
            "from_address": "0x123...",
            "to_address": "0x456...",
            "amount": "100.5",
            "timestamp": "2024-01-15T10:25:00Z",
            "risk_score": 0.85,
            "block_number": 12345678
        }
    ],
    "total": 150,
    "page": 0,
    "limit": 50
}
```

### Alert Management Endpoints

#### Get Alerts
```http
GET /api/v1/alerts?priority=high&status=unread&limit=20
Authorization: Bearer <access_token>

Response:
{
    "alerts": [
        {
            "id": "alert_456",
            "title": "Suspicious Transaction Pattern",
            "description": "Rapid succession of high-value transactions detected",
            "risk_level": "high",
            "alert_type": "suspicious_pattern",
            "timestamp": "2024-01-15T10:20:00Z",
            "is_read": false,
            "is_resolved": false,
            "related_transaction_ids": ["tx_123", "tx_124", "tx_125"]
        }
    ],
    "total": 23,
    "unread_count": 15
}
```

#### Update Alert Status
```http
PUT /api/v1/alerts/alert_456/status
Content-Type: application/json
Authorization: Bearer <access_token>

{
    "is_read": true,
    "is_resolved": false,
    "investigation_notes": "Initial review completed, requires further investigation"
}

Response:
{
    "success": true,
    "alert": {
        "id": "alert_456",
        "is_read": true,
        "is_resolved": false,
        "investigation_notes": "Initial review completed, requires further investigation",
        "updated_at": "2024-01-15T10:35:00Z"
    }
}
```

### WebSocket Connection

#### Connection Establishment
```javascript
// WebSocket URL with JWT authentication
wss://api.aml.com/ws?token=<jwt_token>

// Subscription message
{
    "action": "subscribe",
    "topics": ["transactions", "alerts", "metrics"],
    "user_id": "user_123"
}
```

#### Real-time Message Format
```json
{
    "type": "transaction",
    "timestamp": "2024-01-15T10:30:00Z",
    "data": {
        "transaction": {
            "id": "tx_789",
            "hash": "0xdef456...",
            "from_address": "0x789...",
            "to_address": "0xabc...",
            "amount": "50.0",
            "risk_score": 0.75,
            "timestamp": "2024-01-15T10:29:45Z"
        }
    }
}
```

---

## Performance Requirements

### Response Time Targets
| Operation | Target Latency | Maximum Acceptable |
|-----------|----------------|-------------------|
| App Launch | <2 seconds | 3 seconds |
| Authentication | <1 second | 2 seconds |
| Dashboard Load | <500ms | 1 second |
| WebSocket Connection | <100ms | 300ms |
| Alert Processing | <200ms | 500ms |
| Transaction Search | <1 second | 3 seconds |
| Graph Visualization | <2 seconds | 5 seconds |

### Memory Management
```swift
class PerformanceMonitor {
    private var memoryUsage: UInt64 = 0
    private let maxMemoryThreshold: UInt64 = 150 * 1024 * 1024 // 150MB
    
    func monitorMemoryUsage() {
        var info = mach_task_basic_info()
        var count = mach_msg_type_number_t(MemoryLayout<mach_task_basic_info>.size)/4
        
        let result = withUnsafeMutablePointer(to: &info) {
            $0.withMemoryRebound(to: integer_t.self, capacity: 1) {
                task_info(mach_task_self_, task_flavor_t(MACH_TASK_BASIC_INFO), $0, &count)
            }
        }
        
        if result == KERN_SUCCESS {
            memoryUsage = UInt64(info.resident_size)
            
            if memoryUsage > maxMemoryThreshold {
                performMemoryCleanup()
            }
        }
    }
    
    private func performMemoryCleanup() {
        // Clear old cached data
        CacheManager.shared.clearOldEntries()
        
        // Trim transaction history
        CoreDataManager.shared.trimOldTransactions()
        
        // Force garbage collection
        NSLog("Memory cleanup performed: \(memoryUsage / 1024 / 1024)MB")
    }
}
```

### Network Optimization
```swift
class NetworkOptimizer {
    private let maxConcurrentRequests = 4
    private let requestTimeoutInterval: TimeInterval = 30
    private let cachePolicy: URLRequest.CachePolicy = .returnCacheDataElseLoad
    
    lazy var urlSession: URLSession = {
        let config = URLSessionConfiguration.default
        config.httpMaximumConnectionsPerHost = maxConcurrentRequests
        config.timeoutIntervalForRequest = requestTimeoutInterval
        config.requestCachePolicy = cachePolicy
        config.urlCache = URLCache(
            memoryCapacity: 20 * 1024 * 1024,  // 20MB
            diskCapacity: 100 * 1024 * 1024,   // 100MB
            diskPath: "aml_network_cache"
        )
        return URLSession(configuration: config)
    }()
}
```

---

## Security Specifications

### Keychain Integration
```swift
class SecureTokenStorage {
    private let service = "com.aml.blockchain.app"
    private let accessGroup = "group.com.aml.shared"
    
    func storeToken(_ token: String, for key: String) throws {
        let data = token.data(using: .utf8)!
        
        let query: [String: Any] = [
            kSecClass as String: kSecClassGenericPassword,
            kSecAttrService as String: service,
            kSecAttrAccount as String: key,
            kSecAttrAccessGroup as String: accessGroup,
            kSecValueData as String: data,
            kSecAttrAccessible as String: kSecAttrAccessibleWhenUnlockedThisDeviceOnly
        ]
        
        SecItemDelete(query as CFDictionary)
        let status = SecItemAdd(query as CFDictionary, nil)
        
        guard status == errSecSuccess else {
            throw KeychainError.storageFailure(status)
        }
    }
    
    func retrieveToken(for key: String) throws -> String? {
        let query: [String: Any] = [
            kSecClass as String: kSecClassGenericPassword,
            kSecAttrService as String: service,
            kSecAttrAccount as String: key,
            kSecAttrAccessGroup as String: accessGroup,
            kSecReturnData as String: true,
            kSecMatchLimit as String: kSecMatchLimitOne
        ]
        
        var dataTypeRef: AnyObject?
        let status = SecItemCopyMatching(query as CFDictionary, &dataTypeRef)
        
        guard status == errSecSuccess,
              let data = dataTypeRef as? Data,
              let token = String(data: data, encoding: .utf8) else {
            return nil
        }
        
        return token
    }
}
```

### Network Security
```swift
class SecureNetworkManager: NSObject, URLSessionDelegate {
    private let pinnedCertificates: [SecCertificate]
    
    init(pinnedCertificates: [SecCertificate]) {
        self.pinnedCertificates = pinnedCertificates
        super.init()
    }
    
    func urlSession(
        _ session: URLSession,
        didReceive challenge: URLAuthenticationChallenge,
        completionHandler: @escaping (URLSession.AuthChallengeDisposition, URLCredential?) -> Void
    ) {
        guard let serverTrust = challenge.protectionSpace.serverTrust else {
            completionHandler(.cancelAuthenticationChallenge, nil)
            return
        }
        
        // Certificate pinning validation
        let serverCertificates = (0..<SecTrustGetCertificateCount(serverTrust)).compactMap {
            SecTrustGetCertificateAtIndex(serverTrust, $0)
        }
        
        let pinnedCertificateData = Set(pinnedCertificates.map { SecCertificateCopyData($0) })
        let serverCertificateData = Set(serverCertificates.map { SecCertificateCopyData($0) })
        
        if !pinnedCertificateData.isDisjoint(with: serverCertificateData) {
            completionHandler(.useCredential, URLCredential(trust: serverTrust))
        } else {
            completionHandler(.cancelAuthenticationChallenge, nil)
        }
    }
}
```

This technical specification provides the detailed implementation requirements for your iOS AML app, ensuring robust performance, security, and integration with your existing blockchain pipeline. 