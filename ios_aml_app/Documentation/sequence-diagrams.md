# iOS AML App - Sequence Diagrams Documentation

## 📋 Table of Contents
1. [Authentication Sequence](#authentication-sequence)
2. [Real-time Data Flow](#real-time-data-flow)
3. [Alert Processing Sequence](#alert-processing-sequence)
4. [Investigation Workflow](#investigation-workflow)
5. [Background Sync Process](#background-sync-process)

---

## Authentication Sequence

### Complete Authentication Flow

```mermaid
sequenceDiagram
    participant iOS as iOS AML App
    participant API as REST API Gateway
    participant WS as WebSocket Service
    participant <PERSON><PERSON><PERSON> as Kafka Consumer
    participant Pipeline as AML Pipeline
    
    Note over iOS,Pipeline: App Launch & Authentication
    iOS->>API: POST /auth/login
    API-->>iOS: JWT Token + User Profile
    
    Note over iOS,Pipeline: Initial Data Load
    iOS->>API: GET /dashboard/metrics
    API->>Kafka: Query latest metrics
    Kafka-->>API: Aggregated data
    API-->>iOS: Dashboard data
    
    Note over iOS,Pipeline: WebSocket Connection
    iOS->>WS: Connect with JWT
    WS-->>iOS: Connection established
    
    Note over iOS,Pipeline: Real-time Data Flow
    Pipeline->>Kafka: New transaction processed
    Kafka->>WS: Push to WebSocket
    WS->>iOS: Real-time transaction data
    iOS->>iOS: Update UI & Store locally
    
    Note over iOS,Pipeline: Alert Processing
    Pipeline->>Kafka: Suspicious pattern detected
    Kafka->>WS: Alert notification
    WS->>iOS: Alert payload
    iOS->>iOS: Show notification + Update badge
    
    Note over iOS,Pipeline: User Interaction
    iOS->>API: GET /transactions/{id}/details
    API->>Kafka: Query transaction history
    Kafka-->>API: Transaction + Graph data
    API-->>iOS: Detailed transaction info
    
    Note over iOS,Pipeline: Background Sync
    iOS->>API: GET /alerts/unread
    API->>Kafka: Query pending alerts
    Kafka-->>API: Alert list
    API-->>iOS: Unread alerts
    iOS->>iOS: Update badge count
    
    Note over iOS,Pipeline: Settings Update
    iOS->>API: PUT /user/settings
    API-->>iOS: Settings updated
    iOS->>WS: Update subscription preferences
    WS-->>iOS: Subscription confirmed
```

### Detailed Authentication Process

```mermaid
sequenceDiagram
    participant User as User
    participant App as iOS App
    participant Keychain as iOS Keychain
    participant API as Auth API
    participant Backend as Backend Services
    
    User->>App: Launch App
    App->>Keychain: Check for stored tokens
    
    alt Tokens exist and valid
        Keychain-->>App: Return JWT token
        App->>API: Validate token
        API-->>App: Token valid
        App->>App: Initialize services
        App->>User: Show Dashboard
    else Tokens expired or missing
        Keychain-->>App: No valid tokens
        App->>User: Show Login Screen
        User->>App: Enter credentials
        App->>API: POST /auth/login
        
        alt Authentication successful
            API-->>App: JWT + Refresh tokens
            App->>Keychain: Store tokens securely
            App->>Backend: Initialize WebSocket connection
            Backend-->>App: Connection established
            App->>User: Show Dashboard
        else Authentication failed
            API-->>App: 401 Unauthorized
            App->>User: Show error message
            User->>App: Retry login
        end
    end
```

---

## Real-time Data Flow

### WebSocket Data Streaming

```mermaid
sequenceDiagram
    participant Pipeline as AML Pipeline
    participant Kafka as Kafka Topics
    participant Bridge as Kafka-WS Bridge
    participant WS as WebSocket Server
    participant iOS as iOS App
    participant UI as User Interface
    
    Note over Pipeline,UI: Transaction Processing Flow
    Pipeline->>Kafka: Publish new transaction
    Kafka->>Bridge: Consume transaction event
    Bridge->>Bridge: Process and format data
    Bridge->>WS: Send to WebSocket clients
    WS->>iOS: Push transaction update
    iOS->>iOS: Validate and cache data
    iOS->>UI: Update transaction stream
    UI->>UI: Animate new transaction card
    
    Note over Pipeline,UI: Alert Generation Flow
    Pipeline->>Kafka: Publish suspicious pattern alert
    Kafka->>Bridge: Consume alert event
    Bridge->>Bridge: Determine alert priority
    Bridge->>WS: Send high-priority alert
    WS->>iOS: Push alert notification
    iOS->>iOS: Store alert locally
    iOS->>iOS: Schedule local notification
    iOS->>UI: Update alert badge
    iOS->>UI: Show in-app notification
    
    Note over Pipeline,UI: Metrics Update Flow
    Pipeline->>Kafka: Publish system metrics
    Kafka->>Bridge: Consume metrics event
    Bridge->>WS: Send metrics update
    WS->>iOS: Push metrics data
    iOS->>UI: Update dashboard charts
    UI->>UI: Animate chart transitions
```

### Real-time Update Processing

```mermaid
sequenceDiagram
    participant WS as WebSocket
    participant Processor as Data Processor
    participant Cache as Local Cache
    participant CoreData as Core Data
    participant ViewModel as View Models
    participant Views as SwiftUI Views
    
    WS->>Processor: Receive WebSocket message
    Processor->>Processor: Parse JSON message
    
    alt Transaction Update
        Processor->>Cache: Update memory cache
        Processor->>CoreData: Store transaction
        Processor->>ViewModel: Notify transaction update
        ViewModel->>Views: Trigger UI refresh
        Views->>Views: Animate transaction list
    else Alert Update
        Processor->>CoreData: Store alert
        Processor->>Cache: Update alert cache
        Processor->>Processor: Check alert priority
        
        alt High Priority Alert
            Processor->>iOS: Schedule local notification
            Processor->>ViewModel: Update alert badge
            ViewModel->>Views: Show alert indicator
        else Normal Priority
            Processor->>ViewModel: Update alert list
            ViewModel->>Views: Refresh alert view
        end
    else Metrics Update
        Processor->>Cache: Update metrics cache
        Processor->>ViewModel: Update dashboard metrics
        ViewModel->>Views: Refresh charts
        Views->>Views: Animate metric changes
    end
```

---

## Alert Processing Sequence

### Alert Lifecycle Management

```mermaid
sequenceDiagram
    participant ML as ML Pipeline
    participant CEP as Flink CEP
    participant Kafka as Kafka Broker
    participant API as API Gateway
    participant iOS as iOS App
    participant User as Investigator
    
    Note over ML,User: Alert Generation
    ML->>CEP: Suspicious transaction detected
    CEP->>CEP: Apply complex event patterns
    CEP->>Kafka: Publish alert to topic
    Kafka->>API: Alert consumed by API
    API->>iOS: Push alert via WebSocket
    
    Note over ML,User: Alert Notification
    iOS->>iOS: Process incoming alert
    iOS->>iOS: Determine notification strategy
    
    alt App in foreground
        iOS->>User: Show in-app notification
        iOS->>iOS: Update alert badge
    else App in background
        iOS->>iOS: Schedule push notification
        iOS->>User: Show system notification
    end
    
    Note over ML,User: Alert Investigation
    User->>iOS: Tap on alert notification
    iOS->>API: GET /alerts/{id}/details
    API->>Kafka: Query alert details
    Kafka-->>API: Alert + related transactions
    API-->>iOS: Detailed alert information
    iOS->>User: Show alert detail view
    
    Note over ML,User: Investigation Actions
    User->>iOS: Start investigation
    iOS->>API: GET /transactions/graph/{id}
    API-->>iOS: Transaction graph data
    iOS->>User: Show graph visualization
    
    User->>iOS: Mark alert as reviewed
    iOS->>API: PUT /alerts/{id}/status
    API->>Kafka: Update alert status
    API-->>iOS: Status updated confirmation
    iOS->>User: Show investigation complete
```

### Push Notification Handling

```mermaid
sequenceDiagram
    participant APNs as Apple Push Service
    participant iOS as iOS App
    participant NotifManager as Notification Manager
    participant CoreData as Core Data
    participant UI as User Interface
    
    Note over APNs,UI: Background Notification
    APNs->>iOS: Push notification received
    iOS->>NotifManager: Handle notification payload
    NotifManager->>NotifManager: Parse alert data
    NotifManager->>CoreData: Store alert locally
    NotifManager->>iOS: Update app badge
    iOS->>UI: Show notification banner
    
    Note over APNs,UI: User Interaction
    UI->>iOS: User taps notification
    iOS->>NotifManager: Handle notification response
    NotifManager->>NotifManager: Extract alert ID
    NotifManager->>iOS: Navigate to alert detail
    iOS->>UI: Present alert view
    
    Note over APNs,UI: Background Processing
    NotifManager->>CoreData: Query unread alerts
    CoreData-->>NotifManager: Return alert count
    NotifManager->>iOS: Update badge number
    iOS->>APNs: Update badge on home screen
```

---

## Investigation Workflow

### Complete Investigation Process

```mermaid
sequenceDiagram
    participant Investigator as Investigator
    participant App as iOS App
    participant API as API Gateway
    participant Graph as Graph Service
    participant ML as ML Explainer
    participant Report as Report Generator
    
    Note over Investigator,Report: Investigation Initiation
    Investigator->>App: Select high-risk alert
    App->>API: GET /alerts/{id}/investigation
    API->>Graph: Fetch transaction network
    Graph-->>API: Return graph topology
    API-->>App: Investigation data package
    App->>Investigator: Show investigation dashboard
    
    Note over Investigator,Report: Pattern Analysis
    Investigator->>App: Request transaction graph
    App->>API: GET /transactions/{id}/graph
    API->>Graph: Build transaction subgraph
    Graph->>ML: Request explainability data
    ML-->>Graph: Return node/edge explanations
    Graph-->>API: Graph + explanations
    API-->>App: Interactive graph data
    App->>Investigator: Display graph visualization
    
    Note over Investigator,Report: Deep Dive Analysis
    Investigator->>App: Drill down on suspicious node
    App->>API: GET /transactions/{id}/relationships
    API->>Graph: Analyze connected transactions
    Graph-->>API: Relationship analysis
    API-->>App: Related transaction cluster
    App->>Investigator: Show expanded network
    
    Note over Investigator,Report: Evidence Collection
    Investigator->>App: Flag suspicious transactions
    App->>API: POST /investigation/evidence
    API->>Report: Store evidence markers
    Investigator->>App: Add investigation notes
    App->>API: POST /investigation/notes
    API->>Report: Store investigation notes
    
    Note over Investigator,Report: Investigation Completion
    Investigator->>App: Complete investigation
    App->>API: PUT /alerts/{id}/resolve
    API->>Report: Generate investigation report
    Report-->>API: Investigation summary
    API-->>App: Resolution confirmation
    App->>Investigator: Show completion status
```

### Graph Exploration Sequence

```mermaid
sequenceDiagram
    participant User as User
    participant GraphView as Graph View
    participant ViewModel as Graph ViewModel
    participant API as API Service
    participant GraphDB as Graph Database
    
    Note over User,GraphDB: Initial Graph Load
    User->>GraphView: Select transaction for graph view
    GraphView->>ViewModel: Request graph data
    ViewModel->>API: GET /graph/transaction/{id}
    API->>GraphDB: Query transaction subgraph
    GraphDB-->>API: Return graph structure
    API-->>ViewModel: Graph nodes + edges
    ViewModel->>GraphView: Update graph display
    GraphView->>User: Show interactive graph
    
    Note over User,GraphDB: Node Interaction
    User->>GraphView: Tap on suspicious node
    GraphView->>ViewModel: Request node details
    ViewModel->>API: GET /transactions/{nodeId}/details
    API->>GraphDB: Fetch transaction details
    GraphDB-->>API: Transaction metadata
    API-->>ViewModel: Detailed transaction info
    ViewModel->>GraphView: Show node details panel
    GraphView->>User: Display transaction details
    
    Note over User,GraphDB: Graph Expansion
    User->>GraphView: Request expand node connections
    GraphView->>ViewModel: Expand graph around node
    ViewModel->>API: GET /graph/expand/{nodeId}
    API->>GraphDB: Query connected subgraph
    GraphDB-->>API: Additional nodes + edges
    API-->>ViewModel: Extended graph data
    ViewModel->>GraphView: Animate graph expansion
    GraphView->>User: Show expanded network
    
    Note over User,GraphDB: Risk Analysis
    User->>GraphView: Request risk scoring
    GraphView->>ViewModel: Calculate path risks
    ViewModel->>API: POST /graph/analyze/risk
    API->>GraphDB: Run risk algorithms
    GraphDB-->>API: Risk scores per path
    API-->>ViewModel: Risk analysis results
    ViewModel->>GraphView: Highlight risky paths
    GraphView->>User: Show color-coded risk levels
```

---

## Background Sync Process

### Background Data Synchronization

```mermaid
sequenceDiagram
    participant iOS as iOS App
    participant BGTask as Background Task
    participant API as API Gateway
    participant CoreData as Core Data
    participant Push as Push Service
    
    Note over iOS,Push: App Backgrounding
    iOS->>BGTask: App enters background
    BGTask->>BGTask: Start background task
    BGTask->>API: Sync pending data
    
    Note over iOS,Push: Data Synchronization
    API->>BGTask: Return pending alerts
    BGTask->>CoreData: Store new alerts
    BGTask->>BGTask: Calculate unread count
    BGTask->>iOS: Update app badge
    
    API->>BGTask: Return updated metrics
    BGTask->>CoreData: Cache metrics data
    
    Note over iOS,Push: Cleanup and Optimization
    BGTask->>CoreData: Clean old cached data
    BGTask->>CoreData: Optimize database
    BGTask->>BGTask: Prepare for next sync
    BGTask->>iOS: Complete background task
    
    Note over iOS,Push: Critical Alert Handling
    API->>Push: Critical alert detected
    Push->>iOS: Send push notification
    iOS->>BGTask: Wake app for processing
    BGTask->>CoreData: Store critical alert
    BGTask->>iOS: Update badge immediately
    iOS->>Push: Confirm alert received
```

### WebSocket Connection Management

```mermaid
sequenceDiagram
    participant App as iOS App
    participant WSManager as WebSocket Manager
    participant Server as WebSocket Server
    participant Monitor as Connection Monitor
    
    Note over App,Monitor: Connection Establishment
    App->>WSManager: Initialize WebSocket
    WSManager->>Server: Connect with JWT token
    Server-->>WSManager: Connection established
    WSManager->>Monitor: Start heartbeat monitoring
    WSManager->>App: Connection ready
    
    Note over App,Monitor: Normal Operation
    loop Real-time Updates
        Server->>WSManager: Push data update
        WSManager->>App: Process update
        App->>App: Update UI
        
        Monitor->>Server: Send heartbeat ping
        Server-->>Monitor: Heartbeat pong
    end
    
    Note over App,Monitor: Connection Issues
    Monitor->>Monitor: Detect connection loss
    Monitor->>WSManager: Trigger reconnection
    WSManager->>Server: Attempt reconnect
    
    alt Reconnection successful
        Server-->>WSManager: Connection restored
        WSManager->>App: Resume real-time updates
        Monitor->>Monitor: Reset failure count
    else Reconnection failed
        WSManager->>Monitor: Increment failure count
        Monitor->>Monitor: Exponential backoff delay
        Monitor->>WSManager: Schedule retry
    end
    
    Note over App,Monitor: Background Handling
    App->>WSManager: App entering background
    WSManager->>Server: Switch to background mode
    Server-->>WSManager: Acknowledge mode switch
    WSManager->>Monitor: Adjust heartbeat frequency
    
    App->>WSManager: App becoming active
    WSManager->>Server: Resume foreground mode
    Server-->>WSManager: Resume full updates
    WSManager->>App: Sync missed data
```

---

These sequence diagrams provide detailed interaction flows for all major processes in your iOS AML app, from authentication and real-time data handling to investigation workflows and background processing. They serve as a comprehensive guide for implementing the complex coordination between iOS app components and backend services. 