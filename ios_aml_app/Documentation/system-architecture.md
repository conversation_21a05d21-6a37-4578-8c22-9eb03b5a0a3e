# iOS AML App - System Architecture Documentation

## 📋 Table of Contents
1. [Overall System Architecture](#overall-system-architecture)
2. [iOS App Internal Architecture](#ios-app-internal-architecture)
3. [Backend Integration Layer](#backend-integration-layer)
4. [Data Flow Architecture](#data-flow-architecture)
5. [Security Architecture](#security-architecture)
6. [Performance Architecture](#performance-architecture)

---

## Overall System Architecture

### High-Level Integration Diagram

```mermaid
graph TB
    subgraph "iOS AML App Architecture"
        subgraph "Presentation Layer"
            A1[Dashboard View] --> A2[Real-time Charts]
            A1 --> A3[Alert Center]
            A1 --> A4[Transaction Explorer]
            A5[Settings View] --> A6[Service Config]
            A5 --> A7[Notification Settings]
            A8[Profile View] --> A9[User Preferences]
        end
        
        subgraph "Business Logic Layer"
            B1[AML Manager] --> B2[Alert Handler]
            B1 --> B3[Transaction Processor]
            B4[Network Manager] --> B5[API Client]
            B4 --> B6[WebSocket Client]
            B7[Data Manager] --> B8[Core Data Stack]
            B7 --> B9[Cache Manager]
        end
        
        subgraph "Data Layer"
            C1[Local Database<br/>Core Data] --> C2[Transaction Entity]
            C1 --> C3[Alert Entity]
            C1 --> C4[User Settings Entity]
            C5[Network Layer] --> C6[REST API Client]
            C5 --> C7[WebSocket Manager]
            C5 --> C8[Push Notification Handler]
        end
    end
    
    subgraph "Backend Services Integration"
        D1[AML REST API Gateway<br/>Port: 8002] --> D2[Transaction Endpoints]
        D1 --> D3[Alert Endpoints]
        D1 --> D4[Analytics Endpoints]
        D5[WebSocket Service<br/>Port: 8003] --> D6[Real-time Transactions]
        D5 --> D7[Live Alerts]
        D8[Push Notification Service] --> D9[APNs Integration]
    end
    
    subgraph "Existing Pipeline Integration"
        E1[Kafka Consumer<br/>Python Service] --> E2[Transaction Topic]
        E1 --> E3[Alert Topic]
        E4[Flink CEP Results] --> E1
        E5[Graph Layer Output] --> E1
        E6[ML Inference Results] --> E1
    end
    
    %% iOS App to Backend Connections
    B5 -.->|HTTPS REST| D1
    B6 -.->|WSS| D5
    C8 -.->|APNs| D8
    
    %% Backend to Pipeline Connections
    D1 -.->|Consume| E1
    D5 -.->|Stream| E1
    
    %% Styling
    classDef iosLayer fill:#007AFF,stroke:#0051D5,stroke-width:2px,color:#fff
    classDef backendLayer fill:#34C759,stroke:#248A3D,stroke-width:2px,color:#fff
    classDef pipelineLayer fill:#FF9500,stroke:#CC7700,stroke-width:2px,color:#fff
    
    class A1,A2,A3,A4,A5,A6,A7,A8,A9,B1,B2,B3,B4,B5,B6,B7,B8,B9,C1,C2,C3,C4,C5,C6,C7,C8 iosLayer
    class D1,D2,D3,D4,D5,D6,D7,D8,D9 backendLayer
    class E1,E2,E3,E4,E5,E6 pipelineLayer
```

### Integration Points

#### 1. **Data Ingestion Chain**
```
Ethereum Sepolia → Infura API → Kafka Streams → Flink CEP → Graph Construction → ML Inference
                                      ↓
                              Kafka Consumer Service → REST API Gateway
                                      ↓                       ↓
                              WebSocket Service ←→ iOS AML App
```

#### 2. **Service Communication**
- **Synchronous**: REST API calls for data retrieval and configuration
- **Asynchronous**: WebSocket connections for real-time updates
- **Push Notifications**: APNs for critical alerts when app is backgrounded

#### 3. **Data Persistence Strategy**
- **Local Cache**: Core Data for offline capability (1000 recent transactions)
- **Real-time Sync**: WebSocket updates with local database synchronization
- **Background Sync**: Periodic REST API calls when app is backgrounded

---

## iOS App Internal Architecture

### Technical Implementation Diagram

```mermaid
graph TD
    subgraph "iOS App Technical Architecture"
        subgraph "SwiftUI Views"
            V1[ContentView] --> V2[DashboardView]
            V1 --> V3[AlertsView]
            V1 --> V4[ExplorerView]
            V1 --> V5[AnalyticsView]
            V1 --> V6[SettingsView]
            
            V2 --> V7[MetricsCardView]
            V2 --> V8[TransactionStreamView]
            V3 --> V9[AlertListView]
            V3 --> V10[AlertDetailView]
            V4 --> V11[SearchView]
            V4 --> V12[TransactionGraphView]
        end
        
        subgraph "View Models (ObservableObject)"
            VM1[DashboardViewModel] --> VM2[MetricsData]
            VM1 --> VM3[TransactionStream]
            VM4[AlertViewModel] --> VM5[AlertList]
            VM4 --> VM6[AlertFilters]
            VM7[ExplorerViewModel] --> VM8[SearchResults]
            VM7 --> VM9[GraphData]
        end
        
        subgraph "Services"
            S1[AMLAPIService] --> S2[HTTPClient]
            S1 --> S3[AuthenticationManager]
            S4[WebSocketService] --> S5[Socket Connection]
            S4 --> S6[Message Handler]
            S7[CoreDataService] --> S8[Data Stack]
            S7 --> S9[Entity Management]
            S10[NotificationService] --> S11[Push Notifications]
            S10 --> S12[Local Notifications]
        end
        
        subgraph "Data Models"
            M1[Transaction] --> M2[Core Data Entity]
            M3[Alert] --> M4[Core Data Entity]
            M5[UserSettings] --> M6[UserDefaults]
            M7[APIResponse] --> M8[Codable Structs]
        end
        
        subgraph "Network Layer"
            N1[URLSession] --> N2[REST API Calls]
            N3[WebSocket Client] --> N4[Real-time Updates]
            N5[Authentication Interceptor] --> N6[JWT Token Management]
        end
    end
    
    %% Connections
    V2 --> VM1
    V3 --> VM4
    V4 --> VM7
    VM1 --> S1
    VM1 --> S4
    VM4 --> S1
    VM7 --> S1
    S1 --> N1
    S4 --> N3
    S7 --> M1
    S7 --> M3
    N1 --> N5
    
    %% Styling
    classDef viewLayer fill:#007AFF,stroke:#0051D5,stroke-width:2px,color:#fff
    classDef viewModelLayer fill:#34C759,stroke:#248A3D,stroke-width:2px,color:#fff
    classDef serviceLayer fill:#FF9500,stroke:#CC7700,stroke-width:2px,color:#fff
    classDef dataLayer fill:#AF52DE,stroke:#8E44AD,stroke-width:2px,color:#fff
    classDef networkLayer fill:#FF2D92,stroke:#D70A53,stroke-width:2px,color:#fff
    
    class V1,V2,V3,V4,V5,V6,V7,V8,V9,V10,V11,V12 viewLayer
    class VM1,VM2,VM3,VM4,VM5,VM6,VM7,VM8,VM9 viewModelLayer
    class S1,S2,S3,S4,S5,S6,S7,S8,S9,S10,S11,S12 serviceLayer
    class M1,M2,M3,M4,M5,M6,M7,M8 dataLayer
    class N1,N2,N3,N4,N5,N6 networkLayer
```

### Architecture Patterns

#### 1. **MVVM (Model-View-ViewModel)**
```swift
// View
struct DashboardView: View {
    @StateObject private var viewModel = DashboardViewModel()
    
    var body: some View {
        VStack {
            MetricsCardView(metrics: viewModel.metrics)
            TransactionStreamView(transactions: viewModel.recentTransactions)
        }
        .onAppear { viewModel.loadData() }
    }
}

// ViewModel
class DashboardViewModel: ObservableObject {
    @Published var metrics: DashboardMetrics?
    @Published var recentTransactions: [Transaction] = []
    
    private let apiService = AMLAPIService.shared
    private let webSocketService = WebSocketService.shared
    
    func loadData() {
        Task {
            metrics = await apiService.fetchDashboardMetrics()
            recentTransactions = await apiService.getRecentTransactions()
        }
    }
}

// Model
struct DashboardMetrics: Codable {
    let transactionsPerSecond: Double
    let activeAlerts: Int
    let systemHealth: SystemStatus
    let mlAccuracy: Double
}
```

#### 2. **Dependency Injection**
```swift
protocol AMLAPIServiceProtocol {
    func fetchDashboardMetrics() async -> DashboardMetrics?
    func getTransactionDetails(_ id: String) async -> TransactionDetail?
}

class DashboardViewModel: ObservableObject {
    private let apiService: AMLAPIServiceProtocol
    
    init(apiService: AMLAPIServiceProtocol = AMLAPIService.shared) {
        self.apiService = apiService
    }
}
```

#### 3. **Reactive Programming with Combine**
```swift
class WebSocketService: ObservableObject {
    @Published var connectionState: ConnectionState = .disconnected
    @Published var latestTransaction: Transaction?
    @Published var newAlert: Alert?
    
    private var cancellables = Set<AnyCancellable>()
    
    func connect() {
        webSocket.connect()
            .receive(on: DispatchQueue.main)
            .sink(
                receiveCompletion: { [weak self] completion in
                    self?.connectionState = .disconnected
                },
                receiveValue: { [weak self] message in
                    self?.handleMessage(message)
                }
            )
            .store(in: &cancellables)
    }
}
```

---

## Backend Integration Layer

### Required New Services

#### 1. **REST API Gateway (Port 8002)**

```python
# FastAPI implementation
from fastapi import FastAPI, Depends, HTTPException
from kafka import KafkaConsumer
import jwt

app = FastAPI(title="AML iOS API Gateway")

# Authentication
def verify_jwt_token(token: str = Depends(oauth2_scheme)):
    try:
        payload = jwt.decode(token, SECRET_KEY, algorithms=["HS256"])
        return payload
    except jwt.PyJWTError:
        raise HTTPException(status_code=401, detail="Invalid token")

# Dashboard endpoints
@app.get("/api/v1/dashboard/metrics")
async def get_dashboard_metrics(user=Depends(verify_jwt_token)):
    consumer = KafkaConsumer('ethereum-transactions')
    # Process Kafka data and return metrics
    return {
        "transactions_per_second": calculate_tps(),
        "active_alerts": get_alert_count(),
        "system_health": get_system_status(),
        "ml_accuracy": get_model_accuracy()
    }

@app.get("/api/v1/transactions")
async def get_transactions(
    limit: int = 50,
    offset: int = 0,
    filter_type: str = None,
    user=Depends(verify_jwt_token)
):
    # Query Kafka consumer or database
    transactions = fetch_transactions_from_kafka(limit, offset, filter_type)
    return {"transactions": transactions, "total": get_total_count()}

@app.get("/api/v1/transactions/{transaction_id}")
async def get_transaction_details(
    transaction_id: str,
    user=Depends(verify_jwt_token)
):
    # Fetch detailed transaction data including graph relationships
    detail = get_transaction_detail(transaction_id)
    if not detail:
        raise HTTPException(status_code=404, detail="Transaction not found")
    return detail

@app.get("/api/v1/alerts")
async def get_alerts(
    priority: str = None,
    status: str = None,
    limit: int = 50,
    user=Depends(verify_jwt_token)
):
    alerts = fetch_alerts_from_kafka(priority, status, limit)
    return {"alerts": alerts}

@app.post("/api/v1/auth/login")
async def login(credentials: LoginCredentials):
    if authenticate_user(credentials):
        token = generate_jwt_token(credentials.username)
        return {"access_token": token, "token_type": "bearer"}
    raise HTTPException(status_code=401, detail="Invalid credentials")
```

#### 2. **WebSocket Service (Port 8003)**

```python
# WebSocket implementation
import asyncio
import websockets
import json
from kafka import KafkaConsumer

class AMLWebSocketServer:
    def __init__(self):
        self.clients = set()
        self.kafka_consumer = KafkaConsumer(
            'ethereum-transactions',
            'filtered-transactions',
            'aml-alerts',
            bootstrap_servers=['localhost:9092']
        )
    
    async def register_client(self, websocket, path):
        self.clients.add(websocket)
        try:
            await websocket.wait_closed()
        finally:
            self.clients.remove(websocket)
    
    async def broadcast_kafka_messages(self):
        for message in self.kafka_consumer:
            if self.clients:
                data = {
                    "topic": message.topic,
                    "data": json.loads(message.value.decode('utf-8')),
                    "timestamp": message.timestamp
                }
                await asyncio.gather(
                    *[client.send(json.dumps(data)) for client in self.clients],
                    return_exceptions=True
                )
    
    async def start_server(self):
        # Start WebSocket server
        start_server = websockets.serve(
            self.register_client, 
            "localhost", 
            8003
        )
        
        # Start Kafka consumer
        consumer_task = asyncio.create_task(self.broadcast_kafka_messages())
        
        await asyncio.gather(start_server, consumer_task)
```

#### 3. **Kafka Consumer Bridge Service**

```python
# Kafka to iOS API bridge
class KafkaToIOSBridge:
    def __init__(self):
        self.consumers = {
            'transactions': KafkaConsumer('ethereum-transactions'),
            'alerts': KafkaConsumer('aml-alerts'),
            'metrics': KafkaConsumer('system-metrics')
        }
        self.data_cache = {}
        self.websocket_server = AMLWebSocketServer()
    
    async def process_transaction_stream(self):
        for message in self.consumers['transactions']:
            transaction = json.loads(message.value.decode('utf-8'))
            
            # Cache for REST API
            self.cache_transaction(transaction)
            
            # Broadcast via WebSocket
            await self.websocket_server.broadcast({
                "type": "transaction",
                "data": transaction
            })
    
    async def process_alert_stream(self):
        for message in self.consumers['alerts']:
            alert = json.loads(message.value.decode('utf-8'))
            
            # Cache for REST API
            self.cache_alert(alert)
            
            # Send push notification
            await self.send_push_notification(alert)
            
            # Broadcast via WebSocket
            await self.websocket_server.broadcast({
                "type": "alert",
                "data": alert
            })
```

---

## Data Flow Architecture

### Real-time Data Processing Flow

```mermaid
flowchart TD
    subgraph "iOS App Data Flow"
        A[App Launch] --> B[Initialize Core Services]
        B --> C[Authenticate User]
        C --> D[Connect to Backend APIs]
        D --> E[Subscribe to WebSocket Streams]
        E --> F[Load Dashboard Data]
        
        subgraph "Real-time Data Processing"
            G[Receive WebSocket Data] --> H{Data Type?}
            H -->|Transaction| I[Process Transaction Data]
            H -->|Alert| J[Process Alert Data]
            H -->|Metrics| K[Update Analytics]
            
            I --> L[Update Local Database]
            J --> M[Trigger Notification]
            K --> N[Refresh UI Charts]
            
            L --> O[Notify View Controllers]
            M --> P[Show Alert Badge]
            N --> Q[Animate Chart Updates]
        end
        
        subgraph "User Interactions"
            R[User Taps Transaction] --> S[Fetch Transaction Details]
            S --> T[Show Transaction View]
            
            U[User Swipes to Refresh] --> V[Call REST API]
            V --> W[Update Local Cache]
            W --> X[Refresh UI]
            
            Y[User Changes Settings] --> Z[Update Backend]
            Z --> AA[Update Local Settings]
        end
        
        subgraph "Background Processing"
            BB[App Enters Background] --> CC[Maintain WebSocket Connection]
            CC --> DD[Process Push Notifications]
            DD --> EE[Update Badge Count]
            
            FF[App Becomes Active] --> GG[Sync with Backend]
            GG --> HH[Refresh All Data]
        end
    end
    
    F --> G
    O --> R
    O --> U
    P --> Y
    EE --> FF
```

### Data Synchronization Strategy

#### 1. **Three-Tier Caching**

```swift
class DataSyncManager {
    // Tier 1: In-memory cache (fastest)
    private var memoryCache: [String: Any] = [:]
    
    // Tier 2: Core Data (medium speed, persistent)
    private let coreDataService = CoreDataService()
    
    // Tier 3: Network (slowest, authoritative)
    private let apiService = AMLAPIService()
    
    func fetchTransaction(_ id: String) async -> Transaction? {
        // Try memory cache first
        if let cached = memoryCache[id] as? Transaction {
            return cached
        }
        
        // Try local database
        if let local = coreDataService.fetchTransaction(id) {
            memoryCache[id] = local
            return local
        }
        
        // Fetch from network
        if let remote = await apiService.getTransactionDetails(id) {
            coreDataService.saveTransaction(remote)
            memoryCache[id] = remote
            return remote
        }
        
        return nil
    }
}
```

#### 2. **Real-time Update Pipeline**

```swift
class RealtimeUpdateManager: ObservableObject {
    @Published var latestMetrics: DashboardMetrics?
    @Published var recentTransactions: [Transaction] = []
    @Published var activeAlerts: [Alert] = []
    
    private let webSocketService = WebSocketService.shared
    private let dataSync = DataSyncManager()
    
    func startRealtimeUpdates() {
        webSocketService.messagePublisher
            .receive(on: DispatchQueue.main)
            .sink { [weak self] message in
                self?.processRealtimeMessage(message)
            }
            .store(in: &cancellables)
    }
    
    private func processRealtimeMessage(_ message: WebSocketMessage) {
        switch message.type {
        case .transaction:
            let transaction = message.data as! Transaction
            dataSync.updateTransaction(transaction)
            updateTransactionStream(transaction)
            
        case .alert:
            let alert = message.data as! Alert
            dataSync.saveAlert(alert)
            addToActiveAlerts(alert)
            scheduleLocalNotification(alert)
            
        case .metrics:
            let metrics = message.data as! DashboardMetrics
            latestMetrics = metrics
        }
    }
}
```

---

## Security Architecture

### Authentication & Authorization Flow

```mermaid
sequenceDiagram
    participant iOS as iOS App
    participant Auth as Auth Service
    participant API as API Gateway
    participant Keychain as iOS Keychain
    
    Note over iOS,Keychain: Initial Authentication
    iOS->>Auth: POST /auth/login (username, password)
    Auth-->>iOS: JWT Token + Refresh Token
    iOS->>Keychain: Store tokens securely
    
    Note over iOS,Keychain: API Requests
    iOS->>Keychain: Retrieve JWT token
    Keychain-->>iOS: JWT token
    iOS->>API: GET /api/v1/dashboard (Authorization: Bearer JWT)
    API->>Auth: Validate JWT token
    Auth-->>API: Token valid + user info
    API-->>iOS: Dashboard data
    
    Note over iOS,Keychain: Token Refresh
    iOS->>API: Request with expired JWT
    API-->>iOS: 401 Unauthorized
    iOS->>Keychain: Retrieve refresh token
    iOS->>Auth: POST /auth/refresh (refresh_token)
    Auth-->>iOS: New JWT token
    iOS->>Keychain: Update stored JWT
    iOS->>API: Retry request with new JWT
    API-->>iOS: Success response
```

### Data Protection Implementation

#### 1. **Keychain Services Integration**

```swift
class SecureStorage {
    private let service = "com.aml.blockchain.app"
    
    func storeJWTToken(_ token: String) -> Bool {
        let data = token.data(using: .utf8)!
        
        let query: [String: Any] = [
            kSecClass as String: kSecClassGenericPassword,
            kSecAttrService as String: service,
            kSecAttrAccount as String: "jwt_token",
            kSecValueData as String: data,
            kSecAttrAccessible as String: kSecAttrAccessibleWhenUnlockedThisDeviceOnly
        ]
        
        SecItemDelete(query as CFDictionary)
        let status = SecItemAdd(query as CFDictionary, nil)
        return status == errSecSuccess
    }
    
    func retrieveJWTToken() -> String? {
        let query: [String: Any] = [
            kSecClass as String: kSecClassGenericPassword,
            kSecAttrService as String: service,
            kSecAttrAccount as String: "jwt_token",
            kSecReturnData as String: true,
            kSecMatchLimit as String: kSecMatchLimitOne
        ]
        
        var dataTypeRef: AnyObject?
        let status = SecItemCopyMatching(query as CFDictionary, &dataTypeRef)
        
        if status == errSecSuccess,
           let data = dataTypeRef as? Data,
           let token = String(data: data, encoding: .utf8) {
            return token
        }
        
        return nil
    }
}
```

#### 2. **Core Data Encryption**

```swift
lazy var persistentContainer: NSPersistentContainer = {
    let container = NSPersistentContainer(name: "AMLDataModel")
    
    // Enable Core Data encryption
    let description = container.persistentStoreDescriptions.first
    description?.setOption(FileProtectionType.complete, 
                          forKey: NSPersistentStoreFileProtectionKey)
    
    container.loadPersistentStores { _, error in
        if let error = error {
            fatalError("Core Data error: \(error)")
        }
    }
    
    return container
}()
```

#### 3. **Network Security**

```swift
class SecureAPIClient {
    private let session: URLSession
    
    init() {
        let configuration = URLSessionConfiguration.default
        
        // Certificate pinning
        configuration.urlSessionDelegate = CertificatePinningDelegate()
        
        // Security headers
        configuration.httpAdditionalHeaders = [
            "X-Content-Type-Options": "nosniff",
            "X-Frame-Options": "DENY",
            "X-XSS-Protection": "1; mode=block"
        ]
        
        self.session = URLSession(configuration: configuration)
    }
}

class CertificatePinningDelegate: NSObject, URLSessionDelegate {
    func urlSession(
        _ session: URLSession,
        didReceive challenge: URLAuthenticationChallenge,
        completionHandler: @escaping (URLSession.AuthChallengeDisposition, URLCredential?) -> Void
    ) {
        // Implement certificate pinning logic
        guard let serverTrust = challenge.protectionSpace.serverTrust else {
            completionHandler(.cancelAuthenticationChallenge, nil)
            return
        }
        
        // Verify against pinned certificate
        if validateCertificate(serverTrust) {
            completionHandler(.useCredential, URLCredential(trust: serverTrust))
        } else {
            completionHandler(.cancelAuthenticationChallenge, nil)
        }
    }
}
```

---

## Performance Architecture

### Memory Management Strategy

#### 1. **Efficient Data Loading**

```swift
class TransactionDataSource: ObservableObject {
    @Published var transactions: [Transaction] = []
    private let pageSize = 50
    private var currentPage = 0
    private var isLoading = false
    
    func loadMoreTransactions() {
        guard !isLoading else { return }
        
        isLoading = true
        
        Task {
            let newTransactions = await apiService.getTransactions(
                limit: pageSize,
                offset: currentPage * pageSize
            )
            
            await MainActor.run {
                self.transactions.append(contentsOf: newTransactions)
                self.currentPage += 1
                self.isLoading = false
            }
        }
    }
    
    // Memory cleanup for large datasets
    func trimOldTransactions() {
        if transactions.count > 1000 {
            transactions.removeFirst(transactions.count - 1000)
        }
    }
}
```

#### 2. **Image and Asset Optimization**

```swift
extension UIImage {
    func compressed(quality: CGFloat = 0.7) -> Data? {
        return jpegData(compressionQuality: quality)
    }
    
    static func cached(named name: String) -> UIImage? {
        if let cached = ImageCache.shared.object(forKey: name as NSString) {
            return cached
        }
        
        let image = UIImage(named: name)
        if let image = image {
            ImageCache.shared.setObject(image, forKey: name as NSString)
        }
        
        return image
    }
}

class ImageCache {
    static let shared = NSCache<NSString, UIImage>()
}
```

#### 3. **Background Processing**

```swift
class BackgroundTaskManager {
    private var backgroundTask: UIBackgroundTaskIdentifier = .invalid
    
    func startBackgroundSync() {
        backgroundTask = UIApplication.shared.beginBackgroundTask { [weak self] in
            self?.endBackgroundSync()
        }
        
        Task {
            await performBackgroundSync()
            endBackgroundSync()
        }
    }
    
    private func endBackgroundSync() {
        UIApplication.shared.endBackgroundTask(backgroundTask)
        backgroundTask = .invalid
    }
    
    private func performBackgroundSync() async {
        // Sync critical data
        await syncPendingAlerts()
        await updateSystemMetrics()
        await cleanupOldCache()
    }
}
```

### Performance Monitoring

```swift
class PerformanceMonitor {
    static let shared = PerformanceMonitor()
    
    private var metrics: [String: TimeInterval] = [:]
    
    func startMeasuring(_ operation: String) {
        metrics[operation] = Date().timeIntervalSince1970
    }
    
    func stopMeasuring(_ operation: String) {
        guard let startTime = metrics[operation] else { return }
        
        let duration = Date().timeIntervalSince1970 - startTime
        
        // Log performance metrics
        print("⏱️ \(operation): \(duration * 1000)ms")
        
        // Send to analytics if duration exceeds threshold
        if duration > 1.0 {
            sendPerformanceAlert(operation: operation, duration: duration)
        }
    }
    
    private func sendPerformanceAlert(operation: String, duration: TimeInterval) {
        // Send performance data to backend monitoring
    }
}
```

---

This comprehensive system architecture documentation provides the technical foundation for implementing your iOS AML app with robust integration to your existing blockchain pipeline. Each section includes implementation details and code examples to guide development. 