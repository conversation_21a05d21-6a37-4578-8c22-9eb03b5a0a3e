# iOS AML App - Data Flow Documentation

## 📋 Table of Contents
1. [Real-time Data Processing Flow](#real-time-data-processing-flow)
2. [Sequence Diagram](#sequence-diagram)
3. [Data Synchronization](#data-synchronization)
4. [Background Processing](#background-processing)

---

## Real-time Data Processing Flow

### Application Data Flow Diagram

```mermaid
flowchart TD
    subgraph "iOS App Data Flow"
        A[App Launch] --> B[Initialize Core Services]
        B --> C[Authenticate User]
        C --> D[Connect to Backend APIs]
        D --> E[Subscribe to WebSocket Streams]
        E --> F[Load Dashboard Data]
        
        subgraph "Real-time Data Processing"
            G[Receive WebSocket Data] --> H{Data Type?}
            H -->|Transaction| I[Process Transaction Data]
            H -->|Alert| J[Process Alert Data]
            H -->|Metrics| K[Update Analytics]
            
            I --> L[Update Local Database]
            J --> M[Trigger Notification]
            K --> N[Refresh UI Charts]
            
            L --> O[Notify View Controllers]
            M --> P[Show Alert Badge]
            N --> Q[Animate Chart Updates]
        end
        
        subgraph "User Interactions"
            R[User Taps Transaction] --> S[Fetch Transaction Details]
            S --> T[Show Transaction View]
            
            U[User Swipes to Refresh] --> V[Call REST API]
            V --> W[Update Local Cache]
            W --> X[Refresh UI]
            
            Y[User Changes Settings] --> Z[Update Backend]
            Z --> AA[Update Local Settings]
        end
        
        subgraph "Background Processing"
            BB[App Enters Background] --> CC[Maintain WebSocket Connection]
            CC --> DD[Process Push Notifications]
            DD --> EE[Update Badge Count]
            
            FF[App Becomes Active] --> GG[Sync with Backend]
            GG --> HH[Refresh All Data]
        end
    end
    
    F --> G
    O --> R
    O --> U
    P --> Y
    EE --> FF
```

### Data Processing Pipeline

#### 1. **Initialization Flow**
```swift
class AppInitializationManager {
    func initializeApp() async {
        // 1. Load stored authentication
        await authManager.loadStoredCredentials()
        
        // 2. Initialize core services
        await coreDataService.initialize()
        await networkManager.initialize()
        
        // 3. Connect to backend
        if authManager.isAuthenticated {
            await connectToBackend()
        }
        
        // 4. Setup real-time connections
        await webSocketService.connect()
        
        // 5. Load initial data
        await loadDashboardData()
    }
    
    private func connectToBackend() async {
        let success = await apiService.validateConnection()
        if !success {
            // Handle connection failure
            await authManager.refreshToken()
        }
    }
}
```

#### 2. **Real-time Data Processing**
```swift
class RealtimeDataProcessor: ObservableObject {
    @Published var processingStats = ProcessingStats()
    
    func processIncomingData(_ message: WebSocketMessage) {
        switch message.type {
        case .transaction:
            processTransaction(message.data as! Transaction)
        case .alert:
            processAlert(message.data as! Alert)
        case .metrics:
            processMetrics(message.data as! SystemMetrics)
        case .heartbeat:
            updateConnectionStatus()
        }
    }
    
    private func processTransaction(_ transaction: Transaction) {
        // 1. Validate transaction data
        guard transaction.isValid else { return }
        
        // 2. Update local cache
        cacheManager.updateTransaction(transaction)
        
        // 3. Notify subscribers
        NotificationCenter.default.post(
            name: .transactionUpdated,
            object: transaction
        )
        
        // 4. Update UI on main thread
        DispatchQueue.main.async {
            self.processingStats.transactionsProcessed += 1
        }
    }
    
    private func processAlert(_ alert: Alert) {
        // 1. Store alert locally
        coreDataService.saveAlert(alert)
        
        // 2. Determine notification priority
        let priority = alert.riskLevel.notificationPriority
        
        // 3. Show local notification if app is backgrounded
        if UIApplication.shared.applicationState != .active {
            scheduleLocalNotification(for: alert, priority: priority)
        }
        
        // 4. Update alert badge
        updateAlertBadge()
    }
}
```

---

## Sequence Diagram

### Complete Interaction Flow

```mermaid
sequenceDiagram
    participant iOS as iOS AML App
    participant API as REST API Gateway
    participant WS as WebSocket Service
    participant Kafka as Kafka Consumer
    participant Pipeline as AML Pipeline
    
    Note over iOS,Pipeline: App Launch & Authentication
    iOS->>API: POST /auth/login
    API-->>iOS: JWT Token + User Profile
    
    Note over iOS,Pipeline: Initial Data Load
    iOS->>API: GET /dashboard/metrics
    API->>Kafka: Query latest metrics
    Kafka-->>API: Aggregated data
    API-->>iOS: Dashboard data
    
    Note over iOS,Pipeline: WebSocket Connection
    iOS->>WS: Connect with JWT
    WS-->>iOS: Connection established
    
    Note over iOS,Pipeline: Real-time Data Flow
    Pipeline->>Kafka: New transaction processed
    Kafka->>WS: Push to WebSocket
    WS->>iOS: Real-time transaction data
    iOS->>iOS: Update UI & Store locally
    
    Note over iOS,Pipeline: Alert Processing
    Pipeline->>Kafka: Suspicious pattern detected
    Kafka->>WS: Alert notification
    WS->>iOS: Alert payload
    iOS->>iOS: Show notification + Update badge
    
    Note over iOS,Pipeline: User Interaction
    iOS->>API: GET /transactions/{id}/details
    API->>Kafka: Query transaction history
    Kafka-->>API: Transaction + Graph data
    API-->>iOS: Detailed transaction info
    
    Note over iOS,Pipeline: Background Sync
    iOS->>API: GET /alerts/unread
    API->>Kafka: Query pending alerts
    Kafka-->>API: Alert list
    API-->>iOS: Unread alerts
    iOS->>iOS: Update badge count
    
    Note over iOS,Pipeline: Settings Update
    iOS->>API: PUT /user/settings
    API-->>iOS: Settings updated
    iOS->>WS: Update subscription preferences
    WS-->>iOS: Subscription confirmed
```

### Authentication Flow Detail

```swift
class AuthenticationFlow {
    func performLogin(username: String, password: String) async -> AuthResult {
        // 1. Send login request
        let response = await apiService.login(username: username, password: password)
        
        // 2. Handle response
        switch response {
        case .success(let authData):
            // Store tokens securely
            secureStorage.storeJWTToken(authData.accessToken)
            secureStorage.storeRefreshToken(authData.refreshToken)
            
            // Update user profile
            userManager.updateProfile(authData.user)
            
            // Initialize real-time connections
            await initializeRealtimeServices()
            
            return .success
            
        case .failure(let error):
            return .failure(error)
        }
    }
    
    private func initializeRealtimeServices() async {
        // Connect to WebSocket with JWT
        await webSocketService.connect(token: secureStorage.getJWTToken())
        
        // Subscribe to relevant topics
        await webSocketService.subscribe(to: [
            "user_transactions",
            "user_alerts",
            "system_metrics"
        ])
    }
}
```

---

## Data Synchronization

### Three-Tier Data Architecture

#### 1. **Memory Cache (Tier 1 - Fastest)**
```swift
class MemoryCache {
    private var cache: [String: Any] = [:]
    private let queue = DispatchQueue(label: "memory-cache", attributes: .concurrent)
    
    func get<T>(_ key: String, type: T.Type) -> T? {
        return queue.sync {
            return cache[key] as? T
        }
    }
    
    func set<T>(_ key: String, value: T) {
        queue.async(flags: .barrier) {
            self.cache[key] = value
        }
    }
    
    func clear() {
        queue.async(flags: .barrier) {
            self.cache.removeAll()
        }
    }
}
```

#### 2. **Core Data (Tier 2 - Medium Speed, Persistent)**
```swift
class CoreDataCache {
    lazy var persistentContainer: NSPersistentContainer = {
        let container = NSPersistentContainer(name: "AMLDataModel")
        container.loadPersistentStores { _, error in
            if let error = error {
                fatalError("Core Data error: \(error)")
            }
        }
        return container
    }()
    
    func saveTransaction(_ transaction: Transaction) {
        let context = persistentContainer.viewContext
        let entity = TransactionEntity(context: context)
        
        entity.id = transaction.id
        entity.hash = transaction.hash
        entity.amount = transaction.amount
        entity.timestamp = transaction.timestamp
        entity.riskScore = transaction.riskScore
        
        try? context.save()
    }
    
    func fetchTransactions(limit: Int = 50) -> [Transaction] {
        let context = persistentContainer.viewContext
        let request: NSFetchRequest<TransactionEntity> = TransactionEntity.fetchRequest()
        request.fetchLimit = limit
        request.sortDescriptors = [NSSortDescriptor(key: "timestamp", ascending: false)]
        
        guard let entities = try? context.fetch(request) else { return [] }
        return entities.compactMap { $0.toTransaction() }
    }
}
```

#### 3. **Network Layer (Tier 3 - Slowest, Authoritative)**
```swift
class NetworkDataSource {
    func fetchTransactions(page: Int, limit: Int) async -> [Transaction] {
        let endpoint = "/api/v1/transactions?page=\(page)&limit=\(limit)"
        
        do {
            let response: TransactionResponse = try await apiService.get(endpoint)
            return response.transactions
        } catch {
            print("Network fetch error: \(error)")
            return []
        }
    }
    
    func fetchTransactionDetails(_ id: String) async -> TransactionDetail? {
        let endpoint = "/api/v1/transactions/\(id)"
        
        do {
            return try await apiService.get(endpoint)
        } catch {
            print("Failed to fetch transaction details: \(error)")
            return nil
        }
    }
}
```

### Unified Data Manager

```swift
class UnifiedDataManager: ObservableObject {
    private let memoryCache = MemoryCache()
    private let coreDataCache = CoreDataCache()
    private let networkSource = NetworkDataSource()
    
    @Published var transactions: [Transaction] = []
    @Published var isLoading = false
    
    func fetchTransaction(_ id: String) async -> Transaction? {
        // Check memory cache first
        if let cached = memoryCache.get(id, type: Transaction.self) {
            return cached
        }
        
        // Check Core Data
        if let local = coreDataCache.fetchTransaction(id) {
            memoryCache.set(id, value: local)
            return local
        }
        
        // Fetch from network
        if let remote = await networkSource.fetchTransactionDetails(id) {
            let transaction = remote.toTransaction()
            
            // Update caches
            coreDataCache.saveTransaction(transaction)
            memoryCache.set(id, value: transaction)
            
            return transaction
        }
        
        return nil
    }
    
    func refreshData() async {
        await MainActor.run { isLoading = true }
        
        let newTransactions = await networkSource.fetchTransactions(page: 0, limit: 50)
        
        await MainActor.run {
            self.transactions = newTransactions
            self.isLoading = false
        }
        
        // Update local cache
        newTransactions.forEach { coreDataCache.saveTransaction($0) }
    }
}
```

---

## Background Processing

### Background Task Management

```swift
class BackgroundProcessingManager {
    private var backgroundTask: UIBackgroundTaskIdentifier = .invalid
    private let processingQueue = DispatchQueue(label: "background-processing")
    
    func startBackgroundProcessing() {
        backgroundTask = UIApplication.shared.beginBackgroundTask { [weak self] in
            self?.endBackgroundProcessing()
        }
        
        guard backgroundTask != .invalid else { return }
        
        processingQueue.async { [weak self] in
            self?.performBackgroundTasks()
            self?.endBackgroundProcessing()
        }
    }
    
    private func performBackgroundTasks() {
        // 1. Sync pending alerts
        syncPendingAlerts()
        
        // 2. Update system metrics
        updateCachedMetrics()
        
        // 3. Clean up old data
        cleanupOldCache()
        
        // 4. Prepare push notifications
        preparePendingNotifications()
    }
    
    private func syncPendingAlerts() {
        let semaphore = DispatchSemaphore(value: 0)
        
        Task {
            let alerts = await apiService.getPendingAlerts()
            alerts.forEach { coreDataCache.saveAlert($0) }
            semaphore.signal()
        }
        
        semaphore.wait()
    }
    
    private func endBackgroundProcessing() {
        UIApplication.shared.endBackgroundTask(backgroundTask)
        backgroundTask = .invalid
    }
}
```

### WebSocket Background Handling

```swift
class BackgroundWebSocketManager {
    private var webSocketTask: URLSessionWebSocketTask?
    private var backgroundSession: URLSession?
    
    func setupBackgroundWebSocket() {
        let config = URLSessionConfiguration.background(withIdentifier: "aml-websocket")
        config.isDiscretionary = false
        config.sessionSendsLaunchEvents = true
        
        backgroundSession = URLSession(configuration: config, delegate: self, delegateQueue: nil)
        
        guard let url = URL(string: "wss://api.aml.com/ws") else { return }
        webSocketTask = backgroundSession?.webSocketTask(with: url)
        webSocketTask?.resume()
    }
    
    func handleBackgroundWebSocketMessage(_ message: URLSessionWebSocketTask.Message) {
        switch message {
        case .data(let data):
            if let wsMessage = try? JSONDecoder().decode(WebSocketMessage.self, from: data) {
                processBackgroundMessage(wsMessage)
            }
        case .string(let string):
            if let data = string.data(using: .utf8),
               let wsMessage = try? JSONDecoder().decode(WebSocketMessage.self, from: data) {
                processBackgroundMessage(wsMessage)
            }
        @unknown default:
            break
        }
    }
    
    private func processBackgroundMessage(_ message: WebSocketMessage) {
        if message.type == .alert {
            let alert = message.data as! Alert
            
            // Show local notification
            scheduleLocalNotification(for: alert)
            
            // Update badge count
            updateAppBadge()
        }
    }
}
```

### Push Notification Processing

```swift
class PushNotificationProcessor: NSObject, UNUserNotificationCenterDelegate {
    
    func processPushNotification(_ userInfo: [AnyHashable: Any]) {
        guard let alertData = userInfo["alert"] as? [String: Any],
              let alert = Alert.from(dictionary: alertData) else { return }
        
        // Save to local database
        coreDataCache.saveAlert(alert)
        
        // Update UI if app is active
        if UIApplication.shared.applicationState == .active {
            NotificationCenter.default.post(
                name: .newAlertReceived,
                object: alert
            )
        }
        
        // Update badge count
        updateAppBadge()
    }
    
    func updateAppBadge() {
        let unreadCount = coreDataCache.getUnreadAlertCount()
        
        DispatchQueue.main.async {
            UIApplication.shared.applicationIconBadgeNumber = unreadCount
        }
    }
    
    // MARK: - UNUserNotificationCenterDelegate
    
    func userNotificationCenter(
        _ center: UNUserNotificationCenter,
        didReceive response: UNNotificationResponse,
        withCompletionHandler completionHandler: @escaping () -> Void
    ) {
        let userInfo = response.notification.request.content.userInfo
        
        if let alertId = userInfo["alert_id"] as? String {
            // Navigate to alert detail when notification is tapped
            NotificationCenter.default.post(
                name: .navigateToAlert,
                object: alertId
            )
        }
        
        completionHandler()
    }
}
```

---

This data flow documentation provides a comprehensive guide for implementing the real-time data processing, synchronization, and background handling capabilities of your iOS AML app. The architecture ensures efficient data management while maintaining real-time responsiveness and offline capability. 