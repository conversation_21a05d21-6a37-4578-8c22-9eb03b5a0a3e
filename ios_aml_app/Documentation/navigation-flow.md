# iOS AML App - Navigation Flow Documentation

## 📋 Table of Contents
1. [App Navigation Structure](#app-navigation-structure)
2. [Screen Hierarchy](#screen-hierarchy)
3. [User Journey Flows](#user-journey-flows)
4. [UI Components](#ui-components)

---

## App Navigation Structure

### Main Navigation Architecture

```mermaid
graph LR
    subgraph "iOS App Screens & Navigation"
        subgraph "Tab Bar Navigation"
            T1[Dashboard<br/>📊]
            T2[Alerts<br/>🚨]
            T3[Explorer<br/>🔍]
            T4[Analytics<br/>📈]
            T5[Settings<br/>⚙️]
        end
        
        subgraph "Dashboard Flow"
            D1[Live Metrics Card] --> D2[Transaction Stream]
            D1 --> D3[Alert Summary]
            D1 --> D4[System Status]
            D2 --> D5[Transaction Detail View]
            D3 --> D6[Alert Detail View]
        end
        
        subgraph "Alert Management"
            A1[Alert List] --> A2[Alert Categories]
            A1 --> A3[Alert Details]
            A2 --> A4[Suspicious Patterns]
            A2 --> A5[High-Risk Transactions]
            A3 --> A6[Investigation Tools]
        end
        
        subgraph "Transaction Explorer"
            E1[Search Interface] --> E2[Transaction History]
            E1 --> E3[Filter Options]
            E2 --> E4[Transaction Graph View]
            E3 --> E5[Advanced Filters]
            E4 --> E6[Node Relationships]
        end
        
        subgraph "Analytics Dashboard"
            AN1[Performance Metrics] --> AN2[CEP Throughput]
            AN1 --> AN3[Detection Accuracy]
            AN1 --> AN4[System Health]
            AN2 --> AN5[Detailed Charts]
            AN3 --> AN6[ML Model Stats]
        end
        
        subgraph "Settings & Configuration"
            S1[User Profile] --> S2[Notification Preferences]
            S1 --> S3[API Configuration]
            S1 --> S4[Data Sync Settings]
            S2 --> S5[Push Notification Setup]
            S3 --> S6[Backend Endpoints]
        end
    end
    
    %% Navigation connections
    T1 --> D1
    T2 --> A1
    T3 --> E1
    T4 --> AN1
    T5 --> S1
```

### Navigation Implementation

#### 1. **Main TabView Structure**
```swift
struct ContentView: View {
    @StateObject private var navigationManager = NavigationManager()
    
    var body: some View {
        TabView(selection: $navigationManager.selectedTab) {
            DashboardView()
                .tabItem {
                    Image(systemName: "chart.line.uptrend.xyaxis")
                    Text("Dashboard")
                }
                .tag(Tab.dashboard)
            
            AlertsView()
                .tabItem {
                    Image(systemName: "exclamationmark.triangle.fill")
                    Text("Alerts")
                }
                .tag(Tab.alerts)
                .badge(navigationManager.unreadAlertCount)
            
            ExplorerView()
                .tabItem {
                    Image(systemName: "magnifyingglass")
                    Text("Explorer")
                }
                .tag(Tab.explorer)
            
            AnalyticsView()
                .tabItem {
                    Image(systemName: "chart.bar.fill")
                    Text("Analytics")
                }
                .tag(Tab.analytics)
            
            SettingsView()
                .tabItem {
                    Image(systemName: "gearshape.fill")
                    Text("Settings")
                }
                .tag(Tab.settings)
        }
        .environmentObject(navigationManager)
    }
}

enum Tab: String, CaseIterable {
    case dashboard, alerts, explorer, analytics, settings
}
```

#### 2. **Navigation Manager**
```swift
class NavigationManager: ObservableObject {
    @Published var selectedTab: Tab = .dashboard
    @Published var unreadAlertCount: Int = 0
    @Published var navigationPath = NavigationPath()
    
    // Deep linking support
    func navigateToAlert(_ alertId: String) {
        selectedTab = .alerts
        // Navigate to specific alert
        navigationPath.append(AlertDestination.detail(alertId))
    }
    
    func navigateToTransaction(_ transactionId: String) {
        selectedTab = .explorer
        // Navigate to specific transaction
        navigationPath.append(ExplorerDestination.transactionDetail(transactionId))
    }
    
    func navigateToSettings(section: SettingsSection) {
        selectedTab = .settings
        navigationPath.append(SettingsDestination.section(section))
    }
}
```

---

## Screen Hierarchy

### 1. Dashboard Tab Flow

```swift
struct DashboardView: View {
    @StateObject private var viewModel = DashboardViewModel()
    @EnvironmentObject var navigationManager: NavigationManager
    
    var body: some View {
        NavigationStack(path: $navigationManager.navigationPath) {
            ScrollView {
                LazyVStack(spacing: 16) {
                    // Live metrics section
                    MetricsCardView(metrics: viewModel.metrics)
                    
                    // Recent transactions
                    TransactionStreamView(transactions: viewModel.recentTransactions)
                    
                    // Alert summary
                    AlertSummaryView(alerts: viewModel.activeAlerts)
                    
                    // System status
                    SystemStatusView(status: viewModel.systemStatus)
                }
                .padding()
            }
            .navigationTitle("AML Dashboard")
            .navigationDestination(for: DashboardDestination.self) { destination in
                switch destination {
                case .transactionDetail(let id):
                    TransactionDetailView(transactionId: id)
                case .alertDetail(let id):
                    AlertDetailView(alertId: id)
                case .systemStatus:
                    SystemStatusDetailView()
                }
            }
        }
        .onAppear {
            viewModel.loadDashboardData()
        }
    }
}

enum DashboardDestination: Hashable {
    case transactionDetail(String)
    case alertDetail(String)
    case systemStatus
}
```

### 2. Alerts Tab Flow

```swift
struct AlertsView: View {
    @StateObject private var viewModel = AlertsViewModel()
    @State private var selectedFilter: AlertFilter = .all
    
    var body: some View {
        NavigationStack {
            VStack {
                // Filter picker
                AlertFilterPicker(selection: $selectedFilter)
                
                // Alert list
                List(viewModel.filteredAlerts) { alert in
                    AlertRowView(alert: alert)
                        .onTapGesture {
                            navigateToAlertDetail(alert.id)
                        }
                }
                .refreshable {
                    await viewModel.refreshAlerts()
                }
            }
            .navigationTitle("Alerts")
            .navigationDestination(for: AlertDestination.self) { destination in
                switch destination {
                case .detail(let id):
                    AlertDetailView(alertId: id)
                case .investigation(let id):
                    AlertInvestigationView(alertId: id)
                case .categories:
                    AlertCategoriesView()
                }
            }
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Categories") {
                        navigateToCategories()
                    }
                }
            }
        }
    }
    
    private func navigateToAlertDetail(_ alertId: String) {
        // Navigation logic
    }
}

enum AlertDestination: Hashable {
    case detail(String)
    case investigation(String)
    case categories
}
```

### 3. Explorer Tab Flow

```swift
struct ExplorerView: View {
    @StateObject private var viewModel = ExplorerViewModel()
    @State private var searchText = ""
    @State private var showingFilters = false
    
    var body: some View {
        NavigationStack {
            VStack {
                // Search bar
                SearchBar(text: $searchText, onSearchButtonClicked: {
                    viewModel.searchTransactions(searchText)
                })
                
                // Filter toggle
                HStack {
                    Button("Filters") {
                        showingFilters.toggle()
                    }
                    .foregroundColor(.blue)
                    
                    Spacer()
                    
                    Text("\(viewModel.transactions.count) results")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
                .padding(.horizontal)
                
                // Results list
                if viewModel.isLoading {
                    ProgressView("Searching...")
                        .frame(maxWidth: .infinity, maxHeight: .infinity)
                } else {
                    List(viewModel.transactions) { transaction in
                        TransactionRowView(transaction: transaction)
                            .onTapGesture {
                                navigateToTransactionDetail(transaction.id)
                            }
                    }
                }
            }
            .navigationTitle("Explorer")
            .navigationDestination(for: ExplorerDestination.self) { destination in
                switch destination {
                case .transactionDetail(let id):
                    TransactionDetailView(transactionId: id)
                case .graphView(let id):
                    TransactionGraphView(transactionId: id)
                case .filters:
                    FilterConfigurationView()
                }
            }
            .sheet(isPresented: $showingFilters) {
                FilterSheetView(filters: $viewModel.activeFilters)
            }
        }
    }
}

enum ExplorerDestination: Hashable {
    case transactionDetail(String)
    case graphView(String)
    case filters
}
```

### 4. Analytics Tab Flow

```swift
struct AnalyticsView: View {
    @StateObject private var viewModel = AnalyticsViewModel()
    @State private var selectedTimeRange: TimeRange = .hour24
    
    var body: some View {
        NavigationStack {
            ScrollView {
                VStack(spacing: 20) {
                    // Time range picker
                    TimeRangePicker(selection: $selectedTimeRange)
                    
                    // Performance metrics
                    PerformanceMetricsCard(metrics: viewModel.performanceMetrics)
                    
                    // CEP throughput chart
                    CEPThroughputChart(data: viewModel.cepData)
                    
                    // ML accuracy chart
                    MLAccuracyChart(data: viewModel.mlAccuracyData)
                    
                    // System health indicators
                    SystemHealthGrid(healthData: viewModel.systemHealth)
                }
                .padding()
            }
            .navigationTitle("Analytics")
            .navigationDestination(for: AnalyticsDestination.self) { destination in
                switch destination {
                case .detailedChart(let type):
                    DetailedChartView(chartType: type)
                case .systemHealth:
                    SystemHealthDetailView()
                case .mlModelStats:
                    MLModelStatsView()
                }
            }
        }
        .onChange(of: selectedTimeRange) { newRange in
            viewModel.updateTimeRange(newRange)
        }
    }
}

enum AnalyticsDestination: Hashable {
    case detailedChart(ChartType)
    case systemHealth
    case mlModelStats
}
```

### 5. Settings Tab Flow

```swift
struct SettingsView: View {
    @StateObject private var viewModel = SettingsViewModel()
    
    var body: some View {
        NavigationStack {
            List {
                Section("Account") {
                    UserProfileRow()
                    NavigationLink("Notification Preferences", 
                                 destination: NotificationSettingsView())
                }
                
                Section("API Configuration") {
                    NavigationLink("Backend Endpoints", 
                                 destination: APIConfigurationView())
                    NavigationLink("Data Sync Settings", 
                                 destination: DataSyncSettingsView())
                }
                
                Section("App") {
                    NavigationLink("About", destination: AboutView())
                    Button("Sign Out") {
                        viewModel.signOut()
                    }
                    .foregroundColor(.red)
                }
            }
            .navigationTitle("Settings")
        }
    }
}
```

---

## User Journey Flows

### 1. Investigation Workflow

```mermaid
flowchart TD
    A[User Opens App] --> B[Dashboard View]
    B --> C{New Alert Badge?}
    C -->|Yes| D[Tap Alerts Tab]
    C -->|No| E[Continue Monitoring]
    
    D --> F[Alert List View]
    F --> G[Select High Priority Alert]
    G --> H[Alert Detail View]
    
    H --> I[View Transaction Graph]
    I --> J[Identify Suspicious Pattern]
    J --> K[Start Investigation]
    
    K --> L[Search Related Transactions]
    L --> M[Explorer View]
    M --> N[Apply Filters]
    N --> O[Review Results]
    
    O --> P[Mark Alert as Reviewed]
    P --> Q[Add Investigation Notes]
    Q --> R[Complete Investigation]
```

### 2. Real-time Monitoring Flow

```mermaid
flowchart TD
    A[Launch App] --> B[Authentication]
    B --> C[Connect to WebSocket]
    C --> D[Dashboard Loads]
    
    D --> E[Real-time Updates Start]
    E --> F{New Transaction?}
    F -->|Yes| G[Update Transaction Stream]
    F -->|No| H{New Alert?}
    
    H -->|Yes| I[Show Notification]
    I --> J[Update Alert Badge]
    J --> K[Add to Alert List]
    
    G --> L[Animate UI Update]
    K --> L
    L --> M[Continue Monitoring]
    M --> E
    
    H -->|No| N{Metrics Update?}
    N -->|Yes| O[Update Charts]
    N -->|No| E
    O --> E
```

### 3. Configuration Flow

```mermaid
flowchart TD
    A[Settings Tab] --> B[User Profile]
    B --> C{First Time Setup?}
    C -->|Yes| D[API Configuration]
    C -->|No| E[Notification Preferences]
    
    D --> F[Enter Backend URL]
    F --> G[Test Connection]
    G --> H{Connection Success?}
    H -->|Yes| I[Save Configuration]
    H -->|No| J[Show Error Message]
    J --> F
    
    I --> K[Setup Notifications]
    K --> L[Enable Push Notifications]
    L --> M[Configure Alert Thresholds]
    M --> N[Save Settings]
    
    E --> O[Adjust Notification Types]
    O --> P[Set Alert Priorities]
    P --> Q[Configure Sync Frequency]
    Q --> N
```

---

## UI Components

### 1. Reusable Card Components

```swift
struct MetricsCard: View {
    let title: String
    let value: String
    let trend: TrendIndicator
    let color: Color
    
    var body: some View {
        VStack(alignment: .leading, spacing: 8) {
            HStack {
                Text(title)
                    .font(.caption)
                    .foregroundColor(.secondary)
                Spacer()
                TrendIcon(trend: trend)
            }
            
            Text(value)
                .font(.title2)
                .fontWeight(.bold)
                .foregroundColor(color)
        }
        .padding()
        .background(Color(.systemBackground))
        .cornerRadius(12)
        .shadow(radius: 2)
    }
}

struct AlertCard: View {
    let alert: Alert
    let onTap: () -> Void
    
    var body: some View {
        HStack {
            RiskLevelIndicator(level: alert.riskLevel)
            
            VStack(alignment: .leading, spacing: 4) {
                Text(alert.title)
                    .font(.headline)
                
                Text(alert.description)
                    .font(.caption)
                    .foregroundColor(.secondary)
                    .lineLimit(2)
                
                Text(alert.timestamp.relativeString)
                    .font(.caption2)
                    .foregroundColor(.secondary)
            }
            
            Spacer()
            
            if !alert.isRead {
                Circle()
                    .fill(Color.blue)
                    .frame(width: 8, height: 8)
            }
        }
        .padding()
        .background(Color(.systemBackground))
        .cornerRadius(12)
        .onTapGesture(perform: onTap)
    }
}
```

### 2. Chart Components

```swift
struct RealtimeLineChart: View {
    let data: [ChartDataPoint]
    let title: String
    
    var body: some View {
        VStack(alignment: .leading) {
            Text(title)
                .font(.headline)
                .padding(.bottom, 8)
            
            Chart(data) { dataPoint in
                LineMark(
                    x: .value("Time", dataPoint.timestamp),
                    y: .value("Value", dataPoint.value)
                )
                .foregroundStyle(Color.blue)
                .interpolationMethod(.catmullRom)
            }
            .frame(height: 200)
            .chartXAxis {
                AxisMarks(values: .stride(by: .minute, count: 10)) { value in
                    AxisGridLine()
                    AxisValueLabel(format: .dateTime.hour().minute())
                }
            }
        }
        .padding()
        .background(Color(.systemBackground))
        .cornerRadius(12)
    }
}

struct RiskScoreGauge: View {
    let score: Double
    let maxScore: Double = 100
    
    var body: some View {
        VStack {
            ZStack {
                Circle()
                    .stroke(Color.gray.opacity(0.3), lineWidth: 10)
                
                Circle()
                    .trim(from: 0, to: score / maxScore)
                    .stroke(
                        riskColor(for: score),
                        style: StrokeStyle(lineWidth: 10, lineCap: .round)
                    )
                    .rotationEffect(.degrees(-90))
                    .animation(.easeInOut(duration: 1), value: score)
                
                VStack {
                    Text("\(Int(score))")
                        .font(.title)
                        .fontWeight(.bold)
                    
                    Text("Risk Score")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
            }
            .frame(width: 120, height: 120)
        }
    }
    
    private func riskColor(for score: Double) -> Color {
        switch score {
        case 0..<30:
            return .green
        case 30..<70:
            return .orange
        default:
            return .red
        }
    }
}
```

### 3. Navigation Helpers

```swift
struct NavigationHelper {
    static func presentAlert(_ alertId: String, from navigationManager: NavigationManager) {
        navigationManager.selectedTab = .alerts
        navigationManager.navigationPath.append(AlertDestination.detail(alertId))
    }
    
    static func presentTransaction(_ transactionId: String, from navigationManager: NavigationManager) {
        navigationManager.selectedTab = .explorer
        navigationManager.navigationPath.append(ExplorerDestination.transactionDetail(transactionId))
    }
    
    static func handleDeepLink(_ url: URL, navigationManager: NavigationManager) {
        guard let components = URLComponents(url: url, resolvingAgainstBaseURL: false) else { return }
        
        switch components.path {
        case "/alert":
            if let alertId = components.queryItems?.first(where: { $0.name == "id" })?.value {
                presentAlert(alertId, from: navigationManager)
            }
        case "/transaction":
            if let txId = components.queryItems?.first(where: { $0.name == "id" })?.value {
                presentTransaction(txId, from: navigationManager)
            }
        default:
            break
        }
    }
}
```

---

This navigation flow documentation provides a comprehensive guide for implementing the iOS app's user interface structure, navigation patterns, and user experience flows. The modular approach ensures consistent navigation behavior while maintaining flexibility for future enhancements. 