# iOS AML App - Documentation Overview

## 📚 Documentation Index

This directory contains comprehensive documentation for the iOS AML (Anti-Money Laundering) app, including system architecture, data flows, navigation patterns, and technical specifications.

### 📋 Documentation Structure

| Document | Description | Key Content |
|----------|-------------|-------------|
| [**system-architecture.md**](./system-architecture.md) | Overall system design and integration | Architecture diagrams, service integration, iOS app layers |
| [**data-flow.md**](./data-flow.md) | Data processing and synchronization | Real-time data flow, sequence diagrams, caching strategy |
| [**navigation-flow.md**](./navigation-flow.md) | App navigation and user experience | Screen hierarchy, user journeys, UI components |
| [**sequence-diagrams.md**](./sequence-diagrams.md) | Detailed interaction flows | Authentication, real-time updates, investigation workflows |
| [**technical-specs.md**](./technical-specs.md) | Implementation specifications | Data models, API specs, performance requirements |

## 🎯 Quick Reference

### System Architecture Overview
- **Integration**: Seamless connection with existing Kafka → Flink CEP → Graph Layer → ML pipeline
- **Real-time**: WebSocket streams for live transaction monitoring and alerts
- **Offline Support**: Three-tier caching (Memory → Core Data → Network)
- **Security**: JWT authentication, Keychain storage, certificate pinning

### Key Features
- **📊 Dashboard**: Real-time metrics and transaction monitoring
- **🚨 Alerts**: Smart notifications with priority-based delivery
- **🔍 Explorer**: Advanced transaction search and graph visualization
- **📈 Analytics**: Performance metrics and system health monitoring
- **⚙️ Settings**: User preferences and API configuration

### Technology Stack
- **iOS**: 15.0+, SwiftUI, Combine, Core Data
- **Architecture**: MVVM with reactive programming
- **Backend**: REST API + WebSocket integration
- **Security**: Keychain, certificate pinning, encrypted storage

## 🚀 Implementation Roadmap

### Phase 1: Core Foundation
1. **Authentication System** - JWT-based secure login
2. **Data Models** - Core Data entities for transactions and alerts
3. **Network Layer** - REST API client with authentication
4. **Basic UI** - TabView navigation and dashboard skeleton

### Phase 2: Real-time Features
1. **WebSocket Integration** - Live data streaming
2. **Alert System** - Push notifications and in-app alerts
3. **Dashboard Implementation** - Real-time metrics and charts
4. **Cache Management** - Three-tier data synchronization

### Phase 3: Investigation Tools
1. **Transaction Explorer** - Search and filtering capabilities
2. **Graph Visualization** - Interactive transaction networks
3. **Alert Management** - Investigation workflow and notes
4. **Advanced Analytics** - Performance metrics and trends

### Phase 4: Polish & Optimization
1. **Performance Optimization** - Memory management and caching
2. **Security Hardening** - Enhanced certificate pinning
3. **Accessibility** - VoiceOver and accessibility compliance
4. **Testing** - Comprehensive unit and UI testing

## 📐 Architecture Diagrams

### Overall System Integration
```
Ethereum (Sepolia) → Infura API → Kafka → Flink CEP → Graph Layer → ML Inference
                                   ↓
                            Kafka Consumer → REST API Gateway → iOS App
                                   ↓
                            WebSocket Service → Real-time Updates
```

### iOS App Layers
- **Presentation Layer**: SwiftUI views, navigation, user interface
- **Business Logic Layer**: ViewModels, service managers, data processing
- **Data Layer**: Core Data, network clients, caching, notifications

### Data Flow Pattern
1. **Real-time Ingestion**: WebSocket → Data Processor → Local Storage → UI Update
2. **User Interactions**: UI → ViewModel → API Service → Backend → Response → UI
3. **Background Sync**: Background Task → API Sync → Core Data → Badge Update

## 🔄 Integration Points

### Backend Services Required
1. **REST API Gateway** (Port 8002)
   - Authentication endpoints
   - Transaction and alert APIs
   - Dashboard metrics
   - User settings management

2. **WebSocket Service** (Port 8003)
   - Real-time transaction stream
   - Alert notifications
   - System metrics updates
   - Connection management

3. **Kafka Consumer Bridge**
   - Topic consumption: `ethereum-transactions`, `aml-alerts`, `system-metrics`
   - Data transformation for mobile consumption
   - Push notification triggers

### Existing Pipeline Integration
- **Flink CEP Results** → Alert generation
- **Graph Layer Output** → Transaction relationships
- **ML Inference Results** → Risk scoring and explanations
- **System Metrics** → Performance monitoring

## 📊 Performance Targets

| Metric | Target | Implementation Strategy |
|--------|--------|------------------------|
| **App Launch** | <2 seconds | Optimized initialization, cached authentication |
| **WebSocket Latency** | <100ms | Direct connection, efficient message parsing |
| **Memory Usage** | <150MB | Three-tier caching, automatic cleanup |
| **Battery Impact** | Low | Background task optimization, efficient networking |
| **Offline Support** | 24 hours | Local storage of 1000 recent transactions |

## 🔒 Security Implementation

### Authentication Flow
1. **Login**: Username/password → JWT + Refresh token
2. **Storage**: Keychain secure storage with device-only access
3. **API Calls**: Bearer token with automatic refresh
4. **Session Management**: Automatic logout on token expiration

### Data Protection
- **Network**: Certificate pinning, TLS 1.3
- **Storage**: Core Data encryption, sensitive data masking
- **Memory**: Secure memory handling, automatic cleanup
- **Notifications**: Encrypted payloads, secure delivery

## 🧭 Navigation Structure

### Tab-Based Navigation
```
📊 Dashboard → Live Metrics, Transaction Stream, Alert Summary
🚨 Alerts → Alert List, Categories, Investigation Tools
🔍 Explorer → Search, Filters, Graph Visualization
📈 Analytics → Performance Metrics, System Health
⚙️ Settings → Profile, Notifications, API Config
```

### Deep Linking Support
- **Alert Links**: `aml://alert?id=alert_123`
- **Transaction Links**: `aml://transaction?id=tx_456`
- **Push Notification**: Direct navigation to relevant content

## 🎨 UI/UX Guidelines

### Design Principles
- **Clarity**: Clear information hierarchy for quick decision-making
- **Efficiency**: Streamlined workflows for investigation tasks
- **Responsiveness**: Real-time updates with smooth animations
- **Accessibility**: VoiceOver support, dynamic type, high contrast

### Component Library
- **MetricsCard**: Real-time dashboard metrics with trend indicators
- **AlertCard**: Priority-based alert notifications with risk levels
- **TransactionRow**: Transaction list items with risk scoring
- **GraphView**: Interactive transaction network visualization

## 🧪 Testing Strategy

### Unit Testing
- **ViewModels**: Business logic and data processing
- **Services**: API clients, WebSocket handling, Core Data operations
- **Models**: Data transformation and validation

### Integration Testing
- **API Integration**: Backend service communication
- **WebSocket**: Real-time data streaming
- **Core Data**: Database operations and migrations

### UI Testing
- **Navigation**: Tab switching and deep linking
- **User Flows**: Complete investigation workflows
- **Accessibility**: VoiceOver and assistive technologies

## 📱 Device Compatibility

### Minimum Requirements
- **iOS Version**: 15.0+
- **Device**: iPhone 8 / iPhone SE (2nd gen) and newer
- **Storage**: 100MB free space
- **Network**: Wi-Fi or cellular data connection

### Optimizations
- **iPhone**: Optimized for all screen sizes (5.4" to 6.7")
- **iPad**: Universal app with adaptive layouts
- **Performance**: Efficient on older devices (A10 Bionic+)

## 🚀 Deployment & Distribution

### Development Process
1. **Local Development**: Xcode with iOS Simulator
2. **Testing**: TestFlight internal testing
3. **Beta Distribution**: External TestFlight beta
4. **App Store**: Production release

### CI/CD Pipeline
- **Build**: Automated Xcode builds on code commits
- **Testing**: Automated unit and UI test execution
- **Distribution**: Automatic TestFlight uploads
- **Monitoring**: Crash reporting and performance analytics

---

## 📞 Next Steps

To begin implementation:

1. **Review Architecture**: Study all documentation files for comprehensive understanding
2. **Setup Development Environment**: Install Xcode 14.0+, configure iOS 15.0+ target
3. **Backend Prerequisites**: Ensure REST API Gateway and WebSocket services are available
4. **Start with Phase 1**: Implement authentication and basic navigation structure
5. **Iterative Development**: Follow the phased implementation roadmap

For questions or clarification on any aspect of the architecture, refer to the specific documentation files or the main [README](../README.md) in the parent directory.

---

*This documentation provides the complete blueprint for building a production-ready iOS AML app that seamlessly integrates with your existing blockchain pipeline while delivering an exceptional mobile investigation experience.* 