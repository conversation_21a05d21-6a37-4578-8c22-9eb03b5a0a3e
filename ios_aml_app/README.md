# iOS AML App - Real-time Blockchain Anti-Money Laundering

[![iOS](https://img.shields.io/badge/iOS-15.0+-blue.svg)](https://developer.apple.com/ios/)
[![Swift](https://img.shields.io/badge/Swift-5.7+-orange.svg)](https://swift.org/)
[![SwiftUI](https://img.shields.io/badge/SwiftUI-4.0+-green.svg)](https://developer.apple.com/xcode/swiftui/)
[![Xcode](https://img.shields.io/badge/Xcode-14.0+-blue.svg)](https://developer.apple.com/xcode/)

## 🎯 Overview

A native iOS application for real-time blockchain Anti-Money Laundering (AML) monitoring and investigation. This app provides investigators and compliance officers with mobile access to your comprehensive AML pipeline, featuring real-time transaction monitoring, intelligent alerts, and interactive investigation tools.

### 🏆 Key Features

- **📊 Real-time Dashboard** - Live metrics from Kafka streams and ML pipeline
- **🚨 Smart Alert System** - Push notifications for suspicious transaction patterns
- **🔍 Transaction Explorer** - Advanced search and graph visualization
- **📈 Performance Analytics** - CEP throughput, ML accuracy, and system health
- **⚙️ Configuration Management** - API endpoints and notification preferences

## 🏗️ System Architecture

### Overall Integration
```
Ethereum (Sepolia) → Infura API → Kafka → Flink CEP → Graph Layer → ML Inference
                                   ↓
                            Kafka Consumer → REST API Gateway → iOS App
                                   ↓
                            WebSocket Service → Real-time Updates
```

### iOS App Architecture Layers

#### 1. **Presentation Layer (SwiftUI)**
- **TabView Navigation**: Dashboard, Alerts, Explorer, Analytics, Settings
- **Responsive Cards**: Metrics, transactions, alerts with live updates
- **Interactive Charts**: Real-time visualization of AML metrics
- **Search Interface**: Advanced transaction filtering and exploration

#### 2. **Business Logic Layer (MVVM)**
- **ViewModels**: ObservableObject classes managing UI state
- **Service Managers**: API clients, WebSocket handlers, data processors
- **Authentication**: JWT-based secure API access
- **Caching Strategy**: Intelligent local data management

#### 3. **Data Layer**
- **Core Data**: Local transaction and alert storage
- **Network Layer**: URLSession with authentication interceptors
- **WebSocket Client**: Real-time streaming from backend services
- **Push Notifications**: APNs integration for critical alerts

## 📱 App Structure

### Main Screens

```
├── 📊 Dashboard
│   ├── Live Metrics Card (Transactions/sec, Alerts, Accuracy)
│   ├── Transaction Stream (Recent suspicious activities)
│   ├── Alert Summary (Critical, High, Medium priority)
│   └── System Status (Pipeline health indicators)
├── 🚨 Alerts
│   ├── Alert Categories (Suspicious patterns, High-risk transactions)
│   ├── Alert Timeline (Chronological investigation view)
│   ├── Investigation Tools (Graph analysis, pattern recognition)
│   └── Alert Management (Mark resolved, assign investigators)
├── 🔍 Explorer
│   ├── Search Interface (Transaction hash, address, amount ranges)
│   ├── Advanced Filters (Time periods, risk scores, patterns)
│   ├── Transaction Graph (Node-edge visualization)
│   └── Relationship Analysis (Connected accounts, flow patterns)
├── 📈 Analytics
│   ├── Performance Metrics (CEP throughput, latency stats)
│   ├── Detection Accuracy (ML model performance)
│   ├── System Health (Resource usage, error rates)
│   └── Historical Trends (Time-series analysis)
└── ⚙️ Settings
    ├── User Profile (Authentication, preferences)
    ├── API Configuration (Backend endpoints, timeouts)
    ├── Notification Settings (Push preferences, alert thresholds)
    └── Data Sync (Cache management, offline support)
```

## 🛠️ Technical Implementation

### Technology Stack
- **Framework**: SwiftUI + Combine
- **Architecture**: MVVM (Model-View-ViewModel)
- **Networking**: URLSession + WebSocket
- **Local Storage**: Core Data
- **Authentication**: JWT tokens
- **Charts**: Swift Charts framework
- **Notifications**: UserNotifications + APNs

### Key Services

#### 1. **AMLAPIService**
```swift
class AMLAPIService: ObservableObject {
    func fetchDashboardMetrics() async -> DashboardMetrics
    func getTransactionDetails(_ id: String) async -> TransactionDetail
    func getAlerts(filter: AlertFilter) async -> [Alert]
    func updateUserSettings(_ settings: UserSettings) async -> Bool
}
```

#### 2. **WebSocketService**
```swift
class WebSocketService: ObservableObject {
    @Published var isConnected: Bool = false
    @Published var latestTransaction: Transaction?
    @Published var newAlert: Alert?
    
    func connect()
    func disconnect()
    func subscribe(to topics: [String])
}
```

#### 3. **CoreDataService**
```swift
class CoreDataService {
    func saveTransaction(_ transaction: Transaction)
    func fetchTransactions(limit: Int) -> [Transaction]
    func saveAlert(_ alert: Alert)
    func markAlertAsRead(_ alertId: String)
}
```

## 🔌 Backend Integration Requirements

### New Services Needed

#### 1. **REST API Gateway** (Port 8002)
```python
# Required endpoints for iOS app
GET /api/v1/dashboard/metrics     # Live dashboard data
GET /api/v1/transactions         # Transaction list with pagination
GET /api/v1/transactions/{id}    # Detailed transaction view
GET /api/v1/alerts              # Alert management
POST /api/v1/auth/login         # JWT authentication
PUT /api/v1/user/settings       # User preference updates
```

#### 2. **WebSocket Service** (Port 8003)
```python
# Real-time streams for iOS app
/ws/transactions    # Live transaction feed
/ws/alerts         # Real-time alert notifications
/ws/metrics        # System performance updates
```

#### 3. **Kafka Consumer Bridge**
```python
# Service to bridge Kafka topics to iOS APIs
- ethereum-transactions → REST API + WebSocket
- filtered-transactions → Alert processing
- graph-snapshots → Analytics data
- ml-predictions → Alert generation
```

## 🚀 Development Setup

### Prerequisites
```bash
# Required software
- Xcode 14.0+
- iOS 15.0+ target
- macOS 13.0+ (Ventura)
- Swift 5.7+
- Git
```

### Project Structure
```
ios_aml_app/
├── AMLApp/                     # Main iOS project
│   ├── Views/                  # SwiftUI views
│   │   ├── Dashboard/
│   │   ├── Alerts/
│   │   ├── Explorer/
│   │   ├── Analytics/
│   │   └── Settings/
│   ├── ViewModels/            # MVVM business logic
│   ├── Services/              # API and data services
│   ├── Models/                # Data models
│   ├── Utils/                 # Helper utilities
│   └── Resources/             # Assets, localization
├── Frameworks/                # External dependencies
├── Tests/                     # Unit and UI tests
├── Documentation/             # Architecture docs
│   ├── system-architecture.md
│   ├── data-flow.md
│   ├── navigation-flow.md
│   ├── sequence-diagrams.md
│   └── technical-specs.md
└── README.md                  # This file
```

## 🔐 Security Considerations

### Authentication & Authorization
- **JWT Tokens**: Secure API authentication
- **Keychain Storage**: Encrypted token persistence
- **Certificate Pinning**: HTTPS connection security
- **App Transport Security**: iOS security compliance

### Data Protection
- **Core Data Encryption**: Local database protection
- **Sensitive Data Masking**: Transaction details protection
- **Background App Refresh**: Secure data sync policies
- **Biometric Authentication**: Touch ID / Face ID integration

## 📊 Performance Targets

### iOS App Metrics
| Metric | Target | Rationale |
|--------|--------|-----------|
| **App Launch Time** | <2 seconds | Quick access for urgent alerts |
| **API Response Time** | <500ms | Real-time investigation needs |
| **WebSocket Latency** | <100ms | Live transaction monitoring |
| **Memory Usage** | <150MB | Efficient resource utilization |
| **Battery Impact** | Low | Background monitoring capability |
| **Offline Support** | 24 hours | Investigation continuity |

### Data Sync Targets
- **Real-time Updates**: <1 second WebSocket latency
- **Background Sync**: Every 5 minutes when backgrounded
- **Offline Capacity**: Store 1000 recent transactions locally
- **Push Notifications**: <5 second delivery for critical alerts

## 🔬 Integration with Existing Pipeline

### Data Sources
1. **Kafka Topics Consumed**:
   - `ethereum-transactions` → Live transaction feed
   - `filtered-transactions` → CEP-processed data
   - `graph-snapshots` → ML-ready graph data
   - `aml-alerts` → Suspicious pattern alerts

2. **Service Dependencies**:
   - **Flink CEP Dashboard** (Port 8081) → System health data
   - **Spark UI** (Port 4040) → Graph processing metrics
   - **Kafka UI** (Port 8080) → Topic monitoring data

### Performance Integration
- **CEP Filtering Results**: Display 85%+ benign transaction filtering
- **Graph Construction Metrics**: Show <500ms graph generation latency
- **ML Inference Data**: Present <1 second end-to-end detection time
- **Explainability Results**: Integrate GNNExplainer visualizations

## 🎯 Research & Compliance Value

### For Financial Institutions
- **Mobile Investigation**: Field investigators can access AML data anywhere
- **Real-time Response**: Immediate notification of suspicious activities
- **Compliance Reporting**: Mobile access to audit trails and documentation
- **Risk Assessment**: On-the-go transaction risk evaluation

### For Academic Research
- **Live System Monitoring**: Real-time observation of ML pipeline performance
- **Data Collection**: Mobile interface for research data gathering
- **Performance Validation**: Visual confirmation of research hypotheses
- **User Experience Studies**: Mobile AML investigator workflow research

## 📈 Expected Outcomes

### User Experience Metrics
- **Investigation Efficiency**: 50% faster suspicious transaction review
- **Alert Response Time**: 80% reduction in alert acknowledgment time
- **Mobile Accessibility**: 24/7 AML monitoring capability
- **User Satisfaction**: >90% investigator approval rating

### Technical Performance
- **Real-time Synchronization**: <1 second data consistency
- **Mobile Optimization**: <100MB memory footprint
- **Network Efficiency**: <10MB/hour background data usage
- **Reliability**: 99.9% uptime with offline fallback

## 🤝 Contributing

### Development Workflow
```bash
# Clone and setup
git clone [repository-url]
cd ios_aml_app
open AMLApp.xcodeproj

# Development branch strategy
git checkout -b feature/alert-system
# Make changes, test, commit
git push origin feature/alert-system
# Create pull request
```

### Code Standards
- **SwiftLint**: Enforced code style guidelines
- **Unit Tests**: >80% code coverage requirement
- **UI Tests**: Critical user journey automation
- **Documentation**: Inline code documentation required

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](../LICENSE) file for details.

## 🙏 Acknowledgments

### iOS Development Frameworks
- [SwiftUI](https://developer.apple.com/xcode/swiftui/) - Modern iOS UI framework
- [Combine](https://developer.apple.com/documentation/combine) - Reactive programming
- [Core Data](https://developer.apple.com/documentation/coredata) - Local data persistence
- [Swift Charts](https://developer.apple.com/documentation/charts) - Data visualization

### Integration with Research Pipeline
This iOS app is part of the **Real-Time On-Chain Anomaly Detection for Anti-Money Laundering** research project, providing mobile access to cutting-edge blockchain AML technology.

---

## 📞 Contact

- **iOS Development Lead**: [Your Name]
- **Backend Integration**: [Backend Team]
- **Project Repository**: [GitHub Link]
- **Documentation**: See `/Documentation` folder for detailed technical specs

---

*This iOS app bridges advanced blockchain AML research with practical mobile investigation tools, making sophisticated financial crime detection accessible anywhere.* 