# Operating System Files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# IDE Files
.vscode/
.idea/
*.swp
*.swo
*~

# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# Virtual Environments
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# Jupyter Notebook
.ipynb_checkpoints

# PyTorch
*.pth
*.pt

# Java
*.class
*.jar
*.war
*.ear
*.nar
hs_err_pid*
target/
.mvn/timing.properties
.mvn/wrapper/maven-wrapper.properties

# Logs
*.log
logs/
*.out

# Docker
*.pid

# Backup files
*.bak
*.backup
*.tmp
*.temp

# Model files (keep only essential ones)
models/checkpoints/*
!models/checkpoints/.gitkeep
models/exports/*
!models/exports/.gitkeep

# Data files (keep structure, not data)
data/*
!data/.gitkeep
dataset/*
!dataset/.gitkeep

# Temporary directories
tmp/*
!tmp/.gitkeep
backups/*
!backups/.gitkeep

# Generated files
*.generated
*.auto

# Kafka/Flink logs
flink-jars/*.jar
docker/volumes/

# Test outputs
test_output/
test_results/

# Configuration overrides (keep templates)
config/local_*.yml
config/dev_*.yml
config/*_secret.yml

# Runtime files
*.pid
*.lock

# Package managers
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# MacOS
.AppleDouble
.LSOverride

# Windows
desktop.ini
$RECYCLE.BIN/

# Linux
*~
.nfs* 