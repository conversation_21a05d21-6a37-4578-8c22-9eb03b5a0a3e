version: '3.8'

services:
  # Flink JobManager - Coordinates Flink cluster
  flink-jobmanager:
    image: flink:1.18.0-scala_2.12
    container_name: flink-jobmanager
    ports:
      - "8081:8081"  # Flink Web UI
    environment:
      - |
        FLINK_PROPERTIES=
        jobmanager.rpc.address: flink-jobmanager
        taskmanager.numberOfTaskSlots: 4
        parallelism.default: 4
        state.backend: filesystem
        state.checkpoints.dir: file:///tmp/flink-checkpoints
        execution.checkpointing.interval: 30s
        web.submit.enable: true
        classloader.resolve-order: parent-first
    volumes:
      - ./cep_layer/target:/opt/flink/jobs
      - flink-checkpoints:/tmp/flink-checkpoints
    command: jobmanager
    networks:
      - blockchain-aml-network
    depends_on:
      - kafka
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8081/config"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Flink TaskManager - Executes Flink tasks
  flink-taskmanager:
    image: flink:1.18.0-scala_2.12
    environment:
      - |
        FLINK_PROPERTIES=
        jobmanager.rpc.address: flink-jobmanager
        taskmanager.numberOfTaskSlots: 4
        taskmanager.memory.process.size: 2048m
        taskmanager.memory.network.fraction: 0.2
        taskmanager.memory.network.min: 128mb
        classloader.resolve-order: parent-first
    volumes:
      - ./cep_layer/target:/opt/flink/jobs
      - flink-checkpoints:/tmp/flink-checkpoints
    command: taskmanager
    networks:
      - blockchain-aml-network
    depends_on:
      - flink-jobmanager
      - kafka
    deploy:
      replicas: 2  # Scale TaskManagers for throughput

  # Zookeeper - Required by Kafka
  zookeeper:
    image: confluentinc/cp-zookeeper:7.4.0
    container_name: zookeeper-cep
    environment:
      ZOOKEEPER_CLIENT_PORT: 2181
      ZOOKEEPER_TICK_TIME: 2000
    ports:
      - "2181:2181"
    volumes:
      - zookeeper-data:/var/lib/zookeeper/data
      - zookeeper-logs:/var/lib/zookeeper/log
    networks:
      - blockchain-aml-network

  # Kafka - Message broker for transaction streaming
  kafka:
    image: confluentinc/cp-kafka:7.4.0
    container_name: kafka-cep
    depends_on:
      - zookeeper
    ports:
      - "9092:9092"
      - "9101:9101"
    environment:
      KAFKA_BROKER_ID: 1
      KAFKA_ZOOKEEPER_CONNECT: 'zookeeper:2181'
      KAFKA_LISTENER_SECURITY_PROTOCOL_MAP: PLAINTEXT:PLAINTEXT,PLAINTEXT_HOST:PLAINTEXT
      KAFKA_ADVERTISED_LISTENERS: PLAINTEXT://kafka:29092,PLAINTEXT_HOST://localhost:9092
      KAFKA_INTER_BROKER_LISTENER_NAME: PLAINTEXT
      KAFKA_OFFSETS_TOPIC_REPLICATION_FACTOR: 1
      KAFKA_TRANSACTION_STATE_LOG_MIN_ISR: 1
      KAFKA_TRANSACTION_STATE_LOG_REPLICATION_FACTOR: 1
      KAFKA_GROUP_INITIAL_REBALANCE_DELAY_MS: 0
      KAFKA_JMX_PORT: 9101
      KAFKA_JMX_HOSTNAME: localhost
      # Performance optimizations for streaming
      KAFKA_NUM_NETWORK_THREADS: 8
      KAFKA_NUM_IO_THREADS: 8
      KAFKA_SOCKET_SEND_BUFFER_BYTES: 102400
      KAFKA_SOCKET_RECEIVE_BUFFER_BYTES: 102400
      KAFKA_SOCKET_REQUEST_MAX_BYTES: 104857600
      KAFKA_NUM_REPLICA_FETCHERS: 4
      KAFKA_REPLICA_FETCH_MAX_BYTES: 1048576
    volumes:
      - kafka-data:/var/lib/kafka/data
    networks:
      - blockchain-aml-network
    healthcheck:
      test: ["CMD", "kafka-broker-api-versions", "--bootstrap-server", "localhost:9092"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Kafka UI - Web interface for Kafka monitoring
  kafka-ui:
    image: provectuslabs/kafka-ui:latest
    container_name: kafka-ui-cep
    depends_on:
      - kafka
    ports:
      - "8080:8080"
    environment:
      KAFKA_CLUSTERS_0_NAME: local
      KAFKA_CLUSTERS_0_BOOTSTRAPSERVERS: kafka:29092
      KAFKA_CLUSTERS_0_ZOOKEEPER: zookeeper:2181
    networks:
      - blockchain-aml-network

  # CEP Application Container (for deployment)
  flink-cep-app:
    build:
      context: ./cep_layer
      dockerfile: Dockerfile
    container_name: flink-cep-app
    environment:
      - KAFKA_BROKERS=kafka:29092
      - FLINK_JOBMANAGER_HOST=flink-jobmanager
      - FLINK_JOBMANAGER_PORT=6123
    volumes:
      - ./cep_layer/target:/app/target
    networks:
      - blockchain-aml-network
    depends_on:
      - flink-jobmanager
      - flink-taskmanager
      - kafka
    profiles:
      - deploy  # Use --profile deploy to include this service

volumes:
  kafka-data:
    driver: local
  zookeeper-data:
    driver: local
  zookeeper-logs:
    driver: local
  flink-checkpoints:
    driver: local

networks:
  blockchain-aml-network:
    driver: bridge
    name: blockchain-aml-network 