version: '3.8'

services:
  zookeeper:
    container_name: zookeeper-integrated
    image: confluentinc/cp-zookeeper:7.3.0
    environment:
      ZOOKEEPER_CLIENT_PORT: 2181
      ZOOKEEPER_TICK_TIME: 2000
    networks:
      - blockchain-aml-integrated-network

  kafka:
    container_name: kafka-integrated
    image: confluentinc/cp-kafka:7.3.0
    depends_on:
      - zookeeper
    ports:
      - "9092:9092"
    environment:
      KAFKA_BROKER_ID: 1
      KAFKA_ZOOKEEPER_CONNECT: zookeeper:2181
      KAFKA_ADVERTISED_LISTENERS: PLAINTEXT://kafka:29092,PLAINTEXT_HOST://localhost:9092
      KAFKA_LISTENER_SECURITY_PROTOCOL_MAP: PLAINTEXT:PLAINTEXT,PLAINTEXT_HOST:PLAINTEXT
      KAFKA_INTER_BROKER_LISTENER_NAME: PLAINTEXT
      KAFKA_OFFSETS_TOPIC_REPLICATION_FACTOR: 1
      KAFKA_TRANSACTION_STATE_LOG_MIN_ISR: 1
      KAFKA_TRANSACTION_STATE_LOG_REPLICATION_FACTOR: 1
      KAFKA_GROUP_INITIAL_REBALANCE_DELAY_MS: 0
      KAFKA_AUTO_CREATE_TOPICS_ENABLE: "true"
      KAFKA_LOG_RETENTION_HOURS: 24
      KAFKA_LOG_RETENTION_BYTES: 1073741824
    networks:
      - blockchain-aml-integrated-network

  kafka-ui:
    container_name: kafka-ui-integrated
    image: provectuslabs/kafka-ui:latest
    ports:
      - "8080:8080"
    environment:
      KAFKA_CLUSTERS_0_NAME: blockchain-aml
      KAFKA_CLUSTERS_0_BOOTSTRAPSERVERS: kafka:29092
      KAFKA_CLUSTERS_0_ZOOKEEPER: zookeeper:2181
    depends_on:
      - kafka
    networks:
      - blockchain-aml-integrated-network

  kafka-init:
    container_name: kafka-init-integrated
    image: confluentinc/cp-kafka:7.3.0
    depends_on:
      - kafka
    command: >
      bash -c "
        echo 'Waiting for Kafka to be ready...'
        cub kafka-ready -b kafka:29092 1 30
        echo 'Creating Kafka topics...'
        kafka-topics --create --if-not-exists --bootstrap-server kafka:29092 --partitions 3 --replication-factor 1 --topic ethereum-transactions
        kafka-topics --create --if-not-exists --bootstrap-server kafka:29092 --partitions 3 --replication-factor 1 --topic filtered-transactions
        kafka-topics --create --if-not-exists --bootstrap-server kafka:29092 --partitions 3 --replication-factor 1 --topic graph-snapshots
        echo 'Kafka topics created'
      "
    networks:
      - blockchain-aml-integrated-network

  jobmanager:
    container_name: flink-jobmanager-integrated
    image: apache/flink:1.18.0-scala_2.12-java11
    ports:
      - "8081:8081"
    command: jobmanager
    environment:
      - JOB_MANAGER_RPC_ADDRESS=jobmanager
      - |
        FLINK_PROPERTIES=
        jobmanager.rpc.address: jobmanager
        jobmanager.memory.process.size: 1600m
        taskmanager.memory.process.size: 1728m
    networks:
      - blockchain-aml-integrated-network

  taskmanager-1:
    container_name: docker-flink-taskmanager-1
    image: apache/flink:1.18.0-scala_2.12-java11
    depends_on:
      - jobmanager
    command: taskmanager
    environment:
      - JOB_MANAGER_RPC_ADDRESS=jobmanager
      - |
        FLINK_PROPERTIES=
        jobmanager.rpc.address: jobmanager
        taskmanager.numberOfTaskSlots: 2
        taskmanager.memory.process.size: 1728m
    networks:
      - blockchain-aml-integrated-network

  taskmanager-2:
    container_name: docker-flink-taskmanager-2
    image: apache/flink:1.18.0-scala_2.12-java11
    depends_on:
      - jobmanager
    command: taskmanager
    environment:
      - JOB_MANAGER_RPC_ADDRESS=jobmanager
      - |
        FLINK_PROPERTIES=
        jobmanager.rpc.address: jobmanager
        taskmanager.numberOfTaskSlots: 2
        taskmanager.memory.process.size: 1728m
    networks:
      - blockchain-aml-integrated-network

  sepolia-ingestion:
    container_name: sepolia-ingestion-integrated
    build:
      context: ../../src/ingestion
      dockerfile: Dockerfile
    depends_on:
      - kafka
      - kafka-init
    restart: on-failure
    environment:
      - KAFKA_BOOTSTRAP_SERVERS=kafka:29092
      - KAFKA_TRANSACTIONS_TOPIC=ethereum-transactions
      - ETHEREUM_NETWORK=sepolia
      - infura_project_id=${INFURA_API_KEY:-********************************}
      - infura_project_secret=${INFURA_API_SECRET:-********************************}
      - INGESTION_MODE=live
      - BATCH_SIZE=5
      - LOG_LEVEL=DEBUG
      # 添加Infura API请求速率限制参数
      - INFURA_REQUEST_INTERVAL=1.0
      - INFURA_BATCH_SIZE=3
      - INFURA_MAX_RETRIES=5
    networks:
      - blockchain-aml-integrated-network

  neo4j:
    container_name: neo4j-graph-store
    image: neo4j:5.15-community
    ports:
      - "7474:7474"  # HTTP
      - "7687:7687"  # Bolt
    environment:
      - NEO4J_AUTH=neo4j/password123
      - NEO4J_PLUGINS=["apoc"]
      - NEO4J_dbms_security_procedures_unrestricted=apoc.*
      - NEO4J_dbms_memory_heap_initial__size=512m
      - NEO4J_dbms_memory_heap_max__size=2G
      - NEO4J_dbms_memory_pagecache_size=1G
    volumes:
      - neo4j-data:/data
      - neo4j-logs:/logs
    networks:
      - blockchain-aml-integrated-network

  graph-construction-layer:
    container_name: graph-construction-layer
    build:
      context: ../../src/graph_layer
      dockerfile: graph_layer_fixed.Dockerfile
    depends_on:
      - kafka
      - neo4j
    environment:
      - KAFKA_BOOTSTRAP_SERVERS=kafka:29092
      - KAFKA_INPUT_TOPIC=filtered-transactions
      - KAFKA_OUTPUT_TOPIC=graph-snapshots
      - KAFKA_CONSUMER_GROUP_ID=graph-construction-consumer-context7
      - KAFKA_STARTING_OFFSETS=earliest
      - KAFKA_AUTO_OFFSET_RESET=earliest
      - WINDOW_DURATION_SECONDS=30
      - WATERMARK_DELAY_SECONDS=15
      - SLIDING_INTERVAL_MINUTES=1
      - LOG_LEVEL=INFO
      - PYTHONPATH=/app
      # Neo4j connection settings
      - NEO4J_URI=bolt://neo4j:7687
      - NEO4J_USER=neo4j
      - NEO4J_PASSWORD=password123
    ports:
      - "4040:4040"
    networks:
      - blockchain-aml-integrated-network

volumes:
  neo4j-data:
    driver: local
  neo4j-logs:
    driver: local

networks:
  blockchain-aml-integrated-network:
    name: blockchain-aml-integrated-network

neo4j:
  container_name: neo4j-graph-store
  image: neo4j:5.15-community
  ports:
    - "7474:7474"  # HTTP
    - "7687:7687"  # Bolt
  environment:
    - NEO4J_AUTH=neo4j/password123
    - NEO4J_PLUGINS=["apoc", "graph-data-science"]
    - NEO4J_dbms_security_procedures_unrestricted=apoc.*,gds.*
  volumes:
    - neo4j-data:/data
    - neo4j-logs:/logs
  networks:
    - blockchain-aml-integrated-network
