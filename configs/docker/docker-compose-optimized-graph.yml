version: '3.8'

services:
  zookeeper:
    image: confluentinc/cp-zookeeper:7.4.0
    environment:
      ZOOKEEPER_CLIENT_PORT: 2181
      ZOOKEEPER_TICK_TIME: 2000
    ports:
      - "2181:2181"
    networks:
      - graph-network

  kafka:
    image: confluentinc/cp-kafka:7.4.0
    depends_on:
      - zookeeper
    ports:
      - "29092:29092"
    environment:
      KAFKA_BROKER_ID: 1
      KAFKA_ZOOKEEPER_CONNECT: zookeeper:2181
      KAFKA_ADVERTISED_LISTENERS: PLAINTEXT://kafka:29092
      KAFKA_OFFSETS_TOPIC_REPLICATION_FACTOR: 1
      KAFKA_AUTO_CREATE_TOPICS_ENABLE: 'true'
      KAFKA_NUM_PARTITIONS: 8
      KAFKA_DEFAULT_REPLICATION_FACTOR: 1
    networks:
      - graph-network

  # Sepolia Ingestion Service
  sepolia-ingestion:
    build:
      context: .
      dockerfile: ingestion/Dockerfile
    environment:
      - INFURA_PROJECT_ID=${INFURA_PROJECT_ID:-your_infura_project_id}
      - KAFKA_BOOTSTRAP_SERVERS=kafka:29092
      - KAFKA_TOPIC=raw-transactions
      - NETWORK_NAME=sepolia
      - BATCH_SIZE=100
      - PROCESSING_INTERVAL=10
    depends_on:
      - kafka
    volumes:
      - ./logs:/app/logs
    networks:
      - graph-network
    restart: unless-stopped

  # Enhanced CEP Layer
  cep-layer:
    build:
      context: .
      dockerfile: cep_layer/Dockerfile
    environment:
      - KAFKA_BOOTSTRAP_SERVERS=kafka:29092
      - INPUT_TOPIC=raw-transactions
      - OUTPUT_TOPIC=filtered-transactions
      - FLINK_PARALLELISM=4
      - FLINK_CHECKPOINT_INTERVAL=30000
    depends_on:
      - kafka
    volumes:
      - ./logs:/app/logs
    networks:
      - graph-network
    restart: unless-stopped

  # Optimized Graph Construction Layer
  graph-layer:
    build:
      context: .
      dockerfile: graph_layer/Dockerfile.optimized
    environment:
      # Kafka Configuration
      - KAFKA_BOOTSTRAP_SERVERS=kafka:29092
      - KAFKA_INPUT_TOPIC=filtered-transactions
      - KAFKA_OUTPUT_TOPIC=graph-snapshots
      - KAFKA_CONSUMER_GROUP=graph-construction-consumer-v2
      
      # Spark Performance Configuration
      - SPARK_EXECUTOR_MEMORY=4g
      - SPARK_DRIVER_MEMORY=2g
      - SPARK_EXECUTOR_CORES=4
      - SPARK_SQL_ADAPTIVE_ENABLED=true
      
      # Streaming Performance Settings
      - TRIGGER_PROCESSING_TIME=10 seconds
      - MAX_OFFSETS_PER_TRIGGER=5000
      - WINDOW_DURATION=5 minutes
      - SLIDING_DURATION=30 seconds
      - WATERMARK_DELAY=1 minute
      
      # Graph Construction Optimization
      - MAX_NODES_PER_GRAPH=5000
      - EDGE_WEIGHT_THRESHOLD=0.001
      - NODE_FEATURE_DIM=32
      - BATCH_SIZE=2000
      - PARALLELISM=8
      
      # Performance Features
      - ENABLE_VECTORIZED_OPERATIONS=true
      - ENABLE_FEATURE_CACHING=true
      - ENABLE_GRAPH_PRUNING=true
      - ENABLE_DETAILED_METRICS=true
      - ENABLE_PERFORMANCE_LOGGING=true
      - ENABLE_COMPRESSION=true
      
      # Resource Management
      - JAVA_OPTS=-XX:+UseG1GC -XX:+UnlockDiagnosticVMOptions -Xmx4g
      
    depends_on:
      - kafka
      - cep-layer
    volumes:
      - ./logs:/app/logs
      - ./data:/app/data
    ports:
      - "4040:4040"  # Spark UI
      - "8085:8085"  # Metrics port
    networks:
      - graph-network
    restart: unless-stopped

networks:
  graph-network:
    driver: bridge

volumes:
  kafka-data:
  zookeeper-data:
  graph-logs:
  graph-checkpoints:
