version: '3.8'

services:
  # Zookeeper for Kafka coordination
  zookeeper:
    image: confluentinc/cp-zookeeper:7.4.0
    hostname: zookeeper
    container_name: aml-zookeeper
    environment:
      ZOOKEEPER_CLIENT_PORT: 2181
      ZOOKEEPER_TICK_TIME: 2000
      ZOOKEEPER_SYNC_LIMIT: 2
    healthcheck:
      test: ["CMD", "echo", "ruok", "|", "nc", "localhost", "2181"]
      interval: 30s
      timeout: 10s
      retries: 3
    networks:
      - aml-network

  # Kafka broker for transaction streaming
  kafka:
    image: confluentinc/cp-kafka:7.4.0
    hostname: kafka
    container_name: aml-kafka
    depends_on:
      zookeeper:
        condition: service_healthy
    environment:
      KAFKA_BROKER_ID: 1
      KAFKA_ZOOKEEPER_CONNECT: 'zookeeper:2181'
      KAFKA_LISTENER_SECURITY_PROTOCOL_MAP: PLAINTEXT:PLAINTEXT,PLAINTEXT_HOST:PLAINTEXT
      KAFKA_ADVERTISED_LISTENERS: PLAINTEXT://kafka:29092,PLAINTEXT_HOST://localhost:9092
      KAFKA_INTER_BROKER_LISTENER_NAME: PLAINTEXT
      KAFKA_OFFSETS_TOPIC_REPLICATION_FACTOR: 1
      KAFKA_TRANSACTION_STATE_LOG_MIN_ISR: 1
      KAFKA_TRANSACTION_STATE_LOG_REPLICATION_FACTOR: 1
      KAFKA_LOG_RETENTION_HOURS: 168  # 7 days
      KAFKA_LOG_SEGMENT_BYTES: **********  # 1GB
      # Performance optimizations
      KAFKA_NUM_NETWORK_THREADS: 8
      KAFKA_NUM_IO_THREADS: 8
      KAFKA_SOCKET_SEND_BUFFER_BYTES: 102400
      KAFKA_SOCKET_RECEIVE_BUFFER_BYTES: 102400
    ports:
      - "9092:9092"
    healthcheck:
      test: ["CMD", "kafka-broker-api-versions", "--bootstrap-server", "localhost:9092"]
      interval: 30s
      timeout: 10s
      retries: 3
    volumes:
      - kafka-data:/var/lib/kafka/data
    networks:
      - aml-network

  # Kafka UI for monitoring
  kafka-ui:
    image: provectuslabs/kafka-ui:latest
    container_name: aml-kafka-ui
    depends_on:
      kafka:
        condition: service_healthy
    environment:
      KAFKA_CLUSTERS_0_NAME: aml-cluster
      KAFKA_CLUSTERS_0_BOOTSTRAPSERVERS: kafka:29092
      KAFKA_CLUSTERS_0_ZOOKEEPER: zookeeper:2181
      SERVER_SERVLET_CONTEXT_PATH: /kafka-ui
    ports:
      - "8080:8080"
    networks:
      - aml-network

  # Ingestion service
  ingestion-service:
    build:
      context: ./ingestion
      dockerfile: Dockerfile
    container_name: aml-ingestion
    depends_on:
      kafka:
        condition: service_healthy
    environment:
      - KAFKA_BOOTSTRAP_SERVERS=kafka:29092
      - INFURA_PROJECT_ID=${INFURA_PROJECT_ID}
      - INFURA_PROJECT_SECRET=${INFURA_PROJECT_SECRET}
      - ETHEREUM_NETWORK=sepolia
      - LOG_LEVEL=INFO
    env_file:
      - .env
    volumes:
      - ./ingestion:/app
      - ./logs:/app/logs
    networks:
      - aml-network
    restart: unless-stopped

volumes:
  kafka-data:
    driver: local

networks:
  aml-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16 