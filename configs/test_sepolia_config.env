# Infura API Configuration - SEPOLIA TESTNET
INFURA_PROJECT_ID=********************************
INFURA_PROJECT_SECRET=******************************************
ETHEREUM_NETWORK=sepolia

# Infura WebSocket Settings - Conservative rate limiting
INFURA_WS_RECONNECT_DELAY=10
INFURA_WS_PING_INTERVAL=60
INFURA_WS_TIMEOUT=120
INFURA_REQUESTS_PER_SECOND=2
INFURA_MAX_RETRIES=5
INFURA_REQUEST_INTERVAL=0.5
INFURA_BATCH_SIZE=5

# Kafka Configuration
KAFKA_BOOTSTRAP_SERVERS=localhost:9092
KAFKA_TRANSACTIONS_TOPIC=ethereum-transactions
KAFKA_BLOCKS_TOPIC=ethereum-blocks

# Kafka Producer Settings
KAFKA_ACKS=all
KAFKA_RETRIES=3
KAFKA_BATCH_SIZE=16384
KAFKA_LINGER_MS=5
KAFKA_BUFFER_MEMORY=33554432

# Kafka Consumer Settings
KAFKA_GROUP_ID=aml-ingestion
KAFKA_AUTO_OFFSET_RESET=latest
KAFKA_ENABLE_AUTO_COMMIT=true

# Kafka Topic Management
KAFKA_NUM_PARTITIONS=3
KAFKA_REPLICATION_FACTOR=1

# Service Configuration
SERVICE_NAME=aml-ingestion-sepolia
LOG_LEVEL=INFO

# Data Processing
INGESTION_BATCH_SIZE=100
PROCESSING_TIMEOUT=30

# Transaction Filtering
FILTER_CONTRACT_CREATION=false
FILTER_ZERO_VALUE=false
MIN_TRANSACTION_VALUE=0.01

# Error Handling
MAX_CONSECUTIVE_ERRORS=10
ERROR_BACKOFF_FACTOR=2.0
MAX_ERROR_BACKOFF=300

# Storage
LOG_DIRECTORY=./logs
DATA_DIRECTORY=./data

# Monitoring
PROMETHEUS_PORT=8000
METRICS_ENABLED=true
HEALTH_CHECK_INTERVAL=30
LOG_PERFORMANCE_METRICS=true
METRIC_COLLECTION_INTERVAL=10 