#!/usr/bin/env python3
"""
Kafka Graph Producer

This script sends test graph snapshots to Kafka.
"""

import argparse
import json
import os
import time
import random
from datetime import datetime, timedelta
from kafka import KafkaProducer

def parse_args():
    """Parse command line arguments"""
    parser = argparse.ArgumentParser(description='Send test graph snapshots to Kafka')
    parser.add_argument('--bootstrap-servers', default='localhost:9092', help='Kafka bootstrap servers')
    parser.add_argument('--topic', default='graph-snapshots', help='Kafka topic to produce to')
    parser.add_argument('--num-snapshots', type=int, default=5, help='Number of snapshots to send')
    parser.add_argument('--delay', type=float, default=1.0, help='Delay between snapshots in seconds')
    parser.add_argument('--input-file', default='test_graph_snapshot.json', help='Input file with test snapshot data')
    parser.add_argument('--randomize', action='store_true', help='Randomize snapshot data')
    return parser.parse_args()

def load_test_snapshot(file_path):
    """Load test snapshot data from file"""
    try:
        with open(file_path, 'r') as f:
            return json.load(f)
    except Exception as e:
        print(f"Error loading test snapshot: {str(e)}")
        return create_test_snapshot()

def create_test_snapshot():
    """Create a test snapshot with random data"""
    num_nodes = random.randint(5, 10)
    num_edges = random.randint(num_nodes, num_nodes * 2)
    
    # Create node features
    node_features = []
    for _ in range(num_nodes):
        features = [random.uniform(0, 1) for _ in range(3)]
        node_features.append(features)
    
    # Create edge index
    sources = []
    targets = []
    for _ in range(num_edges):
        source = random.randint(0, num_nodes - 1)
        target = random.randint(0, num_nodes - 1)
        while target == source:
            target = random.randint(0, num_nodes - 1)
        sources.append(source)
        targets.append(target)
    
    # Create edge attributes
    edge_attr = []
    total_volume = 0
    for _ in range(num_edges):
        amount = random.uniform(0.1, 5.0)
        total_volume += amount
        edge_attr.append([amount])
    
    # Create address mapping
    address_mapping = {}
    for i in range(num_nodes):
        address = f"0x{''.join(random.choices('0123456789abcdef', k=40))}"
        address_mapping[str(i)] = address
    
    # Create risk scores
    risk_scores = {}
    for i in range(num_nodes):
        risk_scores[str(i)] = random.uniform(0, 1)
    
    # Create snapshot
    now = datetime.now()
    window_start = now - timedelta(minutes=5)
    window_end = now
    
    snapshot = {
        "graph_data": {
            "node_features": node_features,
            "edge_index": [sources, targets],
            "edge_attr": edge_attr,
            "num_nodes": num_nodes
        },
        "window_start": window_start.isoformat(),
        "window_end": window_end.isoformat(),
        "num_nodes": num_nodes,
        "num_edges": num_edges,
        "max_risk_score": max(risk_scores.values()) if risk_scores else 0,
        "total_volume": total_volume,
        "construction_time_ms": random.uniform(5, 15),
        "processing_stats": {
            "construction_time_ms": random.uniform(5, 15),
            "preprocessing_ms": random.uniform(1, 5),
            "extraction_ms": random.uniform(3, 8),
            "pruning_ms": random.uniform(0.001, 0.01),
            "mapping_ms": random.uniform(0.001, 0.01),
            "pyg_construction_ms": random.uniform(0.1, 1),
            "metadata_ms": random.uniform(0.05, 0.1),
            "transactions_per_ms": random.uniform(0.5, 1),
            "nodes_per_ms": random.uniform(0.5, 1),
            "edges_per_ms": random.uniform(0.5, 1)
        },
        "metadata": {
            "transaction_count": num_edges,
            "unique_addresses": num_nodes,
            "total_volume": total_volume,
            "avg_transaction_value": total_volume / num_edges if num_edges > 0 else 0,
            "median_transaction_value": random.uniform(0.5, 2),
            "avg_node_degree": num_edges * 2 / num_nodes if num_nodes > 0 else 0,
            "max_node_degree": random.randint(2, 5),
            "avg_edge_weight": total_volume / num_edges if num_edges > 0 else 0,
            "max_edge_weight": max([attr[0] for attr in edge_attr]) if edge_attr else 0,
            "graph_density": num_edges / (num_nodes * (num_nodes - 1)) if num_nodes > 1 else 0,
            "construction_method": "vectorized",
            "feature_caching_enabled": True,
            "graph_pruning_enabled": True
        },
        "address_mapping": address_mapping,
        "risk_scores": risk_scores
    }
    
    return snapshot

def randomize_snapshot(snapshot):
    """Randomize an existing snapshot"""
    # Get the number of nodes and edges
    num_nodes = snapshot.get('num_nodes', 0)
    num_edges = snapshot.get('num_edges', 0)
    
    if num_nodes > 0 and num_edges > 0:
        # Randomize risk scores
        if 'risk_scores' in snapshot:
            for idx in snapshot['risk_scores']:
                snapshot['risk_scores'][idx] = random.uniform(0, 1)
            snapshot['max_risk_score'] = max(snapshot['risk_scores'].values()) if snapshot['risk_scores'] else 0
        
        # Randomize edge weights
        edge_attr = snapshot.get('graph_data', {}).get('edge_attr', [])
        total_volume = 0
        if edge_attr:
            for i in range(len(edge_attr)):
                if isinstance(edge_attr[i], list) and len(edge_attr[i]) > 0:
                    amount = random.uniform(0.1, 5.0)
                    edge_attr[i][0] = amount
                    total_volume += amount
            
            snapshot['total_volume'] = total_volume
            if 'metadata' in snapshot:
                snapshot['metadata']['total_volume'] = total_volume
                snapshot['metadata']['avg_transaction_value'] = total_volume / num_edges if num_edges > 0 else 0
                snapshot['metadata']['avg_edge_weight'] = total_volume / num_edges if num_edges > 0 else 0
                snapshot['metadata']['max_edge_weight'] = max([attr[0] for attr in edge_attr]) if edge_attr else 0
        
        # Update timestamps
        now = datetime.now()
        window_start = now - timedelta(minutes=5)
        window_end = now
        snapshot['window_start'] = window_start.isoformat()
        snapshot['window_end'] = window_end.isoformat()
    
    return snapshot

def produce_graph_snapshots(bootstrap_servers, topic, num_snapshots, delay, input_file, randomize):
    """Produce graph snapshots to Kafka"""
    # Create Kafka producer
    producer = KafkaProducer(
        bootstrap_servers=bootstrap_servers,
        value_serializer=lambda x: json.dumps(x).encode('utf-8')
    )
    
    # Load test snapshot
    base_snapshot = load_test_snapshot(input_file)
    
    # Send snapshots
    for i in range(num_snapshots):
        # Create or randomize snapshot
        if randomize:
            snapshot = randomize_snapshot(base_snapshot.copy())
        else:
            snapshot = base_snapshot.copy()
        
        # Send to Kafka
        producer.send(topic, value=snapshot)
        producer.flush()
        
        print(f"Sent snapshot {i+1}/{num_snapshots} with {snapshot.get('num_nodes', 0)} nodes and {snapshot.get('num_edges', 0)} edges")
        
        if i < num_snapshots - 1:
            time.sleep(delay)
    
    producer.close()
    
    return num_snapshots

def main():
    """Main entry point"""
    args = parse_args()
    
    count = produce_graph_snapshots(
        args.bootstrap_servers,
        args.topic,
        args.num_snapshots,
        args.delay,
        args.input_file,
        args.randomize
    )
    
    print(f"Produced {count} graph snapshots")

if __name__ == "__main__":
    main() 