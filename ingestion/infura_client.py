"""
Infura WebSocket client for real-time Ethereum data streaming
Updated with latest Web3.py AsyncWeb3 patterns from Context7
"""

import asyncio
import structlog
from typing import Optional, Dict, Any, Callable, List
from dataclasses import dataclass, asdict
from datetime import datetime
import json
import time
import random

# Updated imports based on Context7 Web3.py patterns
from web3 import AsyncWeb3
from web3.providers.persistent import WebSocketProvider
from web3.utils.subscriptions import (
    NewHeadsSubscription,
    NewHeadsSubscriptionContext,
    LogsSubscription,
    LogsSubscriptionContext,
)
from web3.exceptions import Web3Exception

from config.settings import IngestionSettings

logger = structlog.get_logger(__name__)


@dataclass
class Transaction:
    """Enhanced transaction model for AML analysis"""
    hash: str
    block_number: int
    block_hash: str
    transaction_index: int
    from_address: str
    to_address: Optional[str]
    value: int  # in wei
    gas: int
    gas_price: int
    gas_used: Optional[int] = None
    nonce: int = 0
    timestamp: float = 0.0
    input_data: str = ""
    status: Optional[int] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for JSON serialization"""
        return asdict(self)
    
    @property
    def value_eth(self) -> float:
        """Convert value from wei to ETH"""
        return self.value / 10**18


class InfuraStreamingClient:
    """
    Enhanced Infura WebSocket client using latest AsyncWeb3 patterns
    Based on Context7 documentation for real-time subscriptions
    """
    
    def __init__(self, settings: IngestionSettings):
        self.settings = settings
        self.w3: Optional[AsyncWeb3] = None
        self.transaction_callback: Optional[Callable] = None
        self.block_callback: Optional[Callable] = None
        self._running = False
        self._error_count = 0
        
        # 请求速率限制参数，从设置中获取
        self._request_interval = settings.infura_request_interval
        self._batch_size = settings.infura_batch_size
        self._last_request_time = 0
        self._max_retries = settings.infura_max_retries
        self._base_backoff = 2  # 基础退避时间（秒）
        
    async def connect(self) -> bool:
        """
        Establish WebSocket connection using Context7 AsyncWeb3 patterns
        """
        try:
            # Create WebSocket provider with latest patterns
            provider = WebSocketProvider(self.settings.infura_wss_url)
            
            # Initialize AsyncWeb3 with context manager pattern  
            self.w3 = await AsyncWeb3(provider)
            
            # Verify connection
            if await self.w3.is_connected():
                logger.info("Successfully connected to Infura WebSocket",
                           network=self.settings.ethereum_network,
                           endpoint=self.settings.infura_wss_url)
                return True
            else:
                logger.error("Failed to establish WebSocket connection")
                return False
                
        except Exception as e:
            logger.error("Error connecting to Infura WebSocket", 
                        error=str(e), 
                        endpoint=self.settings.infura_wss_url)
            return False
    
    async def disconnect(self):
        """Properly disconnect WebSocket connection"""
        if self.w3 and self.w3.provider:
            try:
                await self.w3.provider.disconnect()
                logger.info("Disconnected from Infura WebSocket")
            except Exception as e:
                logger.warning("Error disconnecting from WebSocket", error=str(e))
    
    def set_transaction_callback(self, callback: Callable[[Transaction], None]):
        """Set callback for transaction processing"""
        self.transaction_callback = callback
    
    def set_block_callback(self, callback: Callable[[Dict], None]):
        """Set callback for block processing"""
        self.block_callback = callback
    
    async def _new_heads_handler(self, handler_context: NewHeadsSubscriptionContext) -> None:
        """
        Handler for new block headers using Context7 patterns
        """
        try:
            # Get block data - in the latest version, the result is in handler_context.result
            header = handler_context.result
            block_number = header.get('number', 0)
            
            logger.debug("New block header received", 
                        block_number=block_number,
                        block_hash=header.get('hash'))
            
            # Implement exponential backoff for block retrieval
            block = await self._retry_with_backoff(
                lambda: self.w3.eth.get_block(block_number, full_transactions=True)
            )
            
            if not block:
                logger.warning("Failed to retrieve block details", block_number=block_number)
                return
                
            # Convert block to dict for callback
            block_data = {
                'number': int(block.number),
                'hash': block.hash.hex() if hasattr(block.hash, 'hex') else str(block.hash),
                'timestamp': int(block.timestamp),
                'miner': str(block.miner),
                'transaction_count': len(block.transactions),
                'gas_used': int(block.gasUsed),
                'gas_limit': int(block.gasLimit)
            }
            
            # Execute block callback if set
            if self.block_callback:
                await self.block_callback(block_data)
            
            # Process transactions in the block
            if self.transaction_callback:
                await self._process_block_transactions(block)
                
        except Exception as e:
            logger.error("Error processing block", 
                        error=str(e),
                        block_number=block_number if 'block_number' in locals() else 'unknown')
            self._error_count += 1
    
    async def _retry_with_backoff(self, operation, max_retries=None):
        """通用的指数退避重试函数"""
        if max_retries is None:
            max_retries = self._max_retries
            
        retries = 0
        while retries <= max_retries:
            try:
                return await operation()
            except Exception as e:
                retries += 1
                if retries > max_retries:
                    logger.error(f"Operation failed after {max_retries} retries", error=str(e))
                    return None
                
                # 计算退避时间（带有随机抖动）
                backoff_time = self._base_backoff * (2 ** (retries - 1)) * (0.5 + random.random())
                logger.warning(f"Operation failed, retrying in {backoff_time:.2f}s (attempt {retries}/{max_retries})", error=str(e))
                await asyncio.sleep(backoff_time)
    
    async def _process_block_transactions(self, block) -> None:
        """Process transactions in a block with rate limiting and batching"""
        if not self.transaction_callback:
            return
            
        try:
            transactions = block.transactions
            logger.info(f"Processing {len(transactions)} transactions from block {block.number}")
            
            # 将交易分批处理，减少API请求频率
            for i in range(0, len(transactions), self._batch_size):
                batch = transactions[i:i + self._batch_size]
                
                # 处理这一批交易
                for tx_data in batch:
                    try:
                        # 确保请求间隔
                        current_time = time.time()
                        time_since_last = current_time - self._last_request_time
                        if time_since_last < self._request_interval:
                            await asyncio.sleep(self._request_interval - time_since_last)
                        
                        # 使用重试机制获取交易收据
                        tx_receipt = await self._retry_with_backoff(
                            lambda: self.w3.eth.get_transaction_receipt(tx_data['hash'])
                        )
                        self._last_request_time = time.time()
                        
                        if not tx_receipt:
                            continue
                        
                        # Convert Web3.py objects to JSON-serializable types
                        transaction = Transaction(
                            hash=tx_data['hash'].hex() if hasattr(tx_data['hash'], 'hex') else str(tx_data['hash']),
                            block_number=int(block.number),
                            block_hash=block.hash.hex() if hasattr(block.hash, 'hex') else str(block.hash),
                            transaction_index=int(tx_data.get('transactionIndex', 0)),
                            from_address=str(tx_data['from']),
                            to_address=str(tx_data['to']) if tx_data.get('to') else None,
                            value=int(tx_data['value']),
                            gas=int(tx_data['gas']),
                            gas_price=int(tx_data.get('gasPrice', 0)),
                            gas_used=int(tx_receipt.get('gasUsed', 0)) if tx_receipt.get('gasUsed') else None,
                            nonce=int(tx_data['nonce']),
                            timestamp=float(block.timestamp),
                            input_data=tx_data.get('input', '').hex() if hasattr(tx_data.get('input', ''), 'hex') else str(tx_data.get('input', '')),
                            status=int(tx_receipt.get('status', 0)) if tx_receipt.get('status') is not None else None
                        )
                        
                        # Apply transaction filters
                        if self._should_process_transaction(transaction):
                            await self.transaction_callback(transaction)
                            
                    except Exception as e:
                        tx_hash_str = tx_data['hash'].hex() if hasattr(tx_data.get('hash'), 'hex') else str(tx_data.get('hash', 'unknown'))[:20]
                        logger.warning("Error processing transaction", 
                                     tx_hash=tx_hash_str,
                                     error=str(e))
                
                # 批次间添加额外延迟，避免触发速率限制
                await asyncio.sleep(self._request_interval * 2)
                
        except Exception as e:
            logger.error("Error processing block transactions", 
                       block_number=block.number,
                       error=str(e))
    
    def _should_process_transaction(self, transaction: Transaction) -> bool:
        """Apply transaction filtering rules"""
        # Filter contract creation if enabled
        if self.settings.filter_contract_creation and transaction.to_address is None:
            return False
        
        # Filter zero value transactions if enabled
        if self.settings.filter_zero_value and transaction.value == 0:
            return False
        
        # Filter by minimum transaction value if set
        if (self.settings.min_transaction_value is not None and 
            transaction.value_eth < self.settings.min_transaction_value):
            return False
        
        return True
    
    async def start_streaming(self) -> None:
        """
        Start real-time streaming using Context7 subscription manager patterns
        """
        if not self.w3:
            raise RuntimeError("Not connected to Infura. Call connect() first.")
        
        try:
            self._running = True
            logger.info("Starting real-time Ethereum data streaming")
            
            # Subscribe to new block headers using Context7 patterns
            await self.w3.subscription_manager.subscribe([
                NewHeadsSubscription(
                    label="ethereum-blocks",
                    handler=self._new_heads_handler
                )
            ])
            
            # Handle subscriptions using the latest pattern
            logger.info("Listening for new blocks and transactions...")
            await self.w3.subscription_manager.handle_subscriptions()
            
        except Exception as e:
            logger.error("Error in streaming", error=str(e))
            self._error_count += 1
            raise
        finally:
            self._running = False
    
    async def stop_streaming(self):
        """Stop the streaming process"""
        self._running = False
        if self.w3 and hasattr(self.w3, 'subscription_manager'):
            try:
                await self.w3.subscription_manager.unsubscribe_all()
                logger.info("Stopped all subscriptions")
            except Exception as e:
                logger.warning("Error stopping subscriptions", error=str(e))
    
    @property
    def is_connected(self) -> bool:
        """Check if WebSocket is connected"""
        return self.w3 is not None and self._running
    
    @property
    def error_count(self) -> int:
        """Get current error count"""
        return self._error_count
    
    def reset_error_count(self):
        """Reset error counter"""
        self._error_count = 0 
