#!/usr/bin/env python3
"""
Kafka Graph Consumer

This script consumes graph snapshots from Kafka and saves them to a file.
"""

import argparse
import json
import os
from datetime import datetime
from kafka import KafkaConsumer

def parse_args():
    """Parse command line arguments"""
    parser = argparse.ArgumentParser(description='Consume graph snapshots from Kafka')
    parser.add_argument('--bootstrap-servers', default='localhost:9092', help='Kafka bootstrap servers')
    parser.add_argument('--topic', default='graph-snapshots', help='Kafka topic to consume from')
    parser.add_argument('--group-id', default='graph-snapshot-consumer', help='Consumer group ID')
    parser.add_argument('--output-dir', default='./graph_snapshots', help='Directory to save snapshots')
    parser.add_argument('--max-messages', type=int, default=10, help='Maximum number of messages to consume')
    parser.add_argument('--timeout-ms', type=int, default=10000, help='Consumer timeout in milliseconds')
    parser.add_argument('--from-beginning', action='store_true', help='Consume from the beginning of the topic')
    return parser.parse_args()

def consume_graph_snapshots(bootstrap_servers, topic, group_id, output_dir, max_messages, timeout_ms, from_beginning):
    """Consume graph snapshots from Kafka and save to files"""
    # Create output directory if it doesn't exist
    os.makedirs(output_dir, exist_ok=True)
    
    # Create timestamp for output files
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    
    # Create Kafka consumer
    auto_offset_reset = 'earliest' if from_beginning else 'latest'
    consumer = KafkaConsumer(
        topic,
        bootstrap_servers=bootstrap_servers,
        group_id=group_id,
        auto_offset_reset=auto_offset_reset,
        value_deserializer=lambda x: json.loads(x.decode('utf-8')),
        consumer_timeout_ms=timeout_ms
    )
    
    # Consume messages
    snapshots = []
    count = 0
    
    print(f"Consuming graph snapshots from topic {topic}...")
    
    for message in consumer:
        snapshot = message.value
        snapshots.append(snapshot)
        count += 1
        
        print(f"Received snapshot {count} with {snapshot.get('num_nodes', 0)} nodes and {snapshot.get('num_edges', 0)} edges")
        
        # Save individual snapshot to file
        single_output_path = os.path.join(output_dir, f"graph_snapshot_{count}_{timestamp}.json")
        with open(single_output_path, 'w') as f:
            json.dump(snapshot, f)
        print(f"Saved snapshot to {single_output_path}")
        
        if count >= max_messages:
            break
    
    consumer.close()
    
    # Save all snapshots to a single file
    if snapshots:
        output_path = os.path.join(output_dir, f"multiple_snapshots_{timestamp}.json")
        with open(output_path, 'w') as f:
            for snapshot in snapshots:
                f.write(json.dumps(snapshot) + '\n')
        print(f"Saved {count} snapshots to {output_path}")
    else:
        print("No snapshots received")
    
    return count, snapshots

def main():
    """Main entry point"""
    args = parse_args()
    
    count, snapshots = consume_graph_snapshots(
        args.bootstrap_servers,
        args.topic,
        args.group_id,
        args.output_dir,
        args.max_messages,
        args.timeout_ms,
        args.from_beginning
    )
    
    print(f"Consumed {count} graph snapshots")
    
    # If we got snapshots, visualize the first one
    if snapshots and count > 0:
        try:
            # Import visualization module
            import sys
            sys.path.append('.')
            import visualize_from_file
            
            # Save the first snapshot to a temporary file
            temp_file = os.path.join(args.output_dir, "temp_snapshot.json")
            with open(temp_file, 'w') as f:
                json.dump(snapshots[0], f)
            
            # Call visualization with the temporary file
            print(f"Visualizing the first snapshot...")
            # Override sys.argv for visualize_from_file.main()
            sys.argv = [
                "visualize_from_file.py",
                "--input-file", temp_file,
                "--output-dir", args.output_dir,
                "--highlight-patterns"
            ]
            visualize_from_file.main()
        except Exception as e:
            print(f"Error visualizing snapshot: {str(e)}")
            import traceback
            traceback.print_exc()

if __name__ == "__main__":
    main() 