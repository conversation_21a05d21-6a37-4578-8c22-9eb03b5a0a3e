"""
Elliptic Bitcoin Dataset Loader for TGAT Model Training

This module loads and preprocesses the Elliptic Bitcoin dataset for temporal graph neural network training.
The Elliptic dataset contains:
- 203,769 Bitcoin transactions with 166 features each
- 234,355 edges representing transaction flows
- Labels: 1=illicit, 2=licit, unknown=unlabeled

Dataset Paper: "The Elliptic Data Set: opening up machine learning on the blockchain" (2019)
"""

import pandas as pd
import numpy as np
import torch
from torch_geometric.data import Data, TemporalData
from sklearn.preprocessing import StandardScaler, LabelEncoder
from sklearn.model_selection import train_test_split
import logging
from typing import Dict, Tuple, Optional, List
import pickle
import os

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class EllipticDatasetLoader:
    """
    Data loader for the Elliptic Bitcoin dataset with temporal graph support for TGAT.
    """
    
    def __init__(self, dataset_path: str = "dataset copy"):
        """
        Initialize the Elliptic dataset loader.
        
        Args:
            dataset_path: Path to the dataset directory containing CSV files
        """
        self.dataset_path = dataset_path
        self.features_file = os.path.join(dataset_path, "elliptic_txs_features.csv")
        self.classes_file = os.path.join(dataset_path, "elliptic_txs_classes.csv")
        self.edgelist_file = os.path.join(dataset_path, "elliptic_txs_edgelist.csv")
        
        # Data containers
        self.features_df = None
        self.classes_df = None
        self.edges_df = None
        self.node_mapping = None
        self.temporal_data = None
        self.scaler = StandardScaler()
        
    def load_raw_data(self) -> None:
        """Load raw CSV files into pandas DataFrames."""
        logger.info("Loading Elliptic dataset...")
        
        # Load features (166 features per transaction)
        logger.info("Loading transaction features...")
        self.features_df = pd.read_csv(self.features_file, header=None)
        self.features_df.columns = ['txId'] + [f'feature_{i}' for i in range(1, 167)]
        
        # Load class labels  
        logger.info("Loading transaction classes...")
        self.classes_df = pd.read_csv(self.classes_file)
        
        # Load edges (transaction flows)
        logger.info("Loading transaction edges...")
        self.edges_df = pd.read_csv(self.edgelist_file)
        
        logger.info(f"Loaded {len(self.features_df)} transactions, {len(self.edges_df)} edges")
        
    def preprocess_features(self) -> Tuple[torch.Tensor, torch.Tensor]:
        """
        Preprocess transaction features for TGAT model.
        
        Returns:
            Tuple of (node_features, time_features) as torch tensors
        """
        logger.info("Preprocessing features...")
        
        if self.features_df is None:
            raise ValueError("Features data not loaded. Call load_raw_data() first.")
            
        # Extract numeric features (skip txId)
        feature_cols = [col for col in self.features_df.columns if col.startswith('feature_')]
        X = self.features_df[feature_cols].values
        
        # Handle missing values
        X = np.nan_to_num(X.astype(np.float64), nan=0.0, posinf=0.0, neginf=0.0)
        
        # Standardize features
        X_scaled = self.scaler.fit_transform(X)
        
        # The first feature is the time step - extract it for temporal modeling
        time_steps = self.features_df['feature_1'].values
        
        # Convert to torch tensors
        node_features = torch.FloatTensor(X_scaled)
        time_features = torch.LongTensor(time_steps.astype(int))
        
        logger.info(f"Feature matrix shape: {node_features.shape}")
        return node_features, time_features
    
    def preprocess_labels(self) -> Tuple[torch.Tensor, torch.Tensor]:
        """
        Preprocess class labels for binary classification.
        
        Returns:
            Tuple of (labels, labeled_mask) tensors
        """
        logger.info("Preprocessing labels...")
        
        # Merge features with classes
        merged = self.features_df[['txId']].merge(
            self.classes_df, on='txId', how='left'
        )
        
        # Convert labels: 1=illicit, 2=licit, unknown=unlabeled
        labels = merged['class'].values
        
        # Create binary labels: 0=licit, 1=illicit, -1=unknown
        binary_labels = np.full(len(labels), -1, dtype=int)
        binary_labels[labels == 2] = 0  # licit
        binary_labels[labels == 1] = 1  # illicit
        
        labeled_mask = binary_labels != -1
        logger.info(f"Labeled transactions: {labeled_mask.sum()} / {len(labels)}")
        logger.info(f"Illicit: {(binary_labels == 1).sum()}, Licit: {(binary_labels == 0).sum()}")
        
        return torch.LongTensor(binary_labels), torch.BoolTensor(labeled_mask)
    
    def create_node_mapping(self) -> Dict[int, int]:
        """Create mapping from transaction IDs to sequential node indices."""
        logger.info("Creating node mapping...")
        
        unique_txids = self.features_df['txId'].unique()
        self.node_mapping = {txid: idx for idx, txid in enumerate(unique_txids)}
        
        logger.info(f"Created mapping for {len(self.node_mapping)} unique nodes")
        return self.node_mapping
    
    def preprocess_edges(self) -> Tuple[torch.Tensor, torch.Tensor]:
        """
        Preprocess edges for temporal graph.
        
        Returns:
            Edge index tensor and edge timestamps
        """
        logger.info("Preprocessing edges...")
        
        # Map transaction IDs to node indices
        edges_mapped = self.edges_df.copy()
        edges_mapped['src'] = edges_mapped['txId1'].map(self.node_mapping)
        edges_mapped['dst'] = edges_mapped['txId2'].map(self.node_mapping)
        
        # Remove edges with unmapped nodes
        valid_edges = edges_mapped.dropna()
        logger.info(f"Valid edges: {len(valid_edges)} / {len(self.edges_df)}")
        
        # Create edge index
        edge_index = torch.LongTensor([
            valid_edges['src'].values,
            valid_edges['dst'].values
        ])
        
        # For temporal modeling, we'll use the time step of the source transaction
        src_times = []
        for src_txid in valid_edges['txId1']:
            time_step = self.features_df[self.features_df['txId'] == src_txid]['feature_1'].iloc[0]
            src_times.append(time_step)
        
        edge_times = torch.LongTensor(src_times)
        
        return edge_index, edge_times
    
    def create_temporal_splits(self, train_ratio: float = 0.6, val_ratio: float = 0.2) -> Dict[str, torch.Tensor]:
        """
        Create temporal train/validation/test splits based on time steps.
        
        Args:
            train_ratio: Ratio of data for training
            val_ratio: Ratio of data for validation
            
        Returns:
            Dictionary with train/val/test masks
        """
        logger.info("Creating temporal splits...")
        
        # Get time steps for all transactions
        time_steps = self.features_df['feature_1'].values
        unique_times = np.sort(np.unique(time_steps))
        
        # Split time steps
        n_times = len(unique_times)
        train_end = int(n_times * train_ratio)
        val_end = int(n_times * (train_ratio + val_ratio))
        
        train_times = unique_times[:train_end]
        val_times = unique_times[train_end:val_end]
        test_times = unique_times[val_end:]
        
        # Create masks
        train_mask = np.isin(time_steps, train_times)
        val_mask = np.isin(time_steps, val_times)
        test_mask = np.isin(time_steps, test_times)
        
        logger.info(f"Temporal splits - Train: {train_mask.sum()}, Val: {val_mask.sum()}, Test: {test_mask.sum()}")
        
        return {
            'train_mask': torch.BoolTensor(train_mask),
            'val_mask': torch.BoolTensor(val_mask),
            'test_mask': torch.BoolTensor(test_mask)
        }
    
    def prepare_for_tgat(self) -> Dict:
        """
        Prepare complete dataset for TGAT model training.
        
        Returns:
            Dictionary containing all processed data for TGAT
        """
        logger.info("Preparing dataset for TGAT model...")
        
        # Load and preprocess all data
        self.load_raw_data()
        self.create_node_mapping()
        
        node_features, time_features = self.preprocess_features()
        labels, labeled_mask = self.preprocess_labels()
        edge_index, edge_times = self.preprocess_edges()
        splits = self.create_temporal_splits()
        
        # Create edge features (simple features for now)
        edge_features = torch.ones(edge_index.size(1), 8)  # 8-dimensional edge features
        
        # Prepare data dictionary
        data_dict = {
            # Node data
            'x': node_features,  # [num_nodes, num_features]
            'y': labels,         # [num_nodes]
            't': time_features,  # [num_nodes] - time steps
            
            # Edge data  
            'edge_index': edge_index,      # [2, num_edges]
            'edge_attr': edge_features,    # [num_edges, edge_features]
            'edge_time': edge_times,       # [num_edges]
            
            # Masks
            'labeled_mask': labeled_mask,  # [num_nodes] - which nodes have labels
            'train_mask': splits['train_mask'],
            'val_mask': splits['val_mask'],
            'test_mask': splits['test_mask'],
            
            # Metadata
            'num_nodes': len(self.node_mapping),
            'num_features': node_features.size(1),
            'num_classes': 2,  # binary classification
            'node_mapping': self.node_mapping,
            'scaler': self.scaler
        }
        
        logger.info("✅ Dataset preparation complete!")
        logger.info(f"   Nodes: {data_dict['num_nodes']}")
        logger.info(f"   Edges: {data_dict['edge_index'].size(1)}")
        logger.info(f"   Features: {data_dict['num_features']}")
        logger.info(f"   Labeled nodes: {data_dict['labeled_mask'].sum()}")
        
        return data_dict
    
    def save_processed_data(self, data_dict: Dict, filepath: str = "elliptic_processed.pkl"):
        """Save processed data to disk for faster loading."""
        logger.info(f"Saving processed data to {filepath}...")
        with open(filepath, 'wb') as f:
            pickle.dump(data_dict, f)
        logger.info("✅ Data saved successfully!")
    
    def load_processed_data(self, filepath: str = "elliptic_processed.pkl") -> Dict:
        """Load previously processed data from disk."""
        logger.info(f"Loading processed data from {filepath}...")
        with open(filepath, 'rb') as f:
            data_dict = pickle.load(f)
        logger.info("✅ Data loaded successfully!")
        return data_dict

def get_elliptic_data(dataset_path: str = "dataset copy", force_reload: bool = False) -> Dict:
    """
    Convenience function to get processed Elliptic dataset.
    
    Args:
        dataset_path: Path to the dataset directory
        force_reload: Force reprocessing even if cached data exists
        
    Returns:
        Processed dataset dictionary
    """
    cache_file = "elliptic_processed.pkl"
    
    if not force_reload and os.path.exists(cache_file):
        loader = EllipticDatasetLoader(dataset_path)
        return loader.load_processed_data(cache_file)
    else:
        loader = EllipticDatasetLoader(dataset_path)
        data_dict = loader.prepare_for_tgat()
        loader.save_processed_data(data_dict, cache_file)
        return data_dict

if __name__ == "__main__":
    # Test the data loader
    print("🚀 Testing Elliptic Dataset Loader")
    
    # Load and process the dataset
    data = get_elliptic_data(force_reload=True)
    
    # Print dataset statistics
    print(f"\n📊 Dataset Statistics:")
    print(f"   Nodes: {data['num_nodes']:,}")
    print(f"   Edges: {data['edge_index'].size(1):,}")
    print(f"   Features per node: {data['num_features']}")
    print(f"   Labeled nodes: {data['labeled_mask'].sum():,}")
    print(f"   Illicit transactions: {(data['y'][data['labeled_mask']] == 1).sum():,}")
    print(f"   Licit transactions: {(data['y'][data['labeled_mask']] == 0).sum():,}")
    
    # Print temporal split statistics
    print(f"\n⏰ Temporal Splits:")
    print(f"   Train: {data['train_mask'].sum():,} nodes")
    print(f"   Validation: {data['val_mask'].sum():,} nodes") 
    print(f"   Test: {data['test_mask'].sum():,} nodes")
    
    print("\n✅ Data loader test complete!") 