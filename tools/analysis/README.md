# Analysis Tools

This directory contains tools for dataset analysis, feature engineering, and data preprocessing for the AML detection pipeline.

## Components

### `enhanced_elliptic_analyzer.py`
- **Purpose**: Comprehensive analysis of the Elliptic Bitcoin dataset
- **Features**:
  - Feature importance analysis using Random Forest
  - Mutual information and chi-squared tests
  - Correlation analysis and ensemble ranking
  - Statistical analysis of transaction patterns
  - Class distribution analysis for imbalanced datasets

### `elliptic_dataset_loader.py`
- **Purpose**: Data loading and preprocessing utilities for Elliptic dataset
- **Features**:
  - Efficient loading of large Bitcoin transaction datasets
  - Data cleaning and preprocessing pipelines
  - Feature normalization and scaling
  - Smart sampling strategies for labeled data
  - Graph construction from transaction flows

## Key Features

### Dataset Analysis
- **Transaction Analysis**: 203,769 Bitcoin transactions with 166 features each
- **Network Analysis**: 234,355 transaction flow edges
- **Label Analysis**: 46,564 labeled transactions (illicit vs licit)
- **Feature Selection**: Variance-based reduction from 166→64 optimized features

### Statistical Tools
- Feature importance ranking using multiple methods
- Class imbalance handling strategies
- Temporal pattern analysis for blockchain data
- Network topology analysis

## Usage

```python
from analysis_tools.enhanced_elliptic_analyzer import Elliptic<PERSON>nalyzer
from analysis_tools.elliptic_dataset_loader import EllipticLoader

# Analyze dataset characteristics
analyzer = EllipticAnalyzer("dataset copy")
feature_importance = analyzer.analyze_feature_importance()
class_distribution = analyzer.analyze_class_distribution()

# Load and preprocess data
loader = EllipticLoader("dataset copy")
processed_data = loader.load_and_preprocess(
    feature_selection=True,
    normalize=True,
    sample_size=10000
)
```

## Integration

These tools integrate with:
- TGAT Layer (provides preprocessed data)
- Database Layer (stores analysis results)
- Model training pipelines (feature engineering)
- Research and optimization workflows

## Analysis Results

Key findings from Elliptic dataset analysis:
- **Dataset Size**: 203K+ transactions, 234K+ edges
- **Feature Optimization**: 64 most informative features selected
- **Class Balance**: ~10% illicit transactions (typical for AML datasets)
- **Temporal Patterns**: Clear temporal clustering in transaction flows 