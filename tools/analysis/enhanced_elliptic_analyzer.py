"""
Enhanced Elliptic Dataset Analyzer and Feature Selector

This module analyzes the Elliptic Bitcoin dataset features and selects the most
important ones for AML detection, handling class imbalance and feature redundancy.
"""

import pandas as pd
import numpy as np
import torch
import torch.nn as nn
from sklearn.preprocessing import StandardScaler
from sklearn.feature_selection import SelectKBest, mutual_info_classif, chi2
from sklearn.ensemble import RandomForestClassifier
from sklearn.decomposition import PCA
from sklearn.metrics import classification_report, roc_auc_score
from sklearn.utils.class_weight import compute_class_weight
import matplotlib.pyplot as plt
import seaborn as sns
from typing import Dict, Tuple, List, Optional
import logging
import warnings
warnings.filterwarnings('ignore')

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class EllipticFeatureAnalyzer:
    """Advanced feature analyzer for Elliptic Bitcoin dataset."""
    
    def __init__(self, dataset_path: str = "dataset copy"):
        self.dataset_path = dataset_path
        self.scaler = StandardScaler()
        self.feature_importance_ = None
        self.selected_features_ = None
        self.feature_names_ = None
        
    def load_data(self, max_samples: int = 20000) -> Tuple[np.ndarray, np.ndarray, List[str]]:
        """Load and preprocess Elliptic data for analysis."""
        logger.info("Loading Elliptic dataset for feature analysis...")
        
        # Load features
        features_df = pd.read_csv(f"{self.dataset_path}/elliptic_txs_features.csv", header=None)
        features_df.columns = ['txId'] + [f'feature_{i}' for i in range(1, 167)]
        
        # Load classes
        classes_df = pd.read_csv(f"{self.dataset_path}/elliptic_txs_classes.csv")
        
        # Merge and get labeled data only
        merged = features_df.merge(classes_df, on='txId', how='inner')
        labeled_data = merged[merged['class'] != 'unknown'].copy()
        
        # Limit samples for analysis efficiency
        if len(labeled_data) > max_samples:
            # Stratified sampling to maintain class balance
            illicit = labeled_data[labeled_data['class'] == 1]
            licit = labeled_data[labeled_data['class'] == 2]
            
            n_illicit = min(len(illicit), max_samples // 4)  # Ensure we have enough illicit
            n_licit = min(len(licit), max_samples - n_illicit)
            
            sampled_data = pd.concat([
                illicit.sample(n=n_illicit, random_state=42),
                licit.sample(n=n_licit, random_state=42)
            ])
            labeled_data = sampled_data
        
        logger.info(f"Analysis dataset: {len(labeled_data)} samples")
        logger.info(f"  Illicit: {(labeled_data['class'] == 1).sum()}")
        logger.info(f"  Licit: {(labeled_data['class'] == 2).sum()}")
        
        # Prepare features and labels
        feature_cols = [col for col in labeled_data.columns if col.startswith('feature_')]
        X = labeled_data[feature_cols].values
        y = labeled_data['class'].values
        
        # Convert labels: 1->1 (illicit), 2->0 (licit)
        y = (y == 1).astype(int)
        
        # Handle missing values
        X = np.nan_to_num(X, nan=0.0)
        
        return X, y, feature_cols
    
    def analyze_feature_importance(self, X: np.ndarray, y: np.ndarray, 
                                 feature_names: List[str]) -> Dict[str, np.ndarray]:
        """Analyze feature importance using multiple methods."""
        logger.info("Analyzing feature importance...")
        
        # Standardize features
        X_scaled = self.scaler.fit_transform(X)
        
        importance_scores = {}
        
        # Method 1: Random Forest Feature Importance
        logger.info("Computing Random Forest importance...")
        rf = RandomForestClassifier(n_estimators=100, random_state=42, class_weight='balanced')
        rf.fit(X_scaled, y)
        importance_scores['random_forest'] = rf.feature_importances_
        
        # Method 2: Mutual Information
        logger.info("Computing Mutual Information scores...")
        mi_scores = mutual_info_classif(X_scaled, y, random_state=42)
        importance_scores['mutual_info'] = mi_scores
        
        # Method 3: Statistical tests (handle class imbalance)
        logger.info("Computing statistical importance...")
        # Use chi2 test for positive features
        X_pos = X_scaled - X_scaled.min() + 1e-6  # Make all features positive
        chi2_scores, _ = chi2(X_pos, y)
        importance_scores['chi2'] = chi2_scores
        
        # Method 4: Correlation with target
        correlations = np.abs([np.corrcoef(X_scaled[:, i], y)[0, 1] for i in range(X_scaled.shape[1])])
        correlations = np.nan_to_num(correlations, nan=0.0)
        importance_scores['correlation'] = correlations
        
        # Method 5: Variance analysis
        variances = np.var(X_scaled, axis=0)
        importance_scores['variance'] = variances
        
        self.feature_importance_ = importance_scores
        self.feature_names_ = feature_names
        
        return importance_scores
    
    def create_ensemble_ranking(self, importance_scores: Dict[str, np.ndarray],
                              weights: Optional[Dict[str, float]] = None) -> np.ndarray:
        """Create ensemble feature ranking from multiple importance methods."""
        if weights is None:
            weights = {
                'random_forest': 0.3,
                'mutual_info': 0.25,
                'chi2': 0.2,
                'correlation': 0.15,
                'variance': 0.1
            }
        
        # Normalize each score to [0, 1]
        normalized_scores = {}
        for method, scores in importance_scores.items():
            if scores.max() > 0:
                normalized_scores[method] = scores / scores.max()
            else:
                normalized_scores[method] = scores
        
        # Compute weighted ensemble
        ensemble_score = np.zeros(len(self.feature_names_))
        for method, weight in weights.items():
            if method in normalized_scores:
                ensemble_score += weight * normalized_scores[method]
        
        return ensemble_score
    
    def select_features(self, X: np.ndarray, y: np.ndarray, 
                       feature_names: List[str], 
                       n_features: int = 64,
                       method: str = 'ensemble') -> Tuple[np.ndarray, List[str]]:
        """Select the most important features."""
        logger.info(f"Selecting top {n_features} features using {method} method...")
        
        if method == 'ensemble':
            # Use ensemble ranking
            importance_scores = self.analyze_feature_importance(X, y, feature_names)
            ensemble_scores = self.create_ensemble_ranking(importance_scores)
            
            # Select top features
            top_indices = np.argsort(ensemble_scores)[-n_features:][::-1]
            
        elif method == 'pca':
            # Use PCA for dimensionality reduction
            X_scaled = self.scaler.fit_transform(X)
            pca = PCA(n_components=n_features, random_state=42)
            X_selected = pca.fit_transform(X_scaled)
            
            # Create synthetic feature names for PCA components
            selected_features = [f'pca_component_{i}' for i in range(n_features)]
            return X_selected, selected_features
            
        elif method == 'mutual_info':
            # Use mutual information selection
            X_scaled = self.scaler.fit_transform(X)
            selector = SelectKBest(score_func=mutual_info_classif, k=n_features)
            X_selected = selector.fit_transform(X_scaled, y)
            top_indices = selector.get_support(indices=True)
            
        else:
            raise ValueError(f"Unknown selection method: {method}")
        
        selected_features = [feature_names[i] for i in top_indices]
        X_selected = X[:, top_indices]
        
        self.selected_features_ = selected_features
        
        logger.info(f"Selected features: {selected_features[:10]}...")  # Show first 10
        return X_selected, selected_features
    
    def analyze_elliptic_structure(self, feature_names: List[str]) -> Dict[str, List[str]]:
        """Analyze the structure of Elliptic features based on domain knowledge."""
        logger.info("Analyzing Elliptic feature structure...")
        
        # Based on Elliptic dataset paper
        feature_groups = {
            'temporal': ['feature_1'],  # Time step
            'local_features': [f'feature_{i}' for i in range(2, 95)],  # Local transaction features
            'aggregated_features': [f'feature_{i}' for i in range(95, 167)]  # Neighbor aggregated features
        }
        
        # Analyze each group
        group_analysis = {}
        for group_name, group_features in feature_groups.items():
            available_features = [f for f in group_features if f in feature_names]
            group_analysis[group_name] = available_features
            logger.info(f"{group_name}: {len(available_features)} features")
        
        return group_analysis
    
    def create_enhanced_features(self, X: np.ndarray, edge_index: np.ndarray,
                               timestamps: np.ndarray) -> np.ndarray:
        """Create enhanced edge features from graph structure."""
        logger.info("Creating enhanced edge features...")
        
        n_edges = edge_index.shape[1]
        edge_features = np.zeros((n_edges, 8))
        
        # Compute node degrees
        n_nodes = X.shape[0]
        in_degree = np.bincount(edge_index[1], minlength=n_nodes)
        out_degree = np.bincount(edge_index[0], minlength=n_nodes)
        
        for i in range(n_edges):
            src, dst = edge_index[0, i], edge_index[1, i]
            
            # Edge feature 0: Source node out-degree (normalized)
            edge_features[i, 0] = out_degree[src] / max(out_degree.max(), 1)
            
            # Edge feature 1: Destination node in-degree (normalized)
            edge_features[i, 1] = in_degree[dst] / max(in_degree.max(), 1)
            
            # Edge feature 2: Time difference (if available)
            if src < len(timestamps) and dst < len(timestamps):
                time_diff = abs(timestamps[src] - timestamps[dst])
                edge_features[i, 2] = min(time_diff / 50.0, 1.0)  # Normalize
            
            # Edge feature 3: Source node feature similarity to destination
            if src < X.shape[0] and dst < X.shape[0]:
                src_feat = X[src, :min(10, X.shape[1])]  # Use first 10 features
                dst_feat = X[dst, :min(10, X.shape[1])]
                similarity = np.corrcoef(src_feat, dst_feat)[0, 1]
                edge_features[i, 3] = np.nan_to_num(similarity, nan=0.0)
            
            # Edge features 4-7: Graph topology features
            edge_features[i, 4] = min(out_degree[src] + in_degree[dst], 100) / 100.0
            edge_features[i, 5] = 1.0 if out_degree[src] > out_degree.mean() else 0.0
            edge_features[i, 6] = 1.0 if in_degree[dst] > in_degree.mean() else 0.0
            edge_features[i, 7] = np.random.random()  # Random feature for baseline
        
        return edge_features
    
    def generate_analysis_report(self, save_path: str = "feature_analysis_report.txt"):
        """Generate a comprehensive analysis report."""
        if self.feature_importance_ is None:
            logger.warning("No feature analysis performed yet.")
            return
        
        logger.info(f"Generating analysis report: {save_path}")
        
        with open(save_path, 'w') as f:
            f.write("🔍 ELLIPTIC DATASET FEATURE ANALYSIS REPORT\n")
            f.write("=" * 50 + "\n\n")
            
            # Feature groups analysis
            feature_groups = self.analyze_elliptic_structure(self.feature_names_)
            f.write("📊 FEATURE GROUPS:\n")
            for group, features in feature_groups.items():
                f.write(f"  {group}: {len(features)} features\n")
            f.write("\n")
            
            # Top features by method
            f.write("🏆 TOP 20 FEATURES BY METHOD:\n")
            for method, scores in self.feature_importance_.items():
                top_indices = np.argsort(scores)[-20:][::-1]
                f.write(f"\n{method.upper()}:\n")
                for i, idx in enumerate(top_indices):
                    f.write(f"  {i+1:2d}. {self.feature_names_[idx]}: {scores[idx]:.4f}\n")
            
            # Ensemble ranking
            ensemble_scores = self.create_ensemble_ranking(self.feature_importance_)
            top_ensemble = np.argsort(ensemble_scores)[-20:][::-1]
            f.write(f"\nENSEMBLE RANKING (TOP 20):\n")
            for i, idx in enumerate(top_ensemble):
                f.write(f"  {i+1:2d}. {self.feature_names_[idx]}: {ensemble_scores[idx]:.4f}\n")
        
        logger.info("✅ Analysis report generated!")

def analyze_elliptic_features(dataset_path: str = "dataset copy", 
                            max_samples: int = 20000,
                            n_features: int = 64) -> Dict:
    """
    Main function to analyze Elliptic features and return optimized dataset.
    
    Args:
        dataset_path: Path to Elliptic dataset
        max_samples: Maximum samples for analysis
        n_features: Number of features to select
        
    Returns:
        Dictionary with analysis results and selected features
    """
    analyzer = EllipticFeatureAnalyzer(dataset_path)
    
    # Load data
    X, y, feature_names = analyzer.load_data(max_samples)
    
    # Analyze features
    importance_scores = analyzer.analyze_feature_importance(X, y, feature_names)
    
    # Select best features
    X_selected, selected_features = analyzer.select_features(X, y, feature_names, n_features)
    
    # Generate report
    analyzer.generate_analysis_report()
    
    # Compute class weights for imbalanced dataset
    class_weights = compute_class_weight('balanced', classes=np.unique(y), y=y)
    class_weight_dict = {0: class_weights[0], 1: class_weights[1]}
    
    results = {
        'X_original': X,
        'X_selected': X_selected,
        'y': y,
        'feature_names': feature_names,
        'selected_features': selected_features,
        'importance_scores': importance_scores,
        'class_weights': class_weight_dict,
        'scaler': analyzer.scaler,
        'analyzer': analyzer
    }
    
    logger.info("🎯 Feature analysis complete!")
    logger.info(f"   Original features: {X.shape[1]}")
    logger.info(f"   Selected features: {len(selected_features)}")
    logger.info(f"   Class weights: {class_weight_dict}")
    
    return results

if __name__ == "__main__":
    # Run feature analysis
    print("🔍 Starting Elliptic Feature Analysis...")
    
    results = analyze_elliptic_features(
        dataset_path="dataset copy",
        max_samples=15000,  # Use more samples for better analysis
        n_features=64       # Select top 64 features
    )
    
    print(f"\n✅ Analysis Results:")
    print(f"   Original features: {results['X_original'].shape[1]}")
    print(f"   Selected features: {len(results['selected_features'])}")
    print(f"   Samples analyzed: {len(results['y'])}")
    print(f"   Illicit rate: {results['y'].mean():.3f}")
    print(f"   Class weights: {results['class_weights']}")
    
    print(f"\n🏆 Top 10 Selected Features:")
    for i, feature in enumerate(results['selected_features'][:10]):
        print(f"   {i+1:2d}. {feature}") 