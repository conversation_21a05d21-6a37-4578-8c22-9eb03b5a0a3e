"""
Configuration management for AML blockchain data ingestion
Handles environment variables, API keys, and system settings
"""

import os
from typing import Optional, List
from pydantic_settings import BaseSettings
from pydantic import Field, validator
from enum import Enum


class LogLevel(str, Enum):
    """Supported log levels"""
    DEBUG = "DEBUG"
    INFO = "INFO"
    WARNING = "WARNING"
    ERROR = "ERROR"
    CRITICAL = "CRITICAL"


class EthereumNetwork(str, Enum):
    """Supported Ethereum networks"""
    MAINNET = "mainnet"
    SEPOLIA = "sepolia"
    GOERLI = "goerli"


class IngestionSettings(BaseSettings):
    """Main ingestion service configuration using latest Pydantic patterns"""
    
    # Service settings
    service_name: str = Field(default="aml-ingestion")
    log_level: LogLevel = Field(default=LogLevel.INFO)
    
    # Infura/Ethereum settings - updated based on Context7 Web3.py patterns
    infura_project_id: str = Field(...)
    infura_project_secret: Optional[str] = Field(default=None)
    ethereum_network: EthereumNetwork = Field(default=EthereumNetwork.SEPOLIA)
    
    # Infura API 请求速率限制参数
    infura_request_interval: float = Field(default=0.5)  # 请求间隔（秒）
    infura_batch_size: int = Field(default=5)  # 每批处理的交易数量
    infura_max_retries: int = Field(default=3)  # 最大重试次数
    
    # Kafka settings - updated based on Context7 kafka-python patterns  
    kafka_bootstrap_servers: str = Field(default="localhost:9092")
    kafka_transactions_topic: str = Field(default="ethereum-transactions")
    kafka_acks: str = Field(default="all")
    kafka_retries: int = Field(default=3) 
    kafka_batch_size: int = Field(default=16384)
    kafka_linger_ms: int = Field(default=100)
    kafka_buffer_memory: int = Field(default=33554432)
    
    # Data processing
    batch_size: int = Field(default=100)
    processing_timeout: int = Field(default=30)
    
    # Transaction filtering - Fixed optional float handling
    filter_contract_creation: bool = Field(default=False)
    filter_zero_value: bool = Field(default=False)
    min_transaction_value: Optional[float] = Field(default=None)
    
    # Error handling
    max_consecutive_errors: int = Field(default=10)
    error_backoff_factor: float = Field(default=2.0)
    
    @validator('min_transaction_value', pre=True)
    def validate_min_transaction_value(cls, v):
        """Handle empty string for optional float field"""
        if v == "" or v is None:
            return None
        try:
            return float(v)
        except (ValueError, TypeError):
            return None
    
    @property 
    def kafka_servers_list(self) -> List[str]:
        """Convert comma-separated kafka servers to list"""
        return [server.strip() for server in self.kafka_bootstrap_servers.split(',')]
    
    @property
    def infura_wss_url(self) -> str:
        """Build WebSocket URL for Infura based on Context7 patterns"""
        return f"wss://{self.ethereum_network.value}.infura.io/ws/v3/{self.infura_project_id}"
    
    @property
    def infura_https_url(self) -> str:
        """Build HTTPS URL for Infura"""
        return f"https://{self.ethereum_network.value}.infura.io/v3/{self.infura_project_id}"

    class Config:
        env_file = ".env"
        case_sensitive = False


def get_settings() -> IngestionSettings:
    """Get the settings instance"""
    return IngestionSettings() 