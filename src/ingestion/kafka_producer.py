"""
Enhanced Kafka producer for AML blockchain data ingestion
Updated with latest kafka-python patterns from Context7
"""

import json
import structlog
from typing import Dict, Any, Optional, Callable
from kafka import KafkaProducer
from kafka.errors import KafkaError, NoBrokersAvailable
import time
import asyncio

from config.settings import IngestionSettings
from infura_client import Transaction

logger = structlog.get_logger(__name__)


class AMLKafkaProducer:
    """
    Enhanced Kafka producer using latest kafka-python patterns from Context7
    Implements compression, batching, callbacks, and error handling
    """
    
    def __init__(self, settings: IngestionSettings):
        self.settings = settings
        self.producer: Optional[KafkaProducer] = None
        self._message_count = 0
        self._error_count = 0
        
    async def connect(self) -> bool:
        """Initialize Kafka producer with Context7 best practices"""
        max_retries = 10
        retry_count = 0
        retry_delay = 3  # seconds
        
        while retry_count < max_retries:
            try:
                logger.info("Initializing Kafka producer", 
                           brokers=self.settings.kafka_servers_list)
                
                # Producer configuration using latest Context7 patterns
                producer_config = {
                    'bootstrap_servers': self.settings.kafka_servers_list,
                    
                    # Delivery guarantees - Context7 recommended patterns
                    'acks': self.settings.kafka_acks,
                    'retries': self.settings.kafka_retries,
                    'enable_idempotence': True,  # Exactly-once semantics
                    
                    # Performance optimizations from Context7
                    'batch_size': self.settings.kafka_batch_size,
                    'linger_ms': self.settings.kafka_linger_ms,
                    'buffer_memory': self.settings.kafka_buffer_memory,
                    'compression_type': 'gzip',  # Alternative compression (snappy not available)
                    
                    # Serialization - using JSON for flexibility
                    'key_serializer': self._serialize_key,
                    'value_serializer': self._serialize_value,
                }
                
                # Create producer with latest patterns
                self.producer = KafkaProducer(**producer_config)
                
                logger.info("Successfully connected to Kafka", 
                           brokers=self.settings.kafka_servers_list,
                           compression='gzip',
                           idempotence=True)
                return True
                
            except NoBrokersAvailable as e:
                retry_count += 1
                if retry_count < max_retries:
                    logger.warning(f"No Kafka brokers available, retrying in {retry_delay} seconds (attempt {retry_count}/{max_retries})")
                    await asyncio.sleep(retry_delay)
                    retry_delay = min(retry_delay * 2, 30)  # Exponential backoff with max of 30 seconds
                else:
                    logger.error(f"Failed to connect to Kafka after {max_retries} attempts", error=str(e))
                    return False
            except Exception as e:
                logger.error("Failed to create Kafka producer", error=str(e))
                return False
    
    def _serialize_key(self, key: Any) -> bytes:
        """Serialize message key using Context7 patterns"""
        if key is None:
            return None
        return str(key).encode('utf-8')
    
    def _serialize_value(self, value: Any) -> bytes:
        """Serialize message value to JSON using Context7 patterns"""
        try:
            return json.dumps(value, default=str).encode('utf-8')
        except Exception as e:
            logger.error("Error serializing value", error=str(e))
            raise
    
    def _on_send_success(self, record_metadata):
        """Callback for successful message delivery - Context7 pattern"""
        logger.debug("Message sent successfully",
                    topic=record_metadata.topic,
                    partition=record_metadata.partition,
                    offset=record_metadata.offset)
        self._message_count += 1
    
    def _on_send_error(self, exception):
        """Callback for failed message delivery - Context7 pattern"""
        logger.error("Message send failed", error=str(exception))
        self._error_count += 1
    
    async def send_transaction(self, transaction: Transaction) -> bool:
        """
        Send transaction to Kafka using Context7 async callback patterns
        """
        if not self.producer:
            logger.error("Producer not initialized")
            return False
        
        try:
            # Create partition key for consistent routing
            partition_key = f"{transaction.from_address}:{transaction.nonce}"
            
            # Convert transaction to dict for serialization
            transaction_dict = transaction.to_dict()
            
            # Add metadata
            transaction_dict.update({
                'ingestion_timestamp': time.time(),
                'source': self.settings.service_name
            })
            
            # Send with callbacks using Context7 patterns
            future = self.producer.send(
                topic=self.settings.kafka_transactions_topic,
                key=partition_key,
                value=transaction_dict
            )
            
            # Add callbacks
            future.add_callback(self._on_send_success)
            future.add_errback(self._on_send_error)
            
            return True
            
        except Exception as e:
            logger.error("Error sending transaction to Kafka", 
                        tx_hash=transaction.hash[:10], 
                        error=str(e))
            self._error_count += 1
            return False
    
    async def send_block(self, block_data: Dict[str, Any]) -> bool:
        """
        Send block data to Kafka using Context7 async callback patterns
        """
        if not self.producer:
            logger.error("Producer not initialized")
            return False
        
        try:
            # Use block hash as key for consistent routing
            block_hash = block_data.get('hash', 'unknown')
            if isinstance(block_hash, bytes):
                block_hash = block_hash.hex()
            
            # Add metadata
            block_data.update({
                'ingestion_timestamp': time.time(),
                'source': self.settings.service_name
            })
            
            # Send with callbacks using Context7 patterns
            future = self.producer.send(
                topic=self.settings.kafka_transactions_topic,
                key=f"block:{block_hash}",
                value=block_data
            )
            
            # Add callbacks
            future.add_callback(self._on_send_success)
            future.add_errback(self._on_send_error)
            
            return True
            
        except Exception as e:
            logger.error("Error sending block to Kafka", 
                        block_number=block_data.get('number', 'unknown'), 
                        error=str(e))
            self._error_count += 1
            return False
    
    def flush(self) -> None:
        """Flush all pending messages"""
        if self.producer:
            try:
                logger.debug("Flushing pending Kafka messages")
                self.producer.flush()
            except Exception as e:
                logger.error("Error flushing Kafka producer", error=str(e))
    
    def close(self) -> None:
        """Close producer and release resources"""
        if self.producer:
            try:
                self.flush()
                self.producer.close()
                logger.info("Kafka producer closed successfully",
                          errors=self._error_count,
                          messages_sent=self._message_count)
            except Exception as e:
                logger.error("Error closing Kafka producer", error=str(e))
            self.producer = None
    
    @property
    def is_connected(self) -> bool:
        """Check if producer is connected"""
        return self.producer is not None
    
    @property
    def message_count(self) -> int:
        """Get message count"""
        return self._message_count
    
    @property 
    def error_count(self) -> int:
        """Get error count"""
        return self._error_count
    
    def get_metrics(self) -> Dict[str, Any]:
        """Get producer metrics"""
        metrics = {
            'messages_sent': self._message_count,
            'errors': self._error_count,
            'is_connected': self.is_connected,
        }
        
        # Add JMX metrics if available
        if self.producer:
            try:
                metrics['kafka_metrics'] = self.producer.metrics()
            except:
                pass
                
        return metrics
