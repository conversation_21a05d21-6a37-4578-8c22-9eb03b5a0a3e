"""
Main ingestion service for AML blockchain data streaming
Updated with latest patterns and simplified architecture
"""

import asyncio
import signal
import sys
from typing import Optional, Dict, Any, Callable, Awaitable
from fastapi import FastAP<PERSON>, HTTPException
import uvicorn

try:
    import structlog
except ImportError:
    import logging
    structlog = logging.getLogger(__name__)

from config.settings import IngestionSettings
from infura_client import InfuraStreamingClient, Transaction
from kafka_producer import AMLKafkaProducer

# Configure basic logging
logger = structlog.get_logger(__name__)

# Create FastAPI app
app = FastAPI(title="AML Ingestion Service")

# Global service instance
service: Optional['AMLIngestionService'] = None

class AMLIngestionService:
    """
    Main service orchestrating Ethereum data ingestion
    Simplified architecture with direct WebSocket streaming
    """
    
    def __init__(self):
        self.settings = IngestionSettings()
        self.infura_client: Optional[InfuraStreamingClient] = None
        self.kafka_producer: Optional[AMLKafkaProducer] = None
        self.running = False
        
    async def initialize(self) -> bool:
        """Initialize all service components"""
        try:
            logger.info("Initializing AML Ingestion Service",
                       service_name=self.settings.service_name,
                       network=self.settings.ethereum_network)
            
            # Initialize Kafka producer
            self.kafka_producer = AMLKafkaProducer(self.settings)
            if not await self.kafka_producer.connect():
                logger.error("Failed to initialize Kafka producer")
                return False
            
            # Initialize Infura client with project ID from settings
            self.infura_client = InfuraStreamingClient(self.settings)
            if not await self.infura_client.connect():
                logger.error("Failed to connect to Infura WebSocket")
                return False
            
            # Set up callbacks - convert async methods to sync wrappers
            self.infura_client.set_transaction_callback(
                lambda tx: asyncio.create_task(self._handle_transaction(tx))
            )
            self.infura_client.set_block_callback(
                lambda block: asyncio.create_task(self._handle_block(block))
            )
            
            logger.info("Service initialization completed successfully")
            return True
            
        except Exception as e:
            logger.error("Failed to initialize service", error=str(e))
            return False
    
    async def _handle_transaction(self, transaction: Transaction) -> None:
        """Handle incoming transaction from Infura"""
        try:
            if self.kafka_producer:
                success = await self.kafka_producer.send_transaction(transaction)
                if success:
                    logger.debug("Transaction processed successfully", 
                               tx_hash=transaction.hash)
                else:
                    logger.warning("Failed to send transaction to Kafka",
                                 tx_hash=transaction.hash)
        except Exception as e:
            logger.error("Error handling transaction", 
                        tx_hash=transaction.hash, 
                        error=str(e))
    
    async def _handle_block(self, block_data: Dict[str, Any]) -> None:
        """Handle incoming block from Infura"""
        try:
            if self.kafka_producer:
                success = await self.kafka_producer.send_block(block_data)
                if success:
                    logger.debug("Block processed successfully", 
                               block_number=block_data.get('number'))
        except Exception as e:
            logger.error("Error handling block", error=str(e))
    
    async def start(self):
        """Start the ingestion service"""
        if not await self.initialize():
            logger.error("Service initialization failed")
            sys.exit(1)
        
        try:
            self.running = True
            logger.info("Starting real-time data ingestion")
            
            # Start streaming - this will run indefinitely
            if self.infura_client:
                await self.infura_client.start_streaming()
            
        except Exception as e:
            logger.error("Error in main service loop", error=str(e))
        finally:
            await self.shutdown()
    
    async def shutdown(self):
        """Graceful shutdown of all components"""
        logger.info("Shutting down ingestion service")
        self.running = False
        
        # Stop streaming
        if self.infura_client:
            await self.infura_client.stop_streaming()
            await self.infura_client.disconnect()
        
        # Close producer
        if self.kafka_producer:
            self.kafka_producer.flush()
            self.kafka_producer.close()
        
        logger.info("Service shutdown completed")
    
    async def health_check(self) -> dict:
        """Service health check"""
        health_status = {
            'service': 'aml-ingestion',
            'status': 'running' if self.running else 'stopped',
            'infura_connected': self.infura_client.is_connected if self.infura_client else False,
            'kafka_connected': self.kafka_producer.is_connected if self.kafka_producer else False,
            'kafka_metrics': self.kafka_producer.get_metrics() if self.kafka_producer else {}
        }
        
        # Consider service healthy if running and both connections are active
        is_healthy = (self.running and 
                     health_status['infura_connected'] and 
                     health_status['kafka_connected'])
        
        health_status['healthy'] = is_healthy
        return health_status

# FastAPI endpoints
@app.get("/health")
async def health_check():
    """Health check endpoint for Docker"""
    if not service:
        raise HTTPException(status_code=503, detail="Service not initialized")
    
    health_status = await service.health_check()
    if not health_status['healthy']:
        raise HTTPException(status_code=503, detail=health_status)
    return health_status

async def main():
    """Main entry point"""
    global service
    service = AMLIngestionService()
    
    # Set up signal handlers for graceful shutdown
    def signal_handler(signum, frame):
        logger.info("Received shutdown signal", signal=signum)
        asyncio.create_task(service.shutdown())
    
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    
    try:
        # Start FastAPI server in the background
        config = uvicorn.Config(app, host="0.0.0.0", port=8000, log_level="info")
        server = uvicorn.Server(config)
        api_task = asyncio.create_task(server.serve())
        
        # Start ingestion service
        await service.start()
    except KeyboardInterrupt:
        logger.info("Received keyboard interrupt")
        await service.shutdown()
    except Exception as e:
        logger.error("Unexpected error in main", error=str(e))
        await service.shutdown()
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main()) 