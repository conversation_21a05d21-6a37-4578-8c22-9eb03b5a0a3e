# Core async and networking
websockets>=11.0.2
aiohttp>=3.8.5
fastapi>=0.104.0
uvicorn>=0.24.0

# Kafka integration  
kafka-python>=2.0.2

# Ethereum and blockchain
web3>=6.9.0
eth-account>=0.9.0
eth-utils>=2.2.0

# Data processing and validation
pydantic>=2.3.0
pydantic-settings>=2.0.3
orjson>=3.9.5  # Fast JSON serialization

# Configuration management
python-decouple>=3.8
click>=8.1.7

# Logging and monitoring
structlog>=23.1.0

# Error handling and resilience
tenacity>=8.2.3  # Retry logic

# Development and testing
pytest>=7.4.0
pytest-asyncio>=0.21.1

# Environment and utilities
python-dotenv>=1.0.0
requests>=2.31.0 