#!/bin/bash

echo "=== Building and Testing CEP Solutions ==="

# Set error handling
set -e

# Build the CEP layer
echo "📦 Building CEP layer..."
mvn clean package -DskipTests

if [ $? -ne 0 ]; then
    echo "❌ Build failed! Please fix compilation errors first."
    exit 1
fi

echo "✅ Build successful!"

# Function to deploy and test a specific CEP version
test_cep_solution() {
    local CLASS_NAME=$1
    local SOLUTION_NAME=$2
    local JAR_FILE="target/cep-layer-1.0-SNAPSHOT.jar"
    
    echo ""
    echo "🚀 Testing $SOLUTION_NAME..."
    echo "Class: $CLASS_NAME"
    
    # Stop any existing CEP jobs
    echo "Stopping existing CEP jobs..."
    curl -X PATCH http://localhost:8081/jobs 2>/dev/null || true
    sleep 2
    
    # Submit the new job
    echo "Submitting $SOLUTION_NAME job..."
    curl -X POST \
        -H "Content-Type: application/json" \
        -d "{
            \"entryClass\": \"$CLASS_NAME\",
            \"programArgs\": [],
            \"allowNonRestoredState\": true
        }" \
        http://localhost:8081/jars/$(basename $JAR_FILE)/run
    
    if [ $? -eq 0 ]; then
        echo "✅ $SOLUTION_NAME submitted successfully!"
        echo "📊 Monitor at: http://localhost:8081"
        echo "📊 Check logs: docker logs flink-taskmanager"
        echo ""
        echo "⏱️  Wait 30 seconds and check for alerts..."
        echo "🔍 Expected output patterns:"
        echo "   - Processing time CEP: 'PROCESSING TIME CEP MATCH FOUND'"
        echo "   - Watermark CEP: 'WATERMARK CEP MATCH FOUND'"
        echo "   - Simple Filter: 'SIMPLE FILTER MATCH'"
        echo ""
        echo "📋 To check filtered-transactions topic:"
        echo "   docker exec kafka kafka-console-consumer.sh --bootstrap-server localhost:9092 --topic filtered-transactions --from-beginning"
        echo ""
    else
        echo "❌ Failed to submit $SOLUTION_NAME"
    fi
}

# Upload JAR to Flink
echo "📤 Uploading JAR to Flink cluster..."
JAR_FILE="target/cep-layer-1.0-SNAPSHOT.jar"

if [ ! -f "$JAR_FILE" ]; then
    echo "❌ JAR file not found: $JAR_FILE"
    exit 1
fi

# Upload JAR
curl -X POST -H "Expect:" -F "jarfile=@$JAR_FILE" http://localhost:8081/jars/upload

echo ""
echo "🎯 Available Solutions:"
echo "1. Processing Time CEP (Recommended) - Bypasses watermark issues"
echo "2. Watermark Fixed CEP - Proper watermark strategy"
echo "3. Simple Filter - Direct filtering without CEP"
echo ""

read -p "Which solution would you like to test? (1/2/3/all): " choice

case $choice in
    1)
        test_cep_solution "com.blockchain.aml.cep.ProcessingTimeAMLCEP" "Processing Time CEP"
        ;;
    2)
        test_cep_solution "com.blockchain.aml.cep.WatermarkFixedAMLCEP" "Watermark Fixed CEP"
        ;;
    3)
        test_cep_solution "com.blockchain.aml.cep.SimpleFilterAML" "Simple Filter AML"
        ;;
    all)
        echo "🧪 Testing all solutions sequentially..."
        test_cep_solution "com.blockchain.aml.cep.ProcessingTimeAMLCEP" "Processing Time CEP"
        sleep 30
        test_cep_solution "com.blockchain.aml.cep.WatermarkFixedAMLCEP" "Watermark Fixed CEP"
        sleep 30
        test_cep_solution "com.blockchain.aml.cep.SimpleFilterAML" "Simple Filter AML"
        ;;
    *)
        echo "❌ Invalid choice. Please select 1, 2, 3, or 'all'"
        exit 1
        ;;
esac

echo ""
echo "🎉 Solution deployment complete!"
echo ""
echo "📊 Monitoring Commands:"
echo "  Flink Web UI: http://localhost:8081"
echo "  Check alerts: docker exec kafka kafka-console-consumer.sh --bootstrap-server localhost:9092 --topic filtered-transactions --from-beginning"
echo "  Check logs: docker logs flink-taskmanager"
echo "  Check ingestion: docker logs sepolia-ingestion"
echo ""
echo "🔧 Troubleshooting:"
echo "  1. Verify ingestion is running: docker ps | grep sepolia"
echo "  2. Check transaction flow: docker logs sepolia-ingestion | tail -20"
echo "  3. Monitor Kafka topics: docker exec kafka kafka-topics.sh --bootstrap-server localhost:9092 --list"
echo "  4. Check pipeline status: docker-compose -f docker-compose-integrated-pipeline.yml ps" 