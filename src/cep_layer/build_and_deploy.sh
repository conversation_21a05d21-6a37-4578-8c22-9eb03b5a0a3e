#!/bin/bash

# Flink CEP AML Application - Build and Deploy Script
# Usage: ./build_and_deploy.sh [--rebuild] [--deploy-only]

set -e  # Exit on any error

# Color codes for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "$SCRIPT_DIR/../.." && pwd)"
FLINK_UI_URL="http://localhost:8081"
KAFKA_UI_URL="http://localhost:8080"
JAR_NAME="flink-cep-aml-1.0.0.jar"
MAIN_CLASS="com.blockchain.aml.cep.FlinkAMLCEP"
DOCKER_COMPOSE_FILE="$PROJECT_ROOT/configs/docker/docker-compose-cep.yml"

echo -e "${BLUE}🚀 Flink CEP AML Application - Build and Deploy${NC}"
echo "================================================="

# Function to print status messages
print_status() {
    echo -e "${GREEN}✓${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}⚠${NC} $1"
}

print_error() {
    echo -e "${RED}✗${NC} $1"
}

# Function to check if service is running
check_service() {
    local service_name=$1
    local url=$2
    
    if curl -s "$url" > /dev/null 2>&1; then
        print_status "$service_name is running at $url"
        return 0
    else
        print_warning "$service_name is not accessible at $url"
        return 1
    fi
}

# Function to wait for service to be ready
wait_for_service() {
    local service_name=$1
    local url=$2
    local max_attempts=30
    local attempt=1
    
    echo "Waiting for $service_name to be ready..."
    
    while [ $attempt -le $max_attempts ]; do
        if curl -s "$url" > /dev/null 2>&1; then
            print_status "$service_name is ready!"
            return 0
        fi
        
        echo -n "."
        sleep 2
        ((attempt++))
    done
    
    print_error "$service_name failed to start within $(($max_attempts * 2)) seconds"
    return 1
}

# Function to check if Docker Compose is running
check_docker_compose() {
    if ! docker-compose -f "$DOCKER_COMPOSE_FILE" ps | grep -q "Up"; then
        print_warning "Flink cluster not running. Starting Docker Compose..."
        
        # Create volume directory for jobs if it doesn't exist
        mkdir -p "$PROJECT_ROOT/cep_layer/target"
        
        # Copy JAR to the volume directory
        if [ -f "$JAR_PATH" ]; then
            cp "$JAR_PATH" "$PROJECT_ROOT/cep_layer/target/"
            print_status "Copied JAR to volume directory"
        fi
        
        # Start the infrastructure
        docker-compose -f "$DOCKER_COMPOSE_FILE" up -d
        
        # Wait for services to be ready
        wait_for_service "Flink JobManager" "$FLINK_UI_URL/config"
        wait_for_service "Kafka UI" "$KAFKA_UI_URL"
    else
        print_status "Docker Compose services are running"
    fi
}

# Parse command line arguments
REBUILD=false
DEPLOY_ONLY=false

while [[ $# -gt 0 ]]; do
    case $1 in
        --rebuild)
            REBUILD=true
            shift
            ;;
        --deploy-only)
            DEPLOY_ONLY=true
            shift
            ;;
        *)
            echo "Unknown option: $1"
            echo "Usage: $0 [--rebuild] [--deploy-only]"
            exit 1
            ;;
    esac
done

# Step 1: Build the application (unless deploy-only)
if [ "$DEPLOY_ONLY" = false ]; then
    echo -e "\n${BLUE}📦 Building CEP Application${NC}"
    echo "-----------------------------"
    
    cd "$SCRIPT_DIR"
    
    if [ "$REBUILD" = true ]; then
        print_status "Cleaning previous build..."
        mvn clean
    fi
    
    print_status "Building JAR with dependencies..."
    if mvn package -DskipTests; then
        print_status "Build completed successfully"
        ls -la target/
    else
        print_error "Build failed"
        exit 1
    fi
else
    echo -e "\n${YELLOW}Skipping build step (deploy-only mode)${NC}"
fi

# Step 2: Check if Flink cluster is running
echo -e "\n${BLUE}🔍 Checking Flink Cluster Status${NC}"
echo "-----------------------------------"

cd "$PROJECT_ROOT"

# Verify docker-compose file exists
if [ ! -f "$DOCKER_COMPOSE_FILE" ]; then
    print_error "Docker Compose file not found: $DOCKER_COMPOSE_FILE"
    exit 1
else
    print_status "Using Docker Compose file: $DOCKER_COMPOSE_FILE"
fi

# Check and start Docker Compose if needed
check_docker_compose

# Verify services are accessible
check_service "Flink Web UI" "$FLINK_UI_URL"
check_service "Kafka UI" "$KAFKA_UI_URL"

# Step 3: Deploy the CEP application
echo -e "\n${BLUE}🚀 Deploying CEP Application${NC}"
echo "-------------------------------"

# Check if JAR exists
JAR_PATH="$SCRIPT_DIR/target/$JAR_NAME"
if [ ! -f "$JAR_PATH" ]; then
    print_error "JAR file not found: $JAR_PATH"
    print_error "Please run the build step first"
    exit 1
fi

print_status "Found JAR file: $JAR_NAME"

# Copy JAR to Flink JobManager container
print_status "Copying JAR to Flink JobManager..."
docker cp "$JAR_PATH" flink-jobmanager:/opt/flink/jobs/

# Submit the job to Flink
print_status "Submitting job to Flink cluster..."
if docker exec flink-jobmanager /opt/flink/bin/flink run \
    --class "$MAIN_CLASS" \
    --detached \
    /opt/flink/jobs/"$JAR_NAME"; then
    print_status "CEP application submitted successfully!"
else
    print_error "Failed to submit CEP application"
    exit 1
fi

# Step 4: Verify deployment
echo -e "\n${BLUE}✅ Verifying Deployment${NC}"
echo "-------------------------"

sleep 5  # Allow job to start

# Check Flink jobs
echo "Current Flink jobs:"
docker exec flink-jobmanager /opt/flink/bin/flink list

# Step 5: Show access information
echo -e "\n${BLUE}🌐 Access Information${NC}"
echo "====================="
echo -e "Flink Web UI:    ${GREEN}$FLINK_UI_URL${NC}"
echo -e "Kafka UI:        ${GREEN}$KAFKA_UI_URL${NC}"
echo -e "JobManager Logs: ${YELLOW}docker logs flink-jobmanager${NC}"
echo -e "TaskManager Logs:${YELLOW}docker logs flink-taskmanager${NC}"

# Step 6: Create topics if needed
echo -e "\n${BLUE}📝 Creating Kafka Topics${NC}"
echo "--------------------------"

# Create input and output topics
docker exec kafka-cep kafka-topics --create --if-not-exists \
    --bootstrap-server localhost:9092 \
    --topic ethereum-transactions \
    --partitions 4 \
    --replication-factor 1

docker exec kafka-cep kafka-topics --create --if-not-exists \
    --bootstrap-server localhost:9092 \
    --topic filtered-transactions \
    --partitions 4 \
    --replication-factor 1

docker exec kafka-cep kafka-topics --create --if-not-exists \
    --bootstrap-server localhost:9092 \
    --topic aml-alerts \
    --partitions 4 \
    --replication-factor 1

print_status "Kafka topics created/verified"

# List topics
echo "Available Kafka topics:"
docker exec kafka-cep kafka-topics --list --bootstrap-server localhost:9092

echo -e "\n${GREEN}🎉 CEP Application Deployment Complete!${NC}"
echo "====================================="
echo "The CEP application is now running and processing transactions."
echo "Use the Flink Web UI to monitor the job and Kafka UI to view topics." 