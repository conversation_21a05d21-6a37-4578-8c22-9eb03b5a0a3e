<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
  <modelVersion>4.0.0</modelVersion>
  <groupId>com.blockchain.aml</groupId>
  <artifactId>flink-cep-aml</artifactId>
  <name>Blockchain AML CEP Layer</name>
  <version>1.0.0</version>
  <description>Apache Flink CEP for real-time AML pattern detection on Ethereum transactions</description>
  <build>
    <plugins>
      <plugin>
        <artifactId>maven-compiler-plugin</artifactId>
        <version>3.11.0</version>
        <configuration>
          <source>11</source>
          <target>11</target>
        </configuration>
      </plugin>
      <plugin>
        <artifactId>maven-shade-plugin</artifactId>
        <version>3.4.1</version>
        <executions>
          <execution>
            <phase>package</phase>
            <goals>
              <goal>shade</goal>
            </goals>
            <configuration>
              <filters>
                <filter>
                  <artifact>*:*</artifact>
                  <excludes>
                    <exclude>META-INF/*.SF</exclude>
                    <exclude>META-INF/*.DSA</exclude>
                    <exclude>META-INF/*.RSA</exclude>
                  </excludes>
                </filter>
              </filters>
              <transformers>
                <transformer>
                  <mainClass>com.blockchain.aml.cep.LiveAMLCEP</mainClass>
                </transformer>
                <transformer />
              </transformers>
            </configuration>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <artifactId>maven-surefire-plugin</artifactId>
        <version>3.0.0</version>
      </plugin>
    </plugins>
  </build>
  <repositories>
    <repository>
      <releases />
      <snapshots>
        <enabled>false</enabled>
      </snapshots>
      <id>aliyun</id>
      <url>https://maven.aliyun.com/repository/public</url>
    </repository>
  </repositories>
  <pluginRepositories>
    <pluginRepository>
      <releases />
      <snapshots>
        <enabled>false</enabled>
      </snapshots>
      <id>aliyun</id>
      <url>https://maven.aliyun.com/repository/public</url>
    </pluginRepository>
  </pluginRepositories>
  <dependencies>
    <dependency>
      <groupId>org.junit.jupiter</groupId>
      <artifactId>junit-jupiter</artifactId>
      <version>5.9.2</version>
      <scope>test</scope>
      <exclusions>
        <exclusion>
          <artifactId>junit-jupiter-api</artifactId>
          <groupId>org.junit.jupiter</groupId>
        </exclusion>
        <exclusion>
          <artifactId>junit-jupiter-params</artifactId>
          <groupId>org.junit.jupiter</groupId>
        </exclusion>
        <exclusion>
          <artifactId>junit-jupiter-engine</artifactId>
          <groupId>org.junit.jupiter</groupId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>org.apache.flink</groupId>
      <artifactId>flink-test-utils</artifactId>
      <version>1.18.0</version>
      <scope>test</scope>
      <exclusions>
        <exclusion>
          <artifactId>flink-test-utils-junit</artifactId>
          <groupId>org.apache.flink</groupId>
        </exclusion>
        <exclusion>
          <artifactId>flink-runtime</artifactId>
          <groupId>org.apache.flink</groupId>
        </exclusion>
        <exclusion>
          <artifactId>flink-core</artifactId>
          <groupId>org.apache.flink</groupId>
        </exclusion>
        <exclusion>
          <artifactId>flink-rpc-akka-loader</artifactId>
          <groupId>org.apache.flink</groupId>
        </exclusion>
        <exclusion>
          <artifactId>flink-clients</artifactId>
          <groupId>org.apache.flink</groupId>
        </exclusion>
        <exclusion>
          <artifactId>assertj-core</artifactId>
          <groupId>org.assertj</groupId>
        </exclusion>
        <exclusion>
          <artifactId>flink-streaming-java</artifactId>
          <groupId>org.apache.flink</groupId>
        </exclusion>
        <exclusion>
          <artifactId>flink-statebackend-rocksdb</artifactId>
          <groupId>org.apache.flink</groupId>
        </exclusion>
        <exclusion>
          <artifactId>flink-statebackend-changelog</artifactId>
          <groupId>org.apache.flink</groupId>
        </exclusion>
        <exclusion>
          <artifactId>flink-dstl-dfs</artifactId>
          <groupId>org.apache.flink</groupId>
        </exclusion>
      </exclusions>
    </dependency>
  </dependencies>
  <properties>
    <scala.binary.version>2.12</scala.binary.version>
    <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    <maven.compiler.target>11</maven.compiler.target>
    <flink.version>1.18.0</flink.version>
    <maven.compiler.source>11</maven.compiler.source>
  </properties>
</project>
