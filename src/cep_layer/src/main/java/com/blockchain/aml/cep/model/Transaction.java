package com.blockchain.aml.cep.model;

import java.io.Serializable;
import java.time.Instant;
import java.util.Objects;

/**
 * Transaction model class for Flink CEP pattern matching
 * Represents an Ethereum transaction with AML-relevant attributes
 */
public class Transaction implements Serializable {
    private static final long serialVersionUID = 1L;
    
    private String hash;
    private String fromAddress;
    private String toAddress;
    private long value; // in Wei
    private long blockNumber;
    private long timestamp;
    private long gasUsed;
    private long gasPrice;
    private String methodId;
    private boolean isContract;
    
    // AML-specific derived fields
    private double valueEth;
    private boolean isRoundAmount;
    private boolean isHighValue;
    private boolean isMicroAmount;
    private String riskLevel;
    
    public Transaction() {}
    
    public Transaction(String hash, String fromAddress, String toAddress, long value, 
                      long blockNumber, long timestamp, long gasUsed, long gasPrice, 
                      String methodId, boolean isContract) {
        this.hash = hash;
        this.fromAddress = fromAddress;
        this.toAddress = toAddress;
        this.value = value;
        this.blockNumber = blockNumber;
        this.timestamp = timestamp;
        this.gasUsed = gasUsed;
        this.gasPrice = gasPrice;
        this.methodId = methodId;
        this.isContract = isContract;
        
        // Calculate derived fields
        this.valueEth = value / 1000000000000000000.0;
        this.isRoundAmount = isRoundAmount(value);
        this.isHighValue = value > 5000000000000000000L; // > 5 ETH
        this.isMicroAmount = value > 0 && value < 1000000000000000L; // < 0.001 ETH
        this.riskLevel = calculateRiskLevel();
    }
    
    private boolean isRoundAmount(long valueWei) {
        return valueWei == 1000000000000000000L ||    // 1 ETH
               valueWei == 100000000000000000L ||     // 0.1 ETH
               valueWei == 10000000000000000L ||      // 0.01 ETH
               valueWei == 50000000000000000L ||      // 0.05 ETH
               valueWei == 500000000000000000L ||     // 0.5 ETH
               valueWei == 2000000000000000000L ||    // 2 ETH
               valueWei == 5000000000000000000L;      // 5 ETH
    }
    
    private String calculateRiskLevel() {
        if (isHighValue) return "HIGH";
        if (isRoundAmount) return "MEDIUM";
        if (isMicroAmount) return "LOW";
        return "NORMAL";
    }
    
    // Getters and setters
    public String getHash() { return hash; }
    public void setHash(String hash) { this.hash = hash; }
    
    public String getFromAddress() { return fromAddress; }
    public void setFromAddress(String fromAddress) { this.fromAddress = fromAddress; }
    
    public String getToAddress() { return toAddress; }
    public void setToAddress(String toAddress) { this.toAddress = toAddress; }
    
    public long getValue() { return value; }
    public void setValue(long value) { 
        this.value = value;
        this.valueEth = value / 1000000000000000000.0;
        this.isRoundAmount = isRoundAmount(value);
        this.isHighValue = value > 5000000000000000000L;
        this.isMicroAmount = value > 0 && value < 1000000000000000L;
        this.riskLevel = calculateRiskLevel();
    }
    
    public long getBlockNumber() { return blockNumber; }
    public void setBlockNumber(long blockNumber) { this.blockNumber = blockNumber; }
    
    public long getTimestamp() { return timestamp; }
    public void setTimestamp(long timestamp) { this.timestamp = timestamp; }
    
    public long getGasUsed() { return gasUsed; }
    public void setGasUsed(long gasUsed) { this.gasUsed = gasUsed; }
    
    public long getGasPrice() { return gasPrice; }
    public void setGasPrice(long gasPrice) { this.gasPrice = gasPrice; }
    
    public String getMethodId() { return methodId; }
    public void setMethodId(String methodId) { this.methodId = methodId; }
    
    public boolean isContract() { return isContract; }
    public void setContract(boolean contract) { isContract = contract; }
    
    public double getValueEth() { return valueEth; }
    public void setValueEth(double valueEth) { this.valueEth = valueEth; }
    
    public boolean isRoundAmount() { return isRoundAmount; }
    public void setRoundAmount(boolean roundAmount) { isRoundAmount = roundAmount; }
    
    public boolean isHighValue() { return isHighValue; }
    public void setHighValue(boolean highValue) { isHighValue = highValue; }
    
    public boolean isMicroAmount() { return isMicroAmount; }
    public void setMicroAmount(boolean microAmount) { isMicroAmount = microAmount; }
    
    public String getRiskLevel() { return riskLevel; }
    
    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        Transaction that = (Transaction) o;
        return Objects.equals(hash, that.hash);
    }
    
    @Override
    public int hashCode() {
        return Objects.hash(hash);
    }
    
    @Override
    public String toString() {
        return "Transaction{" +
                "hash='" + hash + '\'' +
                ", from='" + fromAddress + '\'' +
                ", to='" + toAddress + '\'' +
                ", valueEth=" + String.format("%.6f", valueEth) +
                ", riskLevel='" + riskLevel + '\'' +
                ", timestamp=" + Instant.ofEpochSecond(timestamp) +
                '}';
    }
} 