package com.blockchain.aml.cep.model;

import java.io.Serializable;
import java.time.Instant;
import java.util.List;
import java.util.Objects;

/**
 * Alert model class representing AML alerts generated by CEP patterns
 */
public class Alert implements Serializable {
    private static final long serialVersionUID = 1L;
    
    private String alertId;
    private String patternName;
    private String alertType;
    private String severity;
    private String description;
    private long detectionTimestamp;
    private List<Transaction> involvedTransactions;
    private String primaryAddress;
    private double totalValue;
    private String riskScore;
    private String reason;
    
    public Alert() {}
    
    public Alert(String alertId, String patternName, String alertType, String severity, 
                String description, List<Transaction> involvedTransactions) {
        this.alertId = alertId;
        this.patternName = patternName;
        this.alertType = alertType;
        this.severity = severity;
        this.description = description;
        this.detectionTimestamp = Instant.now().toEpochMilli();
        this.involvedTransactions = involvedTransactions;
        
        // Calculate derived fields
        if (involvedTransactions != null && !involvedTransactions.isEmpty()) {
            this.primaryAddress = involvedTransactions.get(0).getFromAddress();
            this.totalValue = involvedTransactions.stream()
                    .mapToDouble(Transaction::getValueEth)
                    .sum();
            this.riskScore = calculateRiskScore();
        }
    }
    
    private String calculateRiskScore() {
        if (totalValue > 10.0) return "CRITICAL";
        if (totalValue > 5.0 || "HIGH".equals(severity)) return "HIGH";
        if (totalValue > 1.0 || "MEDIUM".equals(severity)) return "MEDIUM";
        return "LOW";
    }
    
    // Getters and setters
    public String getAlertId() { return alertId; }
    public void setAlertId(String alertId) { this.alertId = alertId; }
    
    public String getPatternName() { return patternName; }
    public void setPatternName(String patternName) { this.patternName = patternName; }
    
    public String getAlertType() { return alertType; }
    public void setAlertType(String alertType) { this.alertType = alertType; }
    
    public String getSeverity() { return severity; }
    public void setSeverity(String severity) { this.severity = severity; }
    
    public String getDescription() { return description; }
    public void setDescription(String description) { this.description = description; }
    
    public long getDetectionTimestamp() { return detectionTimestamp; }
    public void setDetectionTimestamp(long detectionTimestamp) { this.detectionTimestamp = detectionTimestamp; }
    
    public List<Transaction> getInvolvedTransactions() { return involvedTransactions; }
    public void setInvolvedTransactions(List<Transaction> involvedTransactions) { 
        this.involvedTransactions = involvedTransactions;
        if (involvedTransactions != null && !involvedTransactions.isEmpty()) {
            this.primaryAddress = involvedTransactions.get(0).getFromAddress();
            this.totalValue = involvedTransactions.stream()
                    .mapToDouble(Transaction::getValueEth)
                    .sum();
            this.riskScore = calculateRiskScore();
        }
    }
    
    public String getPrimaryAddress() { return primaryAddress; }
    public void setPrimaryAddress(String primaryAddress) { this.primaryAddress = primaryAddress; }
    
    public double getTotalValue() { return totalValue; }
    public void setTotalValue(double totalValue) { this.totalValue = totalValue; }
    
    public String getRiskScore() { return riskScore; }
    public void setRiskScore(String riskScore) { this.riskScore = riskScore; }
    
    public String getReason() { return reason; }
    public void setReason(String reason) { this.reason = reason; }
    
    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        Alert alert = (Alert) o;
        return Objects.equals(alertId, alert.alertId);
    }
    
    @Override
    public int hashCode() {
        return Objects.hash(alertId);
    }
    
    @Override
    public String toString() {
        return "Alert{" +
                "alertId='" + alertId + '\'' +
                ", patternName='" + patternName + '\'' +
                ", alertType='" + alertType + '\'' +
                ", severity='" + severity + '\'' +
                ", primaryAddress='" + primaryAddress + '\'' +
                ", totalValue=" + String.format("%.6f", totalValue) + " ETH" +
                ", riskScore='" + riskScore + '\'' +
                ", timestamp=" + Instant.ofEpochMilli(detectionTimestamp) +
                '}';
    }
} 