package com.blockchain.aml.cep;

import org.apache.flink.api.common.eventtime.SerializableTimestampAssigner;
import org.apache.flink.api.common.eventtime.WatermarkStrategy;
import org.apache.flink.api.common.functions.MapFunction;
import org.apache.flink.api.common.serialization.SimpleStringSchema;
import org.apache.flink.cep.CEP;
import org.apache.flink.cep.PatternStream;
import org.apache.flink.cep.functions.PatternProcessFunction;
import org.apache.flink.cep.pattern.Pattern;
import org.apache.flink.cep.pattern.conditions.IterativeCondition;
import org.apache.flink.cep.pattern.conditions.SimpleCondition;
import org.apache.flink.connector.kafka.sink.KafkaRecordSerializationSchema;
import org.apache.flink.connector.kafka.sink.KafkaSink;
import org.apache.flink.connector.kafka.source.KafkaSource;
import org.apache.flink.connector.kafka.source.enumerator.initializer.OffsetsInitializer;
import org.apache.flink.streaming.api.datastream.DataStream;
import org.apache.flink.streaming.api.datastream.KeyedStream;
import org.apache.flink.streaming.api.environment.StreamExecutionEnvironment;
import org.apache.flink.util.Collector;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.blockchain.aml.cep.model.Transaction;
import com.blockchain.aml.cep.model.Alert;

import java.math.BigInteger;
import java.time.Duration;
import java.time.Instant;
import java.util.*;

/**
 * 🔥 Live AML CEP - Processing Time Mode (FIXED for Zero Output Issue)
 * 
 * This version uses PROCESSING TIME to avoid watermark-related CEP issues.
 * Patterns trigger immediately when transactions arrive, bypassing event time dependencies.
 * Optimized for detecting AML patterns in live Ethereum Sepolia data.
 */
public class LiveAMLCEP {
    
    private static final String KAFKA_BROKERS = "kafka:29092";
    private static final String INPUT_TOPIC = "ethereum-transactions";
    private static final String OUTPUT_TOPIC = "filtered-transactions";
    private static final String CONSUMER_GROUP = "live-aml-cep";

    public static void main(String[] args) throws Exception {
        // Set up the streaming execution environment
        final StreamExecutionEnvironment env = StreamExecutionEnvironment.getExecutionEnvironment();
        
        // Configure for low latency and high throughput
        env.setParallelism(2);
        env.getConfig().setAutoWatermarkInterval(100L);
        
        System.out.println("🔥 Starting Live AML CEP - Optimized for Real Sepolia Transactions");
        
        // Kafka Source Configuration
        KafkaSource<String> source = KafkaSource.<String>builder()
                .setBootstrapServers(KAFKA_BROKERS)
                .setTopics(INPUT_TOPIC)
                .setGroupId(CONSUMER_GROUP)
                .setStartingOffsets(OffsetsInitializer.latest()) // Start from latest for live monitoring
                .setValueOnlyDeserializer(new SimpleStringSchema())
                .build();
        
        // Kafka Sink Configuration  
        KafkaSink<String> sink = KafkaSink.<String>builder()
                .setBootstrapServers(KAFKA_BROKERS)
                .setRecordSerializer(KafkaRecordSerializationSchema.builder()
                        .setTopic(OUTPUT_TOPIC)
                        .setValueSerializationSchema(new SimpleStringSchema())
                        .build())
                .build();
        
        // Create the data stream from Kafka and convert to Transaction objects
        DataStream<String> rawTransactions = env
                .fromSource(source, WatermarkStrategy.noWatermarks(), "Live Ethereum Transactions");
        
        // 🔧 PROCESSING TIME FIX: Use processing time with timestamp validation
        DataStream<Transaction> transactions = rawTransactions
                .map(new TransactionMapper())
                .filter(t -> t != null)
                .map(tx -> {
                    // 规范化时间戳
                    long now = Instant.now().toEpochMilli();
                    if (tx.getTimestamp() > now) {
                        System.out.println("⚠️ Future timestamp detected: " + tx.getTimestamp() + 
                                         " > " + now + ", normalizing to current time");
                        tx.setTimestamp(now);
                    }
                    return tx;
                });
        
        System.out.println("🎯 Applying Live-Optimized AML Patterns...");
        
        // 🔧 FINAL TEST: Use constant key with comprehensive debugging
        try {
            System.out.println("🔍 STEP 1: Starting pattern application...");
            
            System.out.println("🔍 STEP 2: About to apply keyBy with constant key...");
            KeyedStream<Transaction, String> keyedTransactions = transactions.keyBy(tx -> {
                System.out.println("🔑 KEYBY FUNCTION: Processing tx " + tx.getHash().substring(0, 8));
                return "constant-key"; // Use simple constant key
            });
            
            System.out.println("🔍 STEP 3: KeyBy operation completed successfully");
            
            // ==== CEP PATTERN INVOCATION ====
            System.out.println("🔍 STEP 4: About to invoke detectHighValuePattern...");
            DataStream<Alert> amlAlerts = detectHighValuePattern(keyedTransactions);
            System.out.println("🔍 STEP 5: detectHighValuePattern returned successfully");
            
            // ==== SERIALIZE AND SINK CEP ALERTS ====
            System.out.println("🔍 STEP 6: About to serialize and sink alerts...");
            amlAlerts
                .map(alert -> {
                    // Convert Alert to JSON string
                    ObjectMapper mapper = new ObjectMapper();
                    String alertJson = mapper.writeValueAsString(alert);
                    System.out.println("🚀 SINKING CEP ALERT: " + alertJson);
                    return alertJson;
                })
                .sinkTo(sink);
            
            System.out.println("🔍 STEP 7: Sink applied successfully - READY FOR EXECUTION");
            
        } catch (Exception e) {
            System.err.println("🚨 ERROR in CEP pattern application: " + e.getMessage());
            e.printStackTrace();
            throw e;
        }
        
        // Execute the streaming job
        env.execute("🔥 Live AML CEP - Real-Time Sepolia Pattern Detection");
    }
    
    /**
     * 📈 Pattern 1: High Value Pattern (Processing Time - Immediate Detection)
     * Detects any single transaction > 0.04 ETH for Sepolia testnet (HIGH RISK)
     * Uses PROCESSING TIME to bypass watermark dependencies
     */
    private static DataStream<Alert> detectHighValuePattern(KeyedStream<Transaction, String> transactions) {
        System.out.println("🔨 CREATING HIGH VALUE PATTERN (PROCESSING TIME)...");
        
        Pattern<Transaction, ?> highValuePattern = Pattern.<Transaction>begin("highValue")
                .where(SimpleCondition.of(tx -> {
                    // Lowered threshold for testnet (0.04 ETH for high sensitivity)
                    boolean isHigh = tx.getValueEth() > 0.04; // Very sensitive for Sepolia
                    System.out.println("📈 *** HIGH VALUE CONDITION *** tx " + tx.getHash().substring(0, 8) + " value=" + tx.getValueEth() + " ETH, isHigh=" + isHigh + " from=" + tx.getFromAddress().substring(0, 8));
                    return isHigh;
                }));
        
        System.out.println("🔨 APPLYING CEP PATTERN TO KEYED STREAM (PROCESSING TIME)...");
        PatternStream<Transaction> patternStream = CEP.pattern(transactions, highValuePattern).inProcessingTime();
        
        System.out.println("🔨 PROCESSING PATTERN MATCHES...");
        return patternStream.process(new PatternProcessFunction<Transaction, Alert>() {
            @Override
            public void processMatch(Map<String, List<Transaction>> match, Context ctx, Collector<Alert> out) {
                Transaction tx = match.get("highValue").get(0);
                
                Alert alert = new Alert(
                    UUID.randomUUID().toString(),
                    "HIGH_VALUE_LIVE",
                    "LARGE_TRANSACTION",
                    "HIGH",
                    String.format("📈 LIVE HIGH VALUE: Transaction of %.6f ETH (>0.04 ETH threshold) from %s", 
                                tx.getValueEth(), tx.getFromAddress().substring(0, 8)),
                    Arrays.asList(tx)
                );
                alert.setReason(String.format("Live transaction value %.6f ETH exceeds high-value threshold", tx.getValueEth()));
                alert.setRiskScore("HIGH");
                
                System.out.println("🚨 📈 LIVE HIGH VALUE ALERT: " + alert.getDescription());
                out.collect(alert);
            }
        });
    }
    
    // Transaction Mapper (same as enhanced version)
    public static class TransactionMapper implements MapFunction<String, Transaction> {
        private final ObjectMapper objectMapper = new ObjectMapper();

        @Override
        public Transaction map(String jsonString) throws Exception {
            try {
                JsonNode jsonNode = objectMapper.readTree(jsonString);
                
                System.out.println("📥 Live CEP PARSING: " + jsonNode.get("hash").asText().substring(0, 12));
                
                Transaction tx = new Transaction();
                tx.setHash(jsonNode.get("hash").asText());
                tx.setFromAddress(jsonNode.get("from_address").asText());
                tx.setToAddress(jsonNode.get("to_address").asText());
                
                // Parse value from Wei to ETH
                String valueStr = jsonNode.get("value").asText();
                BigInteger valueWei = new BigInteger(valueStr);
                double valueEth = valueWei.doubleValue() / 1e18;
                
                // 🔧 FIX: Set value FIRST (which will auto-calculate with wrong thresholds)
                tx.setValue(valueWei.longValue());
                
                // 🔧 FIX: Then OVERRIDE with our custom AML thresholds for Sepolia
                tx.setValueEth(valueEth);
                tx.setHighValue(valueEth > 0.04); // Very sensitive for Sepolia - AFTER setValue!
                tx.setRoundAmount(isRoundAmount(valueEth));
                tx.setMicroAmount(valueEth > 0 && valueEth < 0.01);
                
                tx.setBlockNumber(jsonNode.get("block_number").asLong());
                tx.setTimestamp(jsonNode.get("timestamp").asLong());
                tx.setGasUsed(jsonNode.get("gas_used").asLong());
                tx.setGasPrice(jsonNode.get("gas_price").asLong());
                tx.setMethodId(jsonNode.get("input_data").asText());
                tx.setContract(false); // Simplified for live analysis
                
                System.out.println("📥 Live PARSED TX: " + tx.getHash().substring(0, 8) + 
                                 ", value=" + tx.getValueEth() + " ETH" +
                                 ", isHighValue=" + tx.isHighValue() +
                                 ", isRound=" + tx.isRoundAmount() +
                                 ", isMicro=" + tx.isMicroAmount());
                
                return tx;
            } catch (Exception e) {
                System.err.println("❌ Live parsing error: " + e.getMessage());
                return null;
            }
        }
        
        private boolean isRoundAmount(double valueEth) {
            // Check if the value is a round number (0.1, 1.0, 10.0, etc.)
            return valueEth == 0.1 || valueEth == 1.0 || valueEth == 10.0 || 
                   valueEth == 0.01 || valueEth == 0.001 || valueEth == 0.05;
        }
    }

    // Alert to JSON Mapper (same as enhanced version)
    public static class AlertToJsonMapper implements MapFunction<Alert, String> {
        private final ObjectMapper objectMapper = new ObjectMapper();

        @Override
        public String map(Alert alert) throws Exception {
            return objectMapper.writeValueAsString(alert);
        }
    }
} 