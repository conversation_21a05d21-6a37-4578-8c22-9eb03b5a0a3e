# Multi-stage build for Flink CEP Application
FROM maven:3.9.4-eclipse-temurin-11 AS builder

# Set working directory
WORKDIR /app

# Copy Maven configuration
COPY pom.xml .

# Download dependencies (for layer caching)
RUN mvn dependency:go-offline -B

# Copy source code
COPY src/ ./src/

# Build the application
RUN mvn clean package -DskipTests

# Runtime image
FROM flink:1.18.0-scala_2.12

# Set working directory
WORKDIR /opt/flink

# Copy the built JAR file
COPY --from=builder /app/target/flink-cep-aml-1.0.0.jar /opt/flink/lib/

# Copy Kafka connector JARs (download during build)
RUN wget -P /opt/flink/lib/ https://repo1.maven.org/maven2/org/apache/flink/flink-connector-kafka/3.0.1-1.18/flink-connector-kafka-3.0.1-1.18.jar && \
    wget -P /opt/flink/lib/ https://repo1.maven.org/maven2/org/apache/flink/flink-json/1.18.0/flink-json-1.18.0.jar

# Set environment variables
ENV FLINK_CONF_DIR=/opt/flink/conf
ENV FLINK_LOG_DIR=/opt/flink/log

# Create log directory
RUN mkdir -p /opt/flink/log

# Expose Flink ports
EXPOSE 6123 8081

# Default command - can be overridden
CMD ["help"] 