#!/usr/bin/env python3
"""
Main entry point for the Hybrid Graph Construction Layer
Implements: Spark = Detection + Neo4j = Graph Persistence
"""

import os
import sys
import logging
from dotenv import load_dotenv

# Import the hybrid graph constructor
from neo4j_graph_builder import HybridGraphConstructor

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[logging.StreamHandler(sys.stdout)]
)

# Load environment variables
load_dotenv('configs/test_sepolia_config.env')
logger = logging.getLogger(__name__)

def main():
    """Main entry point for the Hybrid Graph Construction Layer"""
    try:
        logger.info("Starting Hybrid Graph Construction Layer")
        logger.info("Architecture: Spark = Detection + Neo4j = Graph Persistence")

        # Initialize hybrid constructor
        constructor = HybridGraphConstructor()
        logger.info("Initialized hybrid graph constructor")

        # Start the hybrid streaming pipeline
        logger.info("Starting hybrid streaming pipeline...")
        constructor.start_streaming()

    except KeyboardInterrupt:
        logger.info("Received shutdown signal")
    except Exception as e:
        logger.error(f"Fatal error in hybrid graph construction layer: {str(e)}")
        raise
    finally:
        # Cleanup
        if 'constructor' in locals():
            constructor.shutdown()

if __name__ == "__main__":
    main()