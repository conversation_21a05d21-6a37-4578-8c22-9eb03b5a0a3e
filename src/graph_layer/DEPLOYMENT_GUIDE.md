# Graph Construction Layer Deployment Guide

This guide provides steps for deploying the fixed Graph Construction Layer with proper Kafka connectivity.

## Prerequisites

- Docker installed and running
- Kafka and Zookeeper deployed and running
- Network `blockchain-aml-integrated-network` created (will be created automatically if missing)

## Key Fixes

This deployment addresses two critical issues:

1. **Hard-coded `startingOffsets = "latest"`** - Changed to use `"earliest"` to process historical data
2. **Missing consumer group ID (`group.id`)** - Added explicit consumer group ID setting

## Deployment Steps

### 1. Build and Deploy Fixed Container

```bash
# Navigate to the graph layer directory
cd src/graph_layer

# Make script executable
chmod +x scripts/build_and_deploy_fixed.sh

# Run deployment script
./scripts/build_and_deploy_fixed.sh
```

This script will:
- Stop and remove existing graph construction layer container
- Build new image with the fixed Dockerfile
- Start new container with correct environment variables
- Display container status and logs

### 2. Verify Deployment

#### Check if container is running

```bash
docker ps | grep graph-construction-layer
```

#### View logs

```bash
docker logs graph-construction-layer -f
```

#### Check Kafka consumer group

```bash
./scripts/check_consumer_group.sh
```

#### Check Kafka connectivity

```bash
./scripts/check_kafka_connection.sh
```

### 3. Send Test Data

```bash
# Make script executable
chmod +x scripts/send_test_data.sh

# Send test data
./scripts/send_test_data.sh
```

### 4. Monitor Graph Construction Process

```bash
# View batch processing logs
./scripts/monitor_graph_layer.sh batch

# Access Spark UI
# Open http://localhost:4040 in your browser
```

## Troubleshooting

### Problem: Container fails to start

Check Docker logs:

```bash
docker logs graph-construction-layer
```

Common issues:
- Java/JVM errors - Check Java installation in container
- Kafka connection errors - Verify Kafka is accessible
- ClassNotFound errors - Check if required JARs are in classpath

### Problem: Cannot connect to Kafka

Run the Kafka connection check script:

```bash
./scripts/check_kafka_connection.sh
```

Verify:
1. Kafka container is running
2. Network connectivity between containers
3. Correct bootstrap server configuration

### Problem: No data processing

1. Check if Kafka topic has data:

```bash
docker exec kafka-integrated kafka-console-consumer --bootstrap-server localhost:9092 --topic filtered-transactions --from-beginning --max-messages 5
```

2. Check consumer group status:

```bash
./scripts/check_consumer_group.sh
```

3. Check checkpoint directory:

```bash
docker exec graph-construction-layer ls -la /tmp/graph-layer-checkpoints-new
```

4. Try sending test data:

```bash
./scripts/send_test_data.sh
```

### Problem: ClassNotFoundException for Kafka classes

This indicates missing JAR files in the Spark classpath. The fixed Dockerfile includes all necessary dependencies, but if you're still experiencing this issue:

1. Verify JARs are present:

```bash
docker exec graph-construction-layer ls -la $SPARK_HOME/jars/kafka*
```

2. Check Java classpath:

```bash
docker exec graph-construction-layer bash -c 'echo $CLASSPATH'
```

3. Restart with explicit classpath:

```bash
docker restart graph-construction-layer
```

## Configuration Reference

| Environment Variable | Description | Default Value |
|---------------------|-------------|---------------|
| KAFKA_BOOTSTRAP_SERVERS | Kafka server address | kafka:29092 |
| KAFKA_INPUT_TOPIC | Input topic name | filtered-transactions |
| KAFKA_OUTPUT_TOPIC | Output topic name | graph-snapshots |
| KAFKA_CONSUMER_GROUP | Consumer group ID | graph-construction-consumer-context7 |
| KAFKA_STARTING_OFFSETS | Starting offsets setting | earliest |
| KAFKA_AUTO_OFFSET_RESET | Auto offset reset policy | earliest |
| CHECKPOINT_LOCATION | Checkpoint directory | /tmp/graph-layer-checkpoints-new |
| WINDOW_DURATION | Window duration for aggregation | 30 seconds |
| SLIDING_DURATION | Sliding interval | 10 seconds |

## Monitoring

### Spark UI

Access the Spark UI at http://localhost:4040 to monitor:
- Running jobs
- Streaming statistics
- Memory usage
- SQL queries

### Logs

Monitor specific log components:

```bash
# Monitor batch processing
./scripts/monitor_graph_layer.sh batch

# Monitor Kafka operations
./scripts/monitor_graph_layer.sh kafka

# Monitor errors
./scripts/monitor_graph_layer.sh error
```

### Kafka Consumer Groups

Monitor consumer group progress:

```bash
./scripts/check_consumer_group.sh
``` 