#!/usr/bin/env python3
"""
Test script for the hybrid architecture
Tests: Kafka -> Spark -> Neo4j pipeline
"""

import os
import sys
import json
import time
import logging
from datetime import datetime
from kafka import KafkaProducer, KafkaConsumer
from kafka.errors import KafkaError

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def create_test_alert():
    """Create a test CEP alert with suspicious transactions"""
    return {
        "alertId": "test_alert_001",
        "patternName": "RAPID_FIRE_TRANSACTIONS",
        "alertType": "SUSPICIOUS_ACTIVITY",
        "severity": "HIGH",
        "description": "Multiple rapid transactions detected",
        "detectionTimestamp": int(datetime.now().timestamp() * 1000),
        "involvedTransactions": [
            {
                "hash": "0xtest123",
                "fromAddress": "0xsuspicious_sender",
                "toAddress": "0xsuspicious_receiver",
                "value": "2000000000000000000",  # 2 ETH in Wei
                "blockNumber": 12345,
                "timestamp": int(datetime.now().timestamp() * 1000),
                "gasUsed": 21000,
                "gasPrice": 20000000000,
                "methodId": "0xa9059cbb",
                "valueEth": 2.0,
                "riskLevel": "HIGH",
                "contract": False
            },
            {
                "hash": "0xtest456",
                "fromAddress": "0xsuspicious_sender",
                "toAddress": "0xanother_receiver",
                "value": "1500000000000000000",  # 1.5 ETH in Wei
                "blockNumber": 12346,
                "timestamp": int(datetime.now().timestamp() * 1000),
                "gasUsed": 21000,
                "gasPrice": 20000000000,
                "methodId": "0xa9059cbb",
                "valueEth": 1.5,
                "riskLevel": "HIGH",
                "contract": False
            }
        ],
        "primaryAddress": "0xsuspicious_sender",
        "totalValue": 3.5,
        "riskScore": "0.85",
        "reason": "Rapid fire transactions with high values"
    }

def test_kafka_producer():
    """Test sending alerts to Kafka"""
    logger.info("Testing Kafka producer...")
    
    try:
        producer = KafkaProducer(
            bootstrap_servers=['localhost:9092'],
            value_serializer=lambda v: json.dumps(v).encode('utf-8'),
            key_serializer=lambda k: k.encode('utf-8') if k else None
        )
        
        # Create test alert
        test_alert = create_test_alert()
        
        # Send to filtered-transactions topic (what Flink CEP would produce)
        future = producer.send('filtered-transactions', value=test_alert, key='test_key')
        
        # Wait for send to complete
        record_metadata = future.get(timeout=10)
        
        logger.info(f"✓ Alert sent to Kafka topic: {record_metadata.topic}")
        logger.info(f"  Partition: {record_metadata.partition}")
        logger.info(f"  Offset: {record_metadata.offset}")
        
        producer.flush()
        producer.close()
        
        return True
        
    except Exception as e:
        logger.error(f"✗ Kafka producer test failed: {str(e)}")
        return False

def test_kafka_consumer():
    """Test consuming alerts from Kafka"""
    logger.info("Testing Kafka consumer...")
    
    try:
        consumer = KafkaConsumer(
            'filtered-transactions',
            bootstrap_servers=['localhost:9092'],
            value_deserializer=lambda m: json.loads(m.decode('utf-8')),
            consumer_timeout_ms=5000,  # 5 second timeout
            auto_offset_reset='latest'
        )
        
        logger.info("✓ Kafka consumer created, waiting for messages...")
        
        message_count = 0
        for message in consumer:
            logger.info(f"✓ Received message: {message.value['alertId']}")
            logger.info(f"  Transactions: {len(message.value['involvedTransactions'])}")
            message_count += 1
            
            if message_count >= 1:  # Just consume one message for test
                break
        
        consumer.close()
        
        if message_count > 0:
            logger.info(f"✓ Successfully consumed {message_count} messages")
            return True
        else:
            logger.warning("✗ No messages received within timeout")
            return False
            
    except Exception as e:
        logger.error(f"✗ Kafka consumer test failed: {str(e)}")
        return False

def test_neo4j_integration():
    """Test Neo4j graph storage"""
    logger.info("Testing Neo4j integration...")
    
    try:
        from neo4j import GraphDatabase
        
        # Connect to Neo4j
        driver = GraphDatabase.driver(
            'bolt://localhost:7687',
            auth=('neo4j', 'password123')
        )
        
        with driver.session() as session:
            # Create a test suspicious transaction
            query = """
            MERGE (from_account:Account {address: 'test_hybrid_from'})
            MERGE (to_account:Account {address: 'test_hybrid_to'})
            MERGE (from_account)-[transfer:TRANSFER {hash: 'test_hybrid_123'}]->(to_account)
            SET transfer.amount = 2.0,
                transfer.timestamp = datetime(),
                transfer.risk_score = 0.85,
                transfer.risk_factors = ['rapid_fire', 'high_value'],
                transfer.alert_id = 'test_alert_001',
                transfer.created_at = datetime()
            RETURN transfer.hash as hash
            """
            
            result = session.run(query)
            record = result.single()
            
            if record:
                logger.info(f"✓ Test transaction stored in Neo4j: {record['hash']}")
                
                # Query it back
                query_result = session.run("""
                MATCH (from:Account)-[t:TRANSFER]->(to:Account)
                WHERE t.hash = 'test_hybrid_123'
                RETURN from.address as from_addr, to.address as to_addr, 
                       t.amount as amount, t.risk_score as risk_score
                """)
                
                for record in query_result:
                    logger.info(f"✓ Retrieved: {record['from_addr']} -> {record['to_addr']}")
                    logger.info(f"  Amount: {record['amount']} ETH, Risk: {record['risk_score']}")
                
                # Clean up
                session.run("""
                MATCH (from:Account {address: 'test_hybrid_from'})-[t:TRANSFER]->(to:Account {address: 'test_hybrid_to'})
                WHERE t.hash = 'test_hybrid_123'
                DELETE t, from, to
                """)
                
                logger.info("✓ Test data cleaned up")
                return True
            else:
                logger.error("✗ Failed to create test transaction")
                return False
                
        driver.close()
        
    except Exception as e:
        logger.error(f"✗ Neo4j integration test failed: {str(e)}")
        return False

def main():
    """Run all hybrid architecture tests"""
    logger.info("=== Testing Hybrid Architecture ===")
    logger.info("Architecture: Kafka -> Spark -> Neo4j")
    
    tests = [
        ("Kafka Producer", test_kafka_producer),
        ("Neo4j Integration", test_neo4j_integration),
        ("Kafka Consumer", test_kafka_consumer),
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        logger.info(f"\n--- Running {test_name} Test ---")
        try:
            results[test_name] = test_func()
        except Exception as e:
            logger.error(f"Test {test_name} failed with exception: {str(e)}")
            results[test_name] = False
    
    # Summary
    logger.info("\n=== Test Results Summary ===")
    passed = 0
    total = len(tests)
    
    for test_name, result in results.items():
        status = "✓ PASS" if result else "✗ FAIL"
        logger.info(f"{test_name}: {status}")
        if result:
            passed += 1
    
    logger.info(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        logger.info("🎉 All hybrid architecture tests passed!")
        logger.info("Ready to run the full pipeline!")
        return True
    else:
        logger.error("❌ Some tests failed. Check the logs above.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
