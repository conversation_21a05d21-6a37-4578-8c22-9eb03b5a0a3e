# Graph Construction Layer Troubleshooting Guide

## 问题诊断与修复指南

本文档提供了针对图构建层（Graph Construction Layer）的常见问题诊断和修复方法，特别是针对Spark无法读取历史数据的问题。

## 已发现的问题

在代码审查中，我们发现了两个关键问题导致图构建层无法正常工作：

### 1. 硬编码的 `startingOffsets = "latest"`

在 `KafkaConfig.get_kafka_options` 和 `_create_kafka_source` 方法中，我们发现：

```python
kafka_options = {
  …,
  "startingOffsets": "latest",
  …,
  "auto.offset.reset": "latest",
  …
}
```

这意味着每次作业启动（或在清除检查点后重新启动）时，Spark会直接跳到主题的*末尾*，只读取启动后到达的*新*消息。如果您没有持续输入新数据，每个批次将显示零行。

### 2. 未设置消费者组ID (`group.id`)

默认情况下，如果您不提供消费者组ID，Spark将自动生成一个随机ID。这导致Kafka无法在您期望的名称下记录任何提交（因此 `--describe --group graph-construction-group` 声称该组"不存在"）。尽管您在 `settings.py` 中有 `kafka_consumer_group` 设置，但它实际上并未注入到您的Kafka选项中。

## 已实施的修复

我们已经修改了以下文件来解决这些问题：

1. `spark_graph_builder.py` - 修改了 `_create_kafka_source` 方法
2. `src/graph_layer/config/settings.py` - 修改了 `KafkaConfig.get_kafka_options` 方法

主要更改：

- 将 `"startingOffsets": "latest"` 更改为 `"startingOffsets": os.getenv("KAFKA_STARTING_OFFSETS", "earliest")`
- 将 `"auto.offset.reset": "latest"` 更改为 `"auto.offset.reset": os.getenv("KAFKA_AUTO_OFFSET_RESET", "earliest")`
- 添加了 `"group.id": settings.kafka_consumer_group` 显式设置消费者组ID

## 如何应用修复

我们创建了几个辅助脚本来简化修复过程：

### 1. 重启图构建层

```bash
./scripts/restart_graph_layer.sh
```

此脚本将：
- 停止图构建层容器
- 清除检查点目录
- 设置正确的环境变量
- 重启图构建层容器

### 2. 检查消费者组状态

```bash
./scripts/check_consumer_group.sh [消费者组名称]
```

此脚本将：
- 检查指定消费者组的状态（默认为 `graph-construction-consumer-context7`）
- 列出所有消费者组
- 显示可用的Kafka主题
- 显示 `filtered-transactions` 主题的详细信息

### 3. 监控图构建层日志

```bash
./scripts/monitor_graph_layer.sh [可选过滤词]
```

此脚本将：
- 显示图构建层容器的实时日志
- 可选地按关键词过滤日志

## 验证修复是否生效

修复成功后，您应该看到：

1. 消费者组 `graph-construction-consumer-context7` 存在并显示非零的 `CURRENT-OFFSET` 和 `LAG`
2. 图构建层日志中显示非零的行数处理
3. 图快照开始在 `graph-snapshots` Kafka主题中出现

## 其他建议

如果您仍然遇到问题：

1. 确保 `filtered-transactions` 主题中有数据
2. 检查图构建层容器的日志是否有错误
3. 确保容器有足够的资源（内存、CPU）
4. 检查网络连接，确保图构建层可以连接到Kafka

## 进一步优化

除了修复基本问题外，还可以考虑以下优化：

1. 调整批处理大小和触发间隔以优化性能
2. 根据您的数据量调整窗口和滑动持续时间
3. 为ARM64/Apple Silicon M3 Pro架构进一步优化Spark配置 