# Graph Construction Layer

## Overview

The Graph Construction Layer is a critical component of the blockchain analytics pipeline that:

1. Consumes filtered transaction alerts from Kafka
2. Applies sliding time windows to group related transactions
3. Constructs graph snapshots using PyTorch Geometric
4. Outputs graph snapshots to Kafka for further analysis

## Recent Fixes

We've addressed two critical issues that were preventing the graph construction layer from processing data:

1. **Hard-coded `startingOffsets = "latest"`** - This caused Spark to skip all historical data and only process new data arriving after the job started.
2. **Missing consumer group ID (`group.id`)** - This prevented <PERSON><PERSON><PERSON> from properly tracking and committing offsets.

### Implementation Details

- Modified Kafka configuration to use `"earliest"` for starting offsets
- Added explicit consumer group ID settings
- Created a simplified Dockerfile with proper dependencies
- Developed helper scripts for deployment, monitoring, and testing

## Deployment

See [DEPLOYMENT_GUIDE.md](./DEPLOYMENT_GUIDE.md) for detailed deployment instructions.

Quick start:

```bash
# Build and deploy
./scripts/build_and_deploy_fixed.sh

# Check Kafka connection
./scripts/check_kafka_connection.sh

# Send test data
./scripts/send_test_data.sh

# Monitor logs
./scripts/monitor_graph_layer.sh
```

## Troubleshooting

See [GRAPH_LAYER_TROUBLESHOOTING.md](./GRAPH_LAYER_TROUBLESHOOTING.md) for detailed troubleshooting steps.

Common issues:

1. **ClassNotFoundException** - Missing JAR files in the Spark classpath
2. **Kafka connectivity** - Network issues between containers
3. **No data processing** - Empty Kafka topic or offset issues

## Architecture

The Graph Construction Layer uses:

- **Apache Spark Structured Streaming** for real-time data processing
- **Kafka** for input/output messaging
- **PyTorch Geometric** for graph construction and analysis

### Data Flow

1. **Input**: Filtered transaction alerts from `filtered-transactions` Kafka topic
2. **Processing**: Apply sliding windows and construct graph snapshots
3. **Output**: Graph snapshots to `graph-snapshots` Kafka topic

## Configuration

Key environment variables:

| Variable | Description | Default |
|----------|-------------|---------|
| KAFKA_BOOTSTRAP_SERVERS | Kafka server address | kafka:29092 |
| KAFKA_INPUT_TOPIC | Input topic | filtered-transactions |
| KAFKA_CONSUMER_GROUP | Consumer group ID | graph-construction-consumer-context7 |
| KAFKA_STARTING_OFFSETS | Starting position | earliest |
| WINDOW_DURATION | Window size | 30 seconds |
| SLIDING_DURATION | Window slide interval | 10 seconds |

## Development

### Prerequisites

- Python 3.9+
- Apache Spark 3.5.0
- PyTorch 2.0.1
- PyTorch Geometric

### Local Setup

1. Install dependencies:
   ```bash
   pip install -r requirements.txt
   ```

2. Run tests:
   ```bash
   python test_optimized_performance.py
   ```

## Monitoring

### Spark UI

Access the Spark UI at http://localhost:4040 for:
- Job status and statistics
- Streaming query metrics
- Memory usage

### Log Monitoring

Monitor specific components:

```bash
# Batch processing
./scripts/monitor_graph_layer.sh batch

# Kafka operations
./scripts/monitor_graph_layer.sh kafka

# Errors
./scripts/monitor_graph_layer.sh error

# Graph construction
./scripts/monitor_graph_layer.sh graph
```

## 功能

- 从Kafka主题消费过滤后的交易数据
- 应用滑动时间窗口来创建图快照
- 使用PyTorch Geometric构建图结构
- 输出图快照到Kafka主题，供下游组件使用

## 架构

图构建层由以下主要组件组成：

1. **Spark Structured Streaming** - 用于高效的流数据处理
2. **Kafka连接器** - 用于从Kafka读取和写入数据
3. **图构造器** - 将交易数据转换为图结构
4. **PyTorch Geometric** - 用于图数据表示和操作

## 配置

图构建层可以通过环境变量进行配置：

| 环境变量 | 描述 | 默认值 |
|----------|------|--------|
| KAFKA_BOOTSTRAP_SERVERS | Kafka服务器地址 | kafka:29092 |
| KAFKA_INPUT_TOPIC | 输入主题名称 | filtered-transactions |
| KAFKA_OUTPUT_TOPIC | 输出主题名称 | graph-snapshots |
| KAFKA_CONSUMER_GROUP | 消费者组ID | graph-construction-consumer-context7 |
| KAFKA_STARTING_OFFSETS | 起始偏移量设置 | earliest |
| KAFKA_AUTO_OFFSET_RESET | 自动偏移量重置策略 | earliest |
| WINDOW_DURATION | 窗口持续时间 | 30 seconds |
| SLIDING_DURATION | 滑动持续时间 | 10 seconds |
| CHECKPOINT_LOCATION | 检查点位置 | /tmp/graph-layer-checkpoints-new |

## 部署

### 使用Docker

```bash
# 构建并部署修复后的容器
./scripts/build_and_deploy_fixed.sh
```

### 手动部署

```bash
# 构建Docker镜像
docker build -t docker-graph-layer-fixed -f Dockerfile.fixed .

# 运行容器
docker run -d --name graph-construction-layer \
  --network blockchain-aml-integrated-network \
  -p 4040:4040 \
  -e KAFKA_BOOTSTRAP_SERVERS="kafka:29092" \
  -e KAFKA_INPUT_TOPIC="filtered-transactions" \
  -e KAFKA_OUTPUT_TOPIC="graph-snapshots" \
  -e KAFKA_CONSUMER_GROUP="graph-construction-consumer-context7" \
  -e KAFKA_STARTING_OFFSETS="earliest" \
  -e KAFKA_AUTO_OFFSET_RESET="earliest" \
  docker-graph-layer-fixed
```

## 监控

### 查看日志

```bash
# 查看容器日志
docker logs graph-construction-layer -f

# 仅查看批处理相关日志
docker logs graph-construction-layer -f | grep -i 'batch'
```

### 检查Kafka消费者组

```bash
# 检查消费者组状态
docker exec kafka-integrated kafka-consumer-groups --bootstrap-server localhost:9092 --describe --group graph-construction-consumer-context7
```

### 访问Spark UI

Spark UI可通过以下地址访问：http://localhost:4040

## 故障排除

如果图构建层未能正确处理数据，请检查以下事项：

1. 确保Kafka主题中有数据：
   ```bash
   docker exec kafka-integrated kafka-console-consumer --bootstrap-server localhost:9092 --topic filtered-transactions --from-beginning --max-messages 5
   ```

2. 确认Kafka环境变量设置正确：
   ```bash
   docker exec graph-construction-layer env | grep KAFKA
   ```

3. 检查Spark UI中的流查询状态

4. 确认检查点目录是否存在并可写：
   ```bash
   docker exec graph-construction-layer ls -la /tmp/graph-layer-checkpoints-new
   ```

## 测试

要发送测试数据以验证图构建层是否正常工作：

```bash
./scripts/send_test_data.sh
```

## Overview

This layer consumes filtered transactions from the CEP (Complex Event Processing) layer via Kafka and constructs temporal graph snapshots using Apache Spark Structured Streaming and PyTorch Geometric.

### Key Features

- **Real-time Graph Construction**: Builds PyTorch Geometric graphs from streaming transaction data
- **Sliding Time Windows**: Creates overlapping temporal snapshots for time-series analysis
- **Advanced Node Features**: Includes temporal, statistical, and risk-based node features
- **Scalable Architecture**: Uses Apache Spark for distributed processing
- **Flexible Output**: Supports both Kafka streaming and file-based outputs

## Architecture

```
Kafka (filtered-transactions) → Spark Structured Streaming → Graph Constructor → PyTorch Geometric → Kafka (graph-snapshots)
```

### Components

1. **SparkGraphBuilder**: Main streaming application using Spark Structured Streaming
2. **GraphConstructor**: Core graph construction logic with PyTorch Geometric integration
3. **Configuration Management**: Pydantic-based settings with environment variable support
4. **Docker Integration**: Containerized deployment with Spark runtime

## Technical Stack

- **Apache Spark 3.5.0**: Distributed stream processing
- **PyTorch Geometric 2.4.0**: Graph neural network library
- **Kafka Integration**: Real-time data streaming
- **Python 3.9**: Core runtime environment

## Configuration

The layer is configured through environment variables and the `config/settings.py` file:

### Key Configuration Parameters

```python
# Streaming Configuration
WINDOW_DURATION="5 minutes"           # Time window size
SLIDING_DURATION="1 minute"           # Window slide interval
TRIGGER_PROCESSING_TIME="30 seconds"  # Processing trigger interval

# Graph Construction
MAX_NODES_PER_GRAPH=10000            # Maximum nodes per graph
NODE_FEATURE_DIM=64                  # Node feature dimensionality
EDGE_WEIGHT_THRESHOLD=0.0            # Minimum edge weight

# Kafka Configuration
KAFKA_INPUT_TOPIC="filtered-transactions"
KAFKA_OUTPUT_TOPIC="graph-snapshots"
KAFKA_BOOTSTRAP_SERVERS="kafka:29092"
```

## Graph Structure

### Node Features (64 dimensions)

1. **Basic Features** (5 dimensions):
   - Total transaction value
   - Transaction count
   - Average transaction value
   - Is contract flag
   - Risk score

2. **Temporal Features** (3 dimensions):
   - First seen timestamp
   - Last seen timestamp
   - Time span (last - first)

3. **Statistical Features** (3 dimensions):
   - Log(value + 1)
   - Log(transaction count + 1)
   - Square root of average value

4. **Padding** (53 dimensions): Zero-padded to reach 64 dimensions

### Edge Features (6 dimensions)

- Total value transferred
- Transaction count
- Average transaction value
- First transaction timestamp
- Last transaction timestamp
- Edge risk score

### Graph Metadata

Each graph snapshot includes:
- Window start/end timestamps
- Node and edge counts
- Maximum risk score
- Total transaction volume
- Construction metadata

## Data Flow

1. **Kafka Input**: Consumes filtered transactions from CEP layer
2. **Windowing**: Applies 5-minute sliding windows with 1-minute slides
3. **Graph Construction**: Builds PyTorch Geometric graphs for each window
4. **Feature Engineering**: Generates comprehensive node and edge features
5. **Serialization**: Converts graphs to JSON format for transmission
6. **Output**: Streams graph snapshots to Kafka or files

## Usage

### Local Development

```bash
# Install dependencies
pip install -r requirements.txt

# Set environment variables
export KAFKA_BOOTSTRAP_SERVERS="localhost:9092"
export KAFKA_INPUT_TOPIC="filtered-transactions"
export KAFKA_OUTPUT_TOPIC="graph-snapshots"

# Run the application
python spark_graph_builder.py
```

### Docker Deployment

```bash
# Build the image
docker build -t graph-construction-layer .

# Run the container
docker run -d \
  --name graph-layer \
  -e KAFKA_BOOTSTRAP_SERVERS="kafka:29092" \
  -e KAFKA_INPUT_TOPIC="filtered-transactions" \
  -e KAFKA_OUTPUT_TOPIC="graph-snapshots" \
  -p 4040:4040 \
  graph-construction-layer
```

### Integration with Pipeline

The layer integrates seamlessly with the existing docker-compose infrastructure:

```yaml
services:
  graph-layer:
    build: ./graph_layer
    environment:
      - KAFKA_BOOTSTRAP_SERVERS=kafka:29092
      - KAFKA_INPUT_TOPIC=filtered-transactions
      - KAFKA_OUTPUT_TOPIC=graph-snapshots
    depends_on:
      - kafka
      - cep-layer
    ports:
      - "4040:4040"  # Spark UI
```

## Performance Characteristics

### Target Performance

- **Latency**: <500ms per graph snapshot
- **Throughput**: 1000+ transactions per second
- **Window Processing**: Real-time 5-minute sliding windows
- **Graph Size**: Up to 10,000 nodes per snapshot

### Optimization Features

- **Spark SQL Adaptive Query Execution**: Automatic optimization
- **Watermark-based Late Data Handling**: Robust stream processing
- **Efficient Serialization**: Arrow-based columnar processing
- **Memory Management**: Configurable executor and driver memory

## Monitoring

### Spark UI

Access the Spark UI at `http://localhost:4040` to monitor:
- Streaming statistics
- Batch processing times
- Resource utilization
- Query execution plans

### Logging

Structured logging with configurable levels:
- Processing statistics
- Graph construction metrics
- Error handling and debugging
- Performance measurements

### Health Checks

- Spark UI availability check
- Kafka connectivity validation
- Processing lag monitoring

## Error Handling

### Fault Tolerance

- **Checkpoint Recovery**: Automatic recovery from failures
- **Graceful Degradation**: Continues processing despite individual failures
- **Dead Letter Handling**: Invalid data routing to error topics
- **Retry Mechanisms**: Configurable retry policies

### Error Scenarios

1. **Empty Windows**: Skips graph construction for windows with no transactions
2. **Large Graphs**: Truncates graphs exceeding node limits
3. **Malformed Data**: Logs errors and continues processing
4. **Kafka Connectivity**: Automatic reconnection and backoff

## Development Guidelines

### Adding New Features

1. **Node Features**: Extend `_build_node_features()` in `GraphConstructor`
2. **Edge Features**: Modify edge attribute calculation
3. **Graph Transformations**: Add PyTorch Geometric transforms
4. **Output Formats**: Extend output serialization methods

### Testing

```bash
# Run unit tests
pytest tests/

# Integration testing
python test_graph_layer_integration.py
```

### Code Quality

- Type hints throughout
- Comprehensive logging
- Error handling with context
- Performance monitoring

## Integration Points

### Upstream: CEP Layer
- **Input Topic**: `filtered-transactions`
- **Data Format**: JSON with AML-specific fields
- **Expected Fields**: hash, addresses, value, timestamps, risk scores

### Downstream: TGAT Model Layer
- **Output Topic**: `graph-snapshots`
- **Data Format**: Serialized PyTorch Geometric graphs
- **Graph Features**: 64-dimensional node features, 6-dimensional edge features

## Troubleshooting

### Common Issues

1. **Memory Errors**: Increase `spark.executor.memory` and `spark.driver.memory`
2. **Lag Building Up**: Reduce window size or increase parallelism
3. **Empty Graphs**: Check CEP layer output and filtering logic
4. **Serialization Errors**: Verify PyTorch Geometric compatibility

### Debug Mode

Enable debug logging:
```bash
export LOG_LEVEL=DEBUG
python spark_graph_builder.py
```

## Future Enhancements

- **Graph Persistence**: Optional graph database storage
- **Feature Store Integration**: MLflow feature store compatibility
- **Advanced Analytics**: Graph-level statistics and metrics
- **Multi-Modal Graphs**: Support for heterogeneous graph structures
- **Real-time Inference**: Direct integration with TGAT model serving 