# 已删除的文件记录

以下文件已被删除或替换，以简化代码库并修复问题：

## Docker文件

- `Dockerfile` -> 重命名为 `Dockerfile.old`（原始版本）
- `Dockerfile.context7` -> 保留作为参考，但不再使用
- `Dockerfile.optimized` -> 保留作为参考，但不再使用
- `Dockerfile.fixed` -> 重命名为 `Dockerfile`（当前使用的版本）

## 原因

1. 简化Docker文件管理 - 多个Docker文件导致混淆
2. 修复Kafka配置问题 - 新的Dockerfile包含了必要的修复
3. 保持一致性 - 确保所有容器使用相同的基础镜像和配置

## 修复的问题

1. 硬编码的 `startingOffsets = "latest"` - 这导致Spark跳过所有历史数据
2. 未设置消费者组ID (`group.id`) - 这导致Kafka无法记录提交

## 新增的脚本

- `scripts/build_and_deploy_fixed.sh` - 构建和部署修复后的Docker镜像
- `scripts/send_test_data.sh` - 发送测试数据到Kafka主题
- `scripts/check_consumer_group.sh` - 检查Kafka消费者组状态
- `scripts/monitor_graph_layer.sh` - 监控图构建层日志 