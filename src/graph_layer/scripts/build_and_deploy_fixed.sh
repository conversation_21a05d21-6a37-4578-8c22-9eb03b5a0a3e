#!/bin/bash

# <PERSON>ript to build and deploy the fixed Graph Construction Layer

echo "🔨 Building and deploying fixed Graph Construction Layer..."

# Navigate to the graph_layer directory
cd "$(dirname "$0")/.." || exit 1

# Stop the existing container
echo "⏹️ Stopping existing graph-construction-layer container..."
docker stop graph-construction-layer || true
docker rm graph-construction-layer || true

# Build the new image
echo "🏗️ Building new Docker image with Kafka fixes..."
docker build -t docker-graph-layer-fixed -f Dockerfile .

# Check if build was successful
if [ $? -ne 0 ]; then
  echo "❌ Docker build failed. Check the error messages above."
  exit 1
fi

# Check if network exists, create if not
if ! docker network ls | grep -q blockchain-aml-integrated-network; then
  echo "🌐 Creating blockchain-aml-integrated-network..."
  docker network create blockchain-aml-integrated-network
fi

# Create a new container with the right environment variables
echo "🚀 Starting new graph-construction-layer container..."
docker run -d --name graph-construction-layer \
  --network blockchain-aml-integrated-network \
  -p 4040:4040 \
  -e KAFKA_BOOTSTRAP_SERVERS="kafka:29092" \
  -e KAFKA_INPUT_TOPIC="filtered-transactions" \
  -e KAFKA_OUTPUT_TOPIC="graph-snapshots" \
  -e KAFKA_CONSUMER_GROUP="graph-construction-consumer-context7" \
  -e SPARK_APP_NAME="GraphConstructionLayer" \
  -e SPARK_MASTER="local[*]" \
  -e SPARK_EXECUTOR_MEMORY="800m" \
  -e SPARK_DRIVER_MEMORY="600m" \
  -e WINDOW_DURATION="30 seconds" \
  -e SLIDING_DURATION="10 seconds" \
  -e TRIGGER_PROCESSING_TIME="10 seconds" \
  -e WATERMARK_DELAY="15 seconds" \
  -e MAX_NODES_PER_GRAPH=5000 \
  -e NODE_FEATURE_DIM=64 \
  -e EDGE_WEIGHT_THRESHOLD=0.0 \
  -e INCLUDE_TEMPORAL_FEATURES="true" \
  -e INCLUDE_STATISTICAL_FEATURES="true" \
  -e MAX_OFFSETS_PER_TRIGGER=5000 \
  -e PARALLELISM=2 \
  -e OUTPUT_FORMAT="kafka" \
  -e CHECKPOINT_LOCATION="/tmp/graph-layer-checkpoints-new" \
  -e ENABLE_METRICS="true" \
  -e LOG_LEVEL="INFO" \
  -e KAFKA_STARTING_OFFSETS="earliest" \
  -e KAFKA_AUTO_OFFSET_RESET="earliest" \
  -v graph-checkpoints:/tmp/graph-layer-checkpoints \
  -v graph-outputs:/tmp/graph-outputs \
  --restart unless-stopped \
  docker-graph-layer-fixed

# Check if container started successfully
if [ $? -ne 0 ]; then
  echo "❌ Failed to start container. Check the error messages above."
  exit 1
fi

# Wait for container to start
echo "⏳ Waiting for container to initialize..."
sleep 20

# Check if container is running
echo "🔍 Checking container status..."
if ! docker ps | grep -q graph-construction-layer; then
  echo "❌ Container is not running. Checking logs..."
  docker logs graph-construction-layer
  exit 1
fi

# Show environment variables
echo "📋 Environment variables in container:"
docker exec graph-construction-layer env | grep -E 'KAFKA|OFFSET|CHECKPOINT'

# Show recent logs
echo "📋 Recent logs from graph-construction-layer:"
docker logs graph-construction-layer --tail 30

echo "✅ Graph layer deployed with Kafka fixes"
echo "   - Starting offsets: earliest (will read all historical data)"
echo "   - Consumer group: graph-construction-consumer-context7"
echo "   - Auto offset reset: earliest"
echo "   - New checkpoint location: /tmp/graph-layer-checkpoints-new"
echo ""
echo "To monitor progress, run: docker logs graph-construction-layer -f"
echo "To check consumer group status, run: ./scripts/check_consumer_group.sh"
echo "To send test data, run: ./scripts/send_test_data.sh" 