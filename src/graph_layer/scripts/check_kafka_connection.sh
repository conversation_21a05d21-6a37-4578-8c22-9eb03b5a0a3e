#!/bin/bash

# Script to check Kafka connection from graph-construction-layer

echo "🔍 Checking Kafka connection from graph-construction-layer..."

# Check if container is running
if ! docker ps | grep -q graph-construction-layer; then
  echo "❌ Container graph-construction-layer is not running"
  exit 1
fi

# Check if Kafka container is running
if ! docker ps | grep -q kafka-integrated; then
  echo "❌ Kafka container (kafka-integrated) is not running"
  exit 1
fi

# Check network connectivity
echo "📡 Testing network connectivity..."
docker exec graph-construction-layer ping -c 2 kafka

# Check Kafka bootstrap servers setting
echo "🔧 Checking Kafka bootstrap servers setting..."
BOOTSTRAP_SERVERS=$(docker exec graph-construction-layer env | grep KAFKA_BOOTSTRAP_SERVERS | cut -d= -f2)
echo "Bootstrap servers: $BOOTSTRAP_SERVERS"

# Check if Kafka port is accessible
echo "🔌 Testing Kafka port connectivity..."
docker exec graph-construction-layer bash -c "timeout 5 bash -c '</dev/tcp/kafka/29092' && echo '✅ Port 29092 is accessible' || echo '❌ Port 29092 is NOT accessible'"

# Check Java classpath for Kafka libraries
echo "📚 Checking Java classpath for Kafka libraries..."
docker exec graph-construction-layer bash -c "ls -la \$SPARK_HOME/jars/kafka* \$SPARK_HOME/jars/spark-sql-kafka*"

# Check Kafka topics
echo "📋 Checking Kafka topics..."
docker exec kafka-integrated kafka-topics --bootstrap-server localhost:9092 --list

# Check consumer groups
echo "👥 Checking consumer groups..."
docker exec kafka-integrated kafka-consumer-groups --bootstrap-server localhost:9092 --list

# Check Spark application logs for Kafka errors
echo "📜 Checking for Kafka errors in logs..."
docker logs graph-construction-layer 2>&1 | grep -i -E "kafka|exception|error|fail" | tail -20

echo "✅ Kafka connection check completed"
echo "If you're still experiencing issues, try:"
echo "1. Restart the graph-construction-layer container: ./scripts/restart_graph_layer.sh"
echo "2. Check if Kafka topics exist and have data"
echo "3. Verify consumer group settings in the code" 