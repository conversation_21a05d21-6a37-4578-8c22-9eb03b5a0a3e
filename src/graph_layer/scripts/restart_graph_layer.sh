#!/bin/bash

# <PERSON>ript to clear checkpoint directory and restart the graph layer container
# This fixes issues with Spark not reading historical data due to checkpoint state

echo "🔄 Restarting Graph Construction Layer with optimized settings..."

# Stop the graph layer container
echo "⏹️ Stopping graph-construction-layer container..."
docker stop graph-construction-layer

# Clear the checkpoint directory in the container
echo "🗑️ Clearing checkpoint directory..."
docker exec -it kafka-integrated bash -c "rm -rf /tmp/graph-layer-checkpoints-arm64/* || true"
docker exec -it kafka-integrated bash -c "rm -rf /tmp/graph-layer-checkpoints-context7/* || true"

# Create a new checkpoint directory
echo "📁 Creating new checkpoint directory..."
docker exec -it kafka-integrated bash -c "mkdir -p /tmp/graph-layer-checkpoints-new"

# Create a new container with the right environment variables
echo "🔄 Recreating container with correct environment variables..."
docker rm graph-construction-layer

# Run the container with the correct environment variables
echo "🚀 Starting new graph-construction-layer container..."
docker run -d --name graph-construction-layer \
  --network blockchain-aml-integrated-network \
  -p 4040:4040 \
  -e KAFKA_BOOTSTRAP_SERVERS="kafka:29092" \
  -e KAFKA_INPUT_TOPIC="filtered-transactions" \
  -e KAFKA_OUTPUT_TOPIC="graph-snapshots" \
  -e KAFKA_CONSUMER_GROUP="graph-construction-consumer-context7" \
  -e SPARK_APP_NAME="GraphConstructionLayer" \
  -e SPARK_MASTER="local[*]" \
  -e SPARK_EXECUTOR_MEMORY="800m" \
  -e SPARK_DRIVER_MEMORY="600m" \
  -e WINDOW_DURATION="30 seconds" \
  -e SLIDING_DURATION="10 seconds" \
  -e TRIGGER_PROCESSING_TIME="10 seconds" \
  -e WATERMARK_DELAY="15 seconds" \
  -e MAX_NODES_PER_GRAPH=5000 \
  -e NODE_FEATURE_DIM=64 \
  -e EDGE_WEIGHT_THRESHOLD=0.0 \
  -e INCLUDE_TEMPORAL_FEATURES="true" \
  -e INCLUDE_STATISTICAL_FEATURES="true" \
  -e MAX_OFFSETS_PER_TRIGGER=5000 \
  -e PARALLELISM=2 \
  -e OUTPUT_FORMAT="kafka" \
  -e CHECKPOINT_LOCATION="/tmp/graph-layer-checkpoints-new" \
  -e ENABLE_METRICS="true" \
  -e LOG_LEVEL="INFO" \
  -e KAFKA_STARTING_OFFSETS="earliest" \
  -e KAFKA_AUTO_OFFSET_RESET="earliest" \
  -v graph-checkpoints:/tmp/graph-layer-checkpoints \
  -v graph-outputs:/tmp/graph-outputs \
  --restart unless-stopped \
  --health-cmd "curl -f http://localhost:4040 || exit 1" \
  --health-interval 30s \
  --health-timeout 10s \
  --health-retries 3 \
  --health-start-period 60s \
  docker-graph-layer

# Wait for container to start
echo "⏳ Waiting for container to initialize..."
sleep 20

# Check if container is running
echo "🔍 Checking container status..."
docker ps | grep graph-construction-layer

# Show environment variables
echo "📋 Environment variables in container:"
docker exec graph-construction-layer env | grep -E 'KAFKA|OFFSET|CHECKPOINT'

# Show recent logs
echo "📋 Recent logs from graph-construction-layer:"
docker logs graph-construction-layer --tail 30

echo "✅ Graph layer restarted with optimized settings"
echo "   - Starting offsets: earliest (will read all historical data)"
echo "   - Consumer group: graph-construction-consumer-context7"
echo "   - Auto offset reset: earliest"
echo "   - New checkpoint location: /tmp/graph-layer-checkpoints-new"
echo ""
echo "To monitor progress, run: docker logs graph-construction-layer -f" 