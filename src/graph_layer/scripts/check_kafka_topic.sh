#!/bin/bash

TOPIC=$1
KAFKA_BROKER=$2

if [ -z "$TOPIC" ] || [ -z "$KAFKA_BROKER" ]; then
    echo "Usage: $0 <topic_name> <kafka_broker>"
    exit 1
fi

# Wait for Kafka to be available
until echo exit | nc ${KAFKA_BROKER//:/ } >/dev/null 2>&1; do
    echo "Waiting for Kafka to be available..."
    sleep 5
done

# Check if topic exists
if kafkacat -L -b $KAFKA_BROKER | grep -q "topic $TOPIC"; then
    echo "Topic $TOPIC exists"
    exit 0
else
    echo "Topic $TOPIC does not exist"
    exit 1
fi 