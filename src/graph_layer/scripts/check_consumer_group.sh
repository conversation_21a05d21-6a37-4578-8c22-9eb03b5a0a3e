#!/bin/bash

# Script to check Kafka consumer group status for graph construction layer

CONSUMER_GROUP=${1:-graph-construction-consumer-context7}

echo "📊 Checking Kafka consumer group: $CONSUMER_GROUP"
echo ""

# Check if consumer group exists
echo "🔍 Consumer group status:"
docker exec kafka-integrated kafka-consumer-groups --bootstrap-server localhost:9092 --describe --group $CONSUMER_GROUP

# List all consumer groups
echo ""
echo "📋 All consumer groups:"
docker exec kafka-integrated kafka-consumer-groups --bootstrap-server localhost:9092 --list

# Check Kafka topics
echo ""
echo "📚 Available Kafka topics:"
docker exec kafka-integrated kafka-topics --bootstrap-server localhost:9092 --list

# Check topic details for filtered-transactions
echo ""
echo "📝 Details for filtered-transactions topic:"
docker exec kafka-integrated kafka-topics --bootstrap-server localhost:9092 --describe --topic filtered-transactions

echo ""
echo "💡 If the consumer group doesn't exist or shows no offsets, it means:"
echo "   1. The graph layer hasn't successfully connected to Kafka, or"
echo "   2. The consumer group name is different than expected, or"
echo "   3. No messages have been consumed yet"
echo ""
echo "🔄 To restart the graph layer with fixed settings, run:"
echo "   ./scripts/restart_graph_layer.sh" 