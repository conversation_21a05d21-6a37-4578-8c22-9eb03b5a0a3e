#!/bin/bash

# Script to send test data to filtered-transactions Kafka topic

echo "📤 Sending test data to filtered-transactions topic..."

# Sample transaction data
TEST_DATA='{
  "alertId":"test-alert-001",
  "patternName":"TEST_PATTERN",
  "alertType":"TEST_ALERT",
  "severity":"MEDIUM",
  "description":"Test transaction for graph construction",
  "detectionTimestamp":'$(date +%s%3N)',
  "involvedTransactions":[
    {
      "hash":"0x123456789abcdef",
      "fromAddress":"0xABCDEF1234567890",
      "toAddress":"0x0987654321FEDCBA",
      "value":100000000000000000,
      "blockNumber":12345678,
      "timestamp":'$(date +%s)',
      "gasUsed":21000,
      "gasPrice":20000000000,
      "methodId":"",
      "valueEth":0.1,
      "riskLevel":"LOW",
      "roundAmount":true,
      "highValue":false,
      "microAmount":false,
      "contract":false
    },
    {
      "hash":"0x987654321fedcba",
      "fromAddress":"0x0987654321FEDCBA",
      "toAddress":"0x5555555555555555",
      "value":50000000000000000,
      "blockNumber":12345679,
      "timestamp":'$(date +%s)',
      "gasUsed":21000,
      "gasPrice":20000000000,
      "methodId":"",
      "valueEth":0.05,
      "riskLevel":"LOW",
      "roundAmount":true,
      "highValue":false,
      "microAmount":false,
      "contract":false
    }
  ],
  "primaryAddress":"0xABCDEF1234567890",
  "totalValue":0.15,
  "riskScore":"LOW",
  "reason":"Test data for graph construction"
}'

# Send the test data to Kafka
echo "$TEST_DATA" | docker exec -i kafka-integrated kafka-console-producer --bootstrap-server localhost:9092 --topic filtered-transactions

echo "✅ Test data sent to filtered-transactions topic"
echo ""
echo "To check if the graph layer is processing the data, run:"
echo "docker logs graph-construction-layer -f | grep -i 'batch'" 