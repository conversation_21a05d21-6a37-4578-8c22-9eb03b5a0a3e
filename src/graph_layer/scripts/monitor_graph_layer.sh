#!/bin/bash

# Script to monitor graph construction layer logs

FILTER=${1:-all}

echo "📊 Monitoring Graph Construction Layer logs..."
echo ""
echo "Press Ctrl+C to exit"
echo ""

# Check if container is running
if ! docker ps | grep -q graph-construction-layer; then
  echo "❌ Container graph-construction-layer is not running"
  exit 1
fi

# Follow logs with filtering based on parameter
case $FILTER in
  batch)
    echo "🔍 Filtering logs for batch processing..."
    docker logs graph-construction-layer -f | grep -i -E 'batch|window|process'
    ;;
  kafka)
    echo "🔍 Filtering logs for Kafka operations..."
    docker logs graph-construction-layer -f | grep -i -E 'kafka|topic|consumer|producer|offset'
    ;;
  error)
    echo "🔍 Filtering logs for errors and warnings..."
    docker logs graph-construction-layer -f | grep -i -E 'error|exception|warn|fail'
    ;;
  graph)
    echo "🔍 Filtering logs for graph construction..."
    docker logs graph-construction-layer -f | grep -i -E 'graph|node|edge|construct'
    ;;
  *)
    echo "🔍 Showing all logs"
    docker logs graph-construction-layer -f
    ;;
esac 