#!/bin/bash

# Script to check if graph-snapshots topic is receiving data

echo "📊 Checking graph-snapshots Kafka topic for output..."

# Check if Kafka container is running
if ! docker ps | grep -q kafka-integrated; then
  echo "❌ Kafka container (kafka-integrated) is not running"
  exit 1
fi

# Check if topic exists
echo "🔍 Checking if graph-snapshots topic exists..."
if ! docker exec kafka-integrated kafka-topics --bootstrap-server localhost:9092 --list | grep -q graph-snapshots; then
  echo "❌ graph-snapshots topic does not exist"
  echo "Creating topic..."
  docker exec kafka-integrated kafka-topics --bootstrap-server localhost:9092 --create --topic graph-snapshots --partitions 1 --replication-factor 1
fi

# Check if topic has data
echo "🔍 Checking for data in graph-snapshots topic..."
echo "Showing the most recent messages (will wait for 10 seconds)..."

# Try to consume messages with a timeout
timeout 10s docker exec kafka-integrated kafka-console-consumer \
  --bootstrap-server localhost:9092 \
  --topic graph-snapshots \
  --from-beginning \
  --max-messages 3 || echo "No messages found or timeout occurred"

echo ""
echo "📈 Topic statistics:"
docker exec kafka-integrated kafka-topics --bootstrap-server localhost:9092 --describe --topic graph-snapshots

echo ""
echo "💡 If no messages are shown:"
echo "   1. The graph layer might not be processing any data yet"
echo "   2. Try sending test data: ./scripts/send_test_data.sh"
echo "   3. Check the graph layer logs: ./scripts/monitor_graph_layer.sh"
echo ""
echo "To continuously monitor the output topic, run:"
echo "docker exec kafka-integrated kafka-console-consumer --bootstrap-server localhost:9092 --topic graph-snapshots" 