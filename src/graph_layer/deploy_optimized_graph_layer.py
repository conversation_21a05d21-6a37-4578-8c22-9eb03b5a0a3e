#!/usr/bin/env python3
"""
Deploy Optimized Graph Construction Layer

This script deploys the performance-optimized Graph Construction Layer
with enhanced monitoring and testing capabilities for live Sepolia transactions.
"""

import os
import sys
import time
import json
import logging
import subprocess
import signal
from datetime import datetime
from typing import Dict, Any, Optional, List
import docker
import yaml

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class OptimizedGraphLayerDeployer:
    """Deployer for the optimized Graph Construction Layer"""
    
    def __init__(self):
        self.docker_client = docker.from_env()
        self.project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
        self.graph_layer_path = os.path.join(self.project_root, "graph_layer")
        self.deployment_config = self._load_deployment_config()
        
    def _load_deployment_config(self) -> Dict[str, Any]:
        """Load deployment configuration"""
        
        return {
            "services": {
                "kafka": {
                    "image": "confluentinc/cp-kafka:7.4.0",
                    "environment": {
                        "KAFKA_ZOOKEEPER_CONNECT": "zookeeper:2181",
                        "KAFKA_ADVERTISED_LISTENERS": "PLAINTEXT://kafka:29092",
                        "KAFKA_OFFSETS_TOPIC_REPLICATION_FACTOR": "1",
                        "KAFKA_AUTO_CREATE_TOPICS_ENABLE": "true"
                    },
                    "ports": ["29092:29092"],
                    "depends_on": ["zookeeper"]
                },
                "zookeeper": {
                    "image": "confluentinc/cp-zookeeper:7.4.0",
                    "environment": {
                        "ZOOKEEPER_CLIENT_PORT": "2181",
                        "ZOOKEEPER_TICK_TIME": "2000"
                    },
                    "ports": ["2181:2181"]
                },
                "graph-layer": {
                    "build": {
                        "context": ".",
                        "dockerfile": "graph_layer/Dockerfile"
                    },
                    "environment": {
                        "KAFKA_BOOTSTRAP_SERVERS": "kafka:29092",
                        "KAFKA_INPUT_TOPIC": "filtered-transactions",
                        "KAFKA_OUTPUT_TOPIC": "graph-snapshots",
                        "KAFKA_CONSUMER_GROUP_ID": "graph-construction-consumer-context7",
                        "KAFKA_STARTING_OFFSETS": "earliest",
                        "WINDOW_DURATION": "30 seconds",
                        "SLIDING_DURATION": "10 seconds",
                        "WATERMARK_DELAY": "15 seconds",
                        "SPARK_EXECUTOR_MEMORY": "4g",
                        "SPARK_DRIVER_MEMORY": "2g",
                        "SPARK_EXECUTOR_CORES": "4",
                        "TRIGGER_PROCESSING_TIME": "10 seconds",
                        "MAX_OFFSETS_PER_TRIGGER": "5000",
                        "ENABLE_DETAILED_METRICS": "true",
                        "ENABLE_PERFORMANCE_LOGGING": "true",
                        "ENABLE_VECTORIZED_OPERATIONS": "true",
                        "ENABLE_FEATURE_CACHING": "true",
                        "ENABLE_GRAPH_PRUNING": "true",
                        "NODE_FEATURE_DIM": "32",
                        "MAX_NODES_PER_GRAPH": "5000",
                        "EDGE_WEIGHT_THRESHOLD": "0.001"
                    },
                    "depends_on": ["kafka"],
                    "volumes": [
                        "./logs:/app/logs",
                        "./data:/app/data"
                    ]
                }
            },
            "performance_targets": {
                "latency_ms": 500,
                "throughput_rps": 1000,
                "success_rate": 0.95
            }
        }
    
    def create_optimized_dockerfile(self):
        """Create optimized Dockerfile for the Graph Construction Layer"""
        
        dockerfile_content = """
# Multi-stage build for optimized Graph Construction Layer
FROM openjdk:11-jdk-slim as spark-base

# Install Python and system dependencies
RUN apt-get update && apt-get install -y \\
    python3 \\
    python3-pip \\
    python3-dev \\
    build-essential \\
    curl \\
    wget \\
    && rm -rf /var/lib/apt/lists/*

# Install Spark
ENV SPARK_VERSION=3.5.0
ENV HADOOP_VERSION=3
RUN wget https://archive.apache.org/dist/spark/spark-${SPARK_VERSION}/spark-${SPARK_VERSION}-bin-hadoop${HADOOP_VERSION}.tgz \\
    && tar -xzf spark-${SPARK_VERSION}-bin-hadoop${HADOOP_VERSION}.tgz \\
    && mv spark-${SPARK_VERSION}-bin-hadoop${HADOOP_VERSION} /opt/spark \\
    && rm spark-${SPARK_VERSION}-bin-hadoop${HADOOP_VERSION}.tgz

ENV SPARK_HOME=/opt/spark
ENV PATH=$PATH:$SPARK_HOME/bin:$SPARK_HOME/sbin
ENV PYTHONPATH=$SPARK_HOME/python:$SPARK_HOME/python/lib/py4j-********-src.zip

# Production stage
FROM spark-base as production

# Set working directory
WORKDIR /app

# Copy requirements and install Python dependencies
COPY graph_layer/requirements.txt .
RUN pip3 install --no-cache-dir -r requirements.txt

# Copy application code
COPY graph_layer/ ./graph_layer/
COPY ingestion/config/ ./ingestion/config/

# Set environment variables for optimization
ENV PYTHONUNBUFFERED=1
ENV SPARK_LOCAL_IP=0.0.0.0
ENV JAVA_OPTS="-XX:+UseG1GC -XX:+UnlockDiagnosticVMOptions"

# Create directories
RUN mkdir -p /app/logs /app/data /tmp/graph-layer-checkpoints-v2

# Set permissions
RUN chmod +x graph_layer/spark_graph_builder.py

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \\
    CMD python3 -c "import requests; requests.get('http://localhost:4040')" || exit 1

# Default command
CMD ["python3", "graph_layer/spark_graph_builder.py"]
"""
        
        dockerfile_path = os.path.join(self.graph_layer_path, "Dockerfile")
        with open(dockerfile_path, 'w') as f:
            f.write(dockerfile_content)
        
        logger.info(f"Created optimized Dockerfile: {dockerfile_path}")
    
    def create_docker_compose(self):
        """Create optimized docker-compose configuration"""
        
        compose_config = {
            "version": "3.8",
            "services": self.deployment_config["services"],
            "networks": {
                "graph-layer-network": {
                    "driver": "bridge"
                }
            },
            "volumes": {
                "kafka-data": {},
                "zookeeper-data": {},
                "graph-layer-logs": {},
                "graph-layer-checkpoints": {}
            }
        }
        
        # Add network to all services
        for service_name in compose_config["services"]:
            compose_config["services"][service_name]["networks"] = ["graph-layer-network"]
        
        # Add volumes to graph-layer service
        compose_config["services"]["graph-layer"]["volumes"].extend([
            "graph-layer-logs:/app/logs",
            "graph-layer-checkpoints:/tmp/graph-layer-checkpoints-v2"
        ])
        
        compose_path = os.path.join(self.project_root, "docker-compose-optimized-graph.yml")
        with open(compose_path, 'w') as f:
            yaml.dump(compose_config, f, default_flow_style=False)
        
        logger.info(f"Created optimized docker-compose: {compose_path}")
        return compose_path
    
    def build_optimized_image(self):
        """Build the optimized Graph Construction Layer image"""
        
        logger.info("Building optimized Graph Construction Layer image...")
        
        try:
            # Build the image
            image, build_logs = self.docker_client.images.build(
                path=self.project_root,
                dockerfile="graph_layer/Dockerfile",
                tag="graph-layer-optimized:latest",
                rm=True,
                forcerm=True
            )
            
            logger.info(f"Successfully built image: {image.id}")
            
            # Log build output
            for log in build_logs:
                if 'stream' in log:
                    logger.debug(log['stream'].strip())
            
            return image
            
        except Exception as e:
            logger.error(f"Failed to build image: {str(e)}")
            raise
    
    def deploy_infrastructure(self, compose_file: str):
        """Deploy the infrastructure using docker-compose"""
        
        logger.info("Deploying optimized Graph Construction Layer infrastructure...")
        
        try:
            # Stop existing containers
            subprocess.run([
                "docker-compose", "-f", compose_file, "down", "-v"
            ], check=False, cwd=self.project_root)
            
            # Start infrastructure
            result = subprocess.run([
                "docker-compose", "-f", compose_file, "up", "-d"
            ], check=True, cwd=self.project_root, capture_output=True, text=True)
            
            logger.info("Infrastructure deployed successfully")
            logger.info(f"Docker-compose output: {result.stdout}")
            
            return True
            
        except subprocess.CalledProcessError as e:
            logger.error(f"Failed to deploy infrastructure: {e.stderr}")
            raise
    
    def wait_for_services(self, timeout: int = 120):
        """Wait for services to be ready"""
        
        logger.info("Waiting for services to be ready...")
        
        services_to_check = [
            ("kafka", "kafka:29092", "Kafka"),
            ("zookeeper", "zookeeper:2181", "Zookeeper")
        ]
        
        start_time = time.time()
        
        for service_name, endpoint, display_name in services_to_check:
            logger.info(f"Checking {display_name} readiness...")
            
            while time.time() - start_time < timeout:
                try:
                    # Check if container is running
                    container = self.docker_client.containers.get(f"fyp-2-copy_{service_name}_1")
                    if container.status == "running":
                        logger.info(f"{display_name} is ready")
                        break
                except:
                    pass
                
                time.sleep(5)
            else:
                raise TimeoutError(f"{display_name} not ready within {timeout} seconds")
        
        # Additional wait for Kafka to be fully ready
        logger.info("Waiting additional 30 seconds for Kafka to be fully ready...")
        time.sleep(30)
        
        logger.info("All services are ready")
    
    def create_performance_monitor(self):
        """Create performance monitoring script"""
        
        monitor_script = """#!/usr/bin/env python3
import time
import json
import logging
import docker
from datetime import datetime
from typing import Dict, Any, List

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class GraphLayerMonitor:
    def __init__(self):
        self.docker_client = docker.from_env()
        
    def get_container_stats(self, container_name: str) -> Dict[str, Any]:
        try:
            container = self.docker_client.containers.get(container_name)
            stats = container.stats(stream=False)
            
            # Calculate CPU usage
            cpu_delta = stats['cpu_stats']['cpu_usage']['total_usage'] - stats['precpu_stats']['cpu_usage']['total_usage']
            system_delta = stats['cpu_stats']['system_cpu_usage'] - stats['precpu_stats']['system_cpu_usage']
            cpu_percent = (cpu_delta / system_delta) * len(stats['cpu_stats']['cpu_usage']['percpu_usage']) * 100
            
            # Calculate memory usage
            memory_usage = stats['memory_stats']['usage']
            memory_limit = stats['memory_stats']['limit']
            memory_percent = (memory_usage / memory_limit) * 100
            
            return {
                'container': container_name,
                'cpu_percent': cpu_percent,
                'memory_usage_mb': memory_usage / (1024 * 1024),
                'memory_percent': memory_percent,
                'timestamp': datetime.now().isoformat()
            }
        except Exception as e:
            logger.error(f"Error getting stats for {container_name}: {str(e)}")
            return {}
    
    def monitor_performance(self, duration: int = 300):
        containers = ['fyp-2-copy_graph-layer_1', 'fyp-2-copy_kafka_1']
        
        logger.info(f"Starting performance monitoring for {duration} seconds...")
        
        start_time = time.time()
        metrics = []
        
        while time.time() - start_time < duration:
            for container in containers:
                stats = self.get_container_stats(container)
                if stats:
                    metrics.append(stats)
                    logger.info(f"{container}: CPU={stats.get('cpu_percent', 0):.1f}%, Memory={stats.get('memory_usage_mb', 0):.1f}MB")
            
            time.sleep(10)
        
        # Save metrics
        with open('/tmp/graph_layer_performance_metrics.json', 'w') as f:
            json.dump(metrics, f, indent=2)
        
        logger.info(f"Performance monitoring completed. Metrics saved.")

if __name__ == "__main__":
    monitor = GraphLayerMonitor()
    monitor.monitor_performance()
"""
        
        monitor_path = os.path.join(self.project_root, "monitor_optimized_graph_layer.py")
        with open(monitor_path, 'w') as f:
            f.write(monitor_script)
        
        os.chmod(monitor_path, 0o755)
        logger.info(f"Created performance monitor: {monitor_path}")
        return monitor_path
    
    def test_with_live_data(self):
        """Test the optimized Graph Construction Layer with live Sepolia data"""
        
        logger.info("Testing optimized Graph Construction Layer with live Sepolia data...")
        
        # Check if ingestion service is running
        try:
            # Start ingestion if not running
            ingestion_result = subprocess.run([
                "docker-compose", "-f", "docker-compose-ingestion.yml", "up", "-d"
            ], cwd=self.project_root, capture_output=True, text=True)
            
            logger.info("Ingestion service started")
            
            # Wait for data to flow
            logger.info("Waiting for live transaction data to flow...")
            time.sleep(60)
            
            # Check Graph Layer logs
            try:
                container = self.docker_client.containers.get("fyp-2-copy_graph-layer_1")
                logs = container.logs(tail=50).decode('utf-8')
                
                logger.info("Recent Graph Layer logs:")
                for line in logs.split('\n')[-20:]:
                    if line.strip():
                        logger.info(f"  {line}")
                
                # Look for performance metrics in logs
                if "construction completed" in logs:
                    logger.info("✅ Graph construction is working!")
                if "Processing batch" in logs:
                    logger.info("✅ Batch processing is active!")
                if "performance" in logs.lower():
                    logger.info("✅ Performance monitoring is active!")
                    
            except Exception as e:
                logger.warning(f"Could not retrieve logs: {str(e)}")
            
        except Exception as e:
            logger.error(f"Error testing with live data: {str(e)}")
    
    def deploy_and_test(self):
        """Complete deployment and testing workflow"""
        
        logger.info("🚀 Starting optimized Graph Construction Layer deployment...")
        
        try:
            # Step 1: Create optimized configurations
            logger.info("Step 1: Creating optimized configurations...")
            self.create_optimized_dockerfile()
            compose_file = self.create_docker_compose()
            monitor_script = self.create_performance_monitor()
            
            # Step 2: Build optimized image
            logger.info("Step 2: Building optimized image...")
            self.build_optimized_image()
            
            # Step 3: Deploy infrastructure
            logger.info("Step 3: Deploying infrastructure...")
            self.deploy_infrastructure(compose_file)
            
            # Step 4: Wait for services
            logger.info("Step 4: Waiting for services to be ready...")
            self.wait_for_services()
            
            # Step 5: Test with live data
            logger.info("Step 5: Testing with live Sepolia data...")
            self.test_with_live_data()
            
            logger.info("🎉 Optimized Graph Construction Layer deployment completed successfully!")
            logger.info(f"📊 Performance monitor available: {monitor_script}")
            logger.info(f"🐳 Docker compose file: {compose_file}")
            
            # Print status
            self.print_deployment_status()
            
        except Exception as e:
            logger.error(f"❌ Deployment failed: {str(e)}")
            raise
    
    def print_deployment_status(self):
        """Print deployment status and useful information"""
        
        logger.info("\n" + "="*60)
        logger.info("OPTIMIZED GRAPH CONSTRUCTION LAYER - DEPLOYMENT STATUS")
        logger.info("="*60)
        
        try:
            # Check running containers
            containers = self.docker_client.containers.list()
            graph_layer_containers = [c for c in containers if 'graph-layer' in c.name]
            
            if graph_layer_containers:
                container = graph_layer_containers[0]
                logger.info(f"✅ Graph Layer Status: {container.status}")
                logger.info(f"📦 Container Name: {container.name}")
                logger.info(f"🏷️  Image: {container.image.tags[0] if container.image.tags else 'unknown'}")
            else:
                logger.warning("⚠️  Graph Layer container not found")
            
            # Performance targets
            targets = self.deployment_config["performance_targets"]
            logger.info(f"\n🎯 Performance Targets:")
            logger.info(f"   • Latency: < {targets['latency_ms']}ms")
            logger.info(f"   • Throughput: > {targets['throughput_rps']} records/second")
            logger.info(f"   • Success Rate: > {targets['success_rate']*100}%")
            
            # Monitoring commands
            logger.info(f"\n📊 Monitoring Commands:")
            logger.info(f"   • View logs: docker logs fyp-2-copy_graph-layer_1 -f")
            logger.info(f"   • Monitor performance: python3 monitor_optimized_graph_layer.py")
            logger.info(f"   • Check topics: docker exec fyp-2-copy_kafka_1 kafka-topics --list --bootstrap-server localhost:9092")
            
            logger.info("\n" + "="*60)
            
        except Exception as e:
            logger.error(f"Error printing status: {str(e)}")


def main():
    """Main deployment function"""
    
    print("🔧 Optimized Graph Construction Layer Deployer")
    print("=" * 50)
    
    deployer = OptimizedGraphLayerDeployer()
    
    try:
        deployer.deploy_and_test()
        
        print("\n✅ Deployment completed successfully!")
        print("🔄 The system is now processing live Sepolia transactions")
        print("📈 Monitor performance using the provided tools")
        
    except KeyboardInterrupt:
        print("\n⏹️  Deployment interrupted by user")
    except Exception as e:
        print(f"\n❌ Deployment failed: {str(e)}")
        sys.exit(1)


if __name__ == "__main__":
    main() 