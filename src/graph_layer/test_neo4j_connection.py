#!/usr/bin/env python3
"""
Test Neo4j connection and basic graph operations
"""

import os
import sys
import logging
from datetime import datetime
from neo4j import GraphDatabase

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_neo4j_connection():
    """Test Neo4j connection and basic operations"""
    
    # Neo4j connection settings
    uri = os.getenv('NEO4J_URI', 'bolt://localhost:7687')
    user = os.getenv('NEO4J_USER', 'neo4j')
    password = os.getenv('NEO4J_PASSWORD', 'password123')
    
    logger.info(f"Testing Neo4j connection to {uri}")
    
    try:
        # Create driver
        driver = GraphDatabase.driver(uri, auth=(user, password))
        
        # Test connection
        with driver.session() as session:
            # Test basic query
            result = session.run("RETURN 'Hello Neo4j!' as message")
            record = result.single()
            logger.info(f"Connection successful: {record['message']}")
            
            # Create test nodes and relationship
            logger.info("Creating test transaction graph...")
            
            test_query = """
            // Create test accounts
            MERGE (from_account:Account {address: 'test_from_address'})
            MERGE (to_account:Account {address: 'test_to_address'})
            
            // Create test transaction
            MERGE (from_account)-[transfer:TRANSFER {hash: 'test_hash_123'}]->(to_account)
            SET transfer.amount = 1.5,
                transfer.timestamp = datetime(),
                transfer.risk_score = 0.8,
                transfer.risk_factors = ['high_value', 'suspicious_amount'],
                transfer.block_number = 12345,
                transfer.gas_used = 21000,
                transfer.is_contract = false,
                transfer.created_at = datetime()
            
            RETURN transfer.hash as transaction_hash, 
                   from_account.address as from_addr,
                   to_account.address as to_addr
            """
            
            result = session.run(test_query)
            record = result.single()
            
            if record:
                logger.info(f"Test transaction created: {record['transaction_hash']}")
                logger.info(f"From: {record['from_addr']} -> To: {record['to_addr']}")
            
            # Query the graph
            logger.info("Querying test graph...")
            
            query_result = session.run("""
            MATCH (from:Account)-[t:TRANSFER]->(to:Account)
            WHERE t.hash = 'test_hash_123'
            RETURN from.address as from_address,
                   to.address as to_address,
                   t.amount as amount,
                   t.risk_score as risk_score,
                   t.risk_factors as risk_factors
            """)
            
            for record in query_result:
                logger.info(f"Found transaction: {record['from_address']} -> {record['to_address']}")
                logger.info(f"Amount: {record['amount']} ETH, Risk Score: {record['risk_score']}")
                logger.info(f"Risk Factors: {record['risk_factors']}")
            
            # Clean up test data
            logger.info("Cleaning up test data...")
            session.run("""
            MATCH (from:Account {address: 'test_from_address'})-[t:TRANSFER]->(to:Account {address: 'test_to_address'})
            WHERE t.hash = 'test_hash_123'
            DELETE t, from, to
            """)
            
            logger.info("Test completed successfully!")
            
        driver.close()
        return True
        
    except Exception as e:
        logger.error(f"Neo4j connection test failed: {str(e)}")
        return False

if __name__ == "__main__":
    success = test_neo4j_connection()
    sys.exit(0 if success else 1)
