"""
Graph Construction Layer Configuration Settings - Context7 Enhanced

This module contains all configuration settings for the Graph Construction Layer,
including Spark, Kafka, and graph construction parameters optimized for high-performance streaming
with latest Context7 optimizations from Apache Spark and PyTorch Geometric.
"""

import os
from pydantic import BaseModel, Field
from typing import List, Dict, Any


class GraphLayerSettings(BaseModel):
    """Configuration settings for Graph Construction Layer - Context7 Performance Optimized"""
    
    # Spark Configuration - Enhanced with Context7 Optimizations
    spark_app_name: str = Field(default="GraphConstructionLayer-Context7-Optimized")
    spark_master: str = Field(default="local[2]")          # Reduced for container efficiency
    spark_executor_memory: str = Field(default="1200m")    # Context7 container optimization
    spark_driver_memory: str = Field(default="800m")       # ARM64 container optimized
    spark_executor_cores: int = Field(default=2)           # Reduced for memory efficiency
    spark_sql_adaptive_enabled: bool = Field(default=True)
    spark_sql_adaptive_coalesce_partitions_enabled: bool = Field(default=True)
    spark_sql_adaptive_skew_join_enabled: bool = Field(default=True)
    spark_sql_adaptive_local_shuffle_reader_enabled: bool = Field(default=True)  # Context7 optimization
    
    # Context7 Streaming Performance Optimizations
    spark_streaming_backpressure_enabled: bool = Field(default=True)
    spark_streaming_receiver_max_rate: int = Field(default=8000)      # Increased based on Context7
    spark_streaming_kafka_max_rate_per_partition: int = Field(default=3000)  # Per-partition rate limit
    spark_serializer_buffer_max: str = Field(default="256m")          # Larger serialization buffer
    
    # Context7 Asynchronous Progress Tracking (Major Performance Enhancement)
    enable_async_progress_tracking: bool = Field(default=True)
    async_progress_tracking_checkpoint_interval_ms: int = Field(default=500)  # Context7 optimization
    
    # Context7 Continuous Processing Mode for Ultra-Low Latency
    enable_continuous_processing: bool = Field(default=True)
    continuous_checkpoint_interval: str = Field(default="1 second")
    
    # Context7 RocksDB State Store Optimizations
    rocksdb_compact_on_commit: bool = Field(default=False)  # Context7 performance tuning
    rocksdb_changelog_checkpointing_enabled: bool = Field(default=True)
    rocksdb_block_size_kb: int = Field(default=8)  # Increased from default 4KB
    rocksdb_block_cache_size_mb: int = Field(default=32)  # Increased cache size
    rocksdb_write_buffer_size_mb: int = Field(default=128)  # Context7 optimization
    rocksdb_max_write_buffer_number: int = Field(default=4)
    rocksdb_track_total_number_of_rows: bool = Field(default=False)  # Context7 performance optimization
    
    # Kafka Configuration - Context7 Enhanced Performance Settings
    kafka_bootstrap_servers: str = Field(default="kafka:29092")
    kafka_input_topic: str = Field(default="filtered-transactions")
    kafka_output_topic: str = Field(default="graph-snapshots")
    kafka_consumer_group: str = Field(default="graph-construction-consumer-context7")
    
    # Context7 Kafka Performance Tuning
    kafka_fetch_min_bytes: int = Field(default=100000)                # Increased batch size
    kafka_fetch_max_wait_ms: int = Field(default=200)                 # Reduced latency
    kafka_max_partition_fetch_bytes: int = Field(default=20971520)    # 20MB per partition
    kafka_session_timeout_ms: int = Field(default=45000)
    kafka_heartbeat_interval_ms: int = Field(default=3000)
    
    # Context7 Kafka Producer Optimizations
    kafka_producer_cache_timeout_ms: int = Field(default=300000)      # 5 minutes
    kafka_producer_cache_eviction_interval_ms: int = Field(default=30000)  # 30 seconds
    kafka_producer_batch_size: int = Field(default=65536)             # 64KB batches
    kafka_producer_linger_ms: int = Field(default=5)                  # Reduced for lower latency
    kafka_producer_compression_type: str = Field(default="snappy")     # Context7 recommendation
    
    # Streaming Configuration - Context7 Optimized for <200ms Target
    trigger_processing_time: str = Field(default="5 seconds")         # Faster processing
    checkpoint_location: str = Field(default="/tmp/graph-layer-checkpoints-context7")
    max_offsets_per_trigger: int = Field(default=8000)                # Larger batches for efficiency
    
    # Graph Construction Parameters - Context7 Performance Optimized
    window_duration: str = Field(default="1 minute")                  # Changed from 5 minutes - faster graphs
    sliding_duration: str = Field(default="10 seconds")               # Changed from 15 seconds - more frequent
    watermark_delay: str = Field(default="15 seconds")                # Changed from 30 seconds - reduced latency
    
    # Context7 Graph Features Configuration - Enhanced
    max_nodes_per_graph: int = Field(default=8000)                    # Increased capacity
    edge_weight_threshold: float = Field(default=0.0005)               # More selective filtering
    include_self_loops: bool = Field(default=False)
    enable_graph_pruning: bool = Field(default=True)
    graph_pruning_threshold: float = Field(default=0.1)               # Context7 optimization
    
    # Context7 PyTorch Geometric Optimizations
    node_feature_dim: int = Field(default=64)                         # Increased for better representation
    include_temporal_features: bool = Field(default=True)
    include_statistical_features: bool = Field(default=True)
    enable_feature_caching: bool = Field(default=True)
    feature_cache_size: int = Field(default=1000)                     # Larger cache
    
    # Context7 torch.compile Optimizations
    enable_torch_compile: bool = Field(default=True)                  # Context7 major optimization
    torch_compile_mode: str = Field(default="default")                # Options: default, reduce-overhead, max-autotune
    torch_compile_fullgraph: bool = Field(default=False)              # Set to True for maximum optimization
    
    # Context7 Vectorized Operations and Memory Optimizations
    enable_vectorized_operations: bool = Field(default=True)
    enable_sparse_tensor_operations: bool = Field(default=True)       # Context7 optimization
    enable_memory_efficient_attention: bool = Field(default=True)     # Context7 optimization
    batch_processing_chunk_size: int = Field(default=5000)            # Context7 optimization
    
    # Performance Configuration - Context7 Enhanced
    graph_construction_timeout: int = Field(default=10)               # Reduced timeout
    batch_size: int = Field(default=3000)                             # Optimized batch size
    parallelism: int = Field(default=12)                              # Increased parallelism
    
    # Context7 Memory Management Optimizations
    spark_executor_memory_overhead: str = Field(default="2g")
    spark_driver_max_result_size: str = Field(default="4g")
    spark_sql_execution_arrow_max_records_per_batch: int = Field(default=50000)  # Context7 optimization
    enable_arrow_based_columnar_data_transfers: bool = Field(default=True)
    
    # Context7 Advanced Performance Settings
    enable_speculation: bool = Field(default=True)
    speculation_multiplier: float = Field(default=1.2)                # Context7 tuning
    dynamic_allocation_enabled: bool = Field(default=True)
    dynamic_allocation_min_executors: int = Field(default=3)
    dynamic_allocation_max_executors: int = Field(default=15)
    dynamic_allocation_cached_executor_idle_timeout: str = Field(default="30s")
    
    # Context7 Monitoring Configuration - Enhanced
    enable_metrics: bool = Field(default=True)
    enable_detailed_metrics: bool = Field(default=True)
    enable_streaming_metrics: bool = Field(default=True)              # Context7 streaming metrics
    metrics_port: int = Field(default=8085)
    log_level: str = Field(default="INFO")
    enable_performance_logging: bool = Field(default=True)
    
    # Context7 Performance Monitoring
    latency_percentiles: List[float] = Field(default=[0.5, 0.90, 0.95, 0.99])
    enable_throughput_monitoring: bool = Field(default=True)
    performance_sample_rate: float = Field(default=0.2)               # Sample 20% for metrics
    latency_target_ms: float = Field(default=200.0)                   # Context7 enhanced target
    
    # Context7 Graph-Specific Optimizations
    enable_graph_caching: bool = Field(default=True)
    graph_cache_size: int = Field(default=500)
    enable_incremental_graph_updates: bool = Field(default=True)      # Context7 optimization
    enable_graph_compression: bool = Field(default=True)
    
    # Output Configuration - Context7 Enhanced
    output_format: str = Field(default="kafka")
    file_output_path: str = Field(default="/tmp/graph-outputs")
    enable_compression: bool = Field(default=True)
    compression_codec: str = Field(default="snappy")                  # Context7 recommendation

    @classmethod
    def from_env(cls) -> "GraphLayerSettings":
        """Load settings from environment variables"""
        return cls(
            spark_app_name=os.getenv("SPARK_APP_NAME", "GraphConstructionLayer-Context7-Optimized"),
            spark_master=os.getenv("SPARK_MASTER", "local[2]"),
            spark_executor_memory=os.getenv("SPARK_EXECUTOR_MEMORY", "1200m"),
            spark_driver_memory=os.getenv("SPARK_DRIVER_MEMORY", "800m"),
            spark_executor_cores=int(os.getenv("SPARK_EXECUTOR_CORES", "2")),
            kafka_bootstrap_servers=os.getenv("KAFKA_BOOTSTRAP_SERVERS", "kafka:29092"),
            kafka_input_topic=os.getenv("KAFKA_INPUT_TOPIC", "filtered-transactions"),
            kafka_output_topic=os.getenv("KAFKA_OUTPUT_TOPIC", "graph-snapshots"),
            kafka_consumer_group=os.getenv("KAFKA_CONSUMER_GROUP", "graph-construction-consumer-context7"),
            trigger_processing_time=os.getenv("TRIGGER_PROCESSING_TIME", "5 seconds"),
            checkpoint_location=os.getenv("CHECKPOINT_LOCATION", "/tmp/graph-layer-checkpoints-context7"),
            max_offsets_per_trigger=int(os.getenv("MAX_OFFSETS_PER_TRIGGER", "8000")),
            window_duration=os.getenv("WINDOW_DURATION", "1 minute"),
            sliding_duration=os.getenv("SLIDING_DURATION", "10 seconds"),
            watermark_delay=os.getenv("WATERMARK_DELAY", "15 seconds"),
            max_nodes_per_graph=int(os.getenv("MAX_NODES_PER_GRAPH", "8000")),
            edge_weight_threshold=float(os.getenv("EDGE_WEIGHT_THRESHOLD", "0.0005")),
            include_self_loops=os.getenv("INCLUDE_SELF_LOOPS", "False").lower() == "true",
            node_feature_dim=int(os.getenv("NODE_FEATURE_DIM", "64")),
            include_temporal_features=os.getenv("INCLUDE_TEMPORAL_FEATURES", "True").lower() == "true",
            include_statistical_features=os.getenv("INCLUDE_STATISTICAL_FEATURES", "True").lower() == "true",
            graph_construction_timeout=int(os.getenv("GRAPH_CONSTRUCTION_TIMEOUT", "10")),
            batch_size=int(os.getenv("BATCH_SIZE", "3000")),
            parallelism=int(os.getenv("PARALLELISM", "12")),
            enable_metrics=os.getenv("ENABLE_METRICS", "True").lower() == "true",
            enable_detailed_metrics=os.getenv("ENABLE_DETAILED_METRICS", "True").lower() == "true",
            metrics_port=int(os.getenv("METRICS_PORT", "8085")),
            log_level=os.getenv("LOG_LEVEL", "INFO"),
            enable_performance_logging=os.getenv("ENABLE_PERFORMANCE_LOGGING", "True").lower() == "true",
            output_format=os.getenv("OUTPUT_FORMAT", "kafka"),
            file_output_path=os.getenv("FILE_OUTPUT_PATH", "/tmp/graph-outputs"),
            enable_compression=os.getenv("ENABLE_COMPRESSION", "True").lower() == "true",
            enable_vectorized_operations=os.getenv("ENABLE_VECTORIZED_OPERATIONS", "True").lower() == "true"
        )


class KafkaConfig:
    """Kafka-specific configuration helper - Context7 Performance Optimized"""
    
    @staticmethod
    def get_kafka_options(settings: GraphLayerSettings) -> Dict[str, Any]:
        kafka_options = {
            "kafka.bootstrap.servers": settings.kafka_bootstrap_servers,
            "subscribe": settings.kafka_input_topic,
            "startingOffsets": "latest",
            "maxOffsetsPerTrigger": str(settings.max_offsets_per_trigger),
            "failOnDataLoss": "false",
            "kafkaConsumer.pollTimeoutMs": "120000",
            
            # Context7 Performance optimizations
            "fetch.min.bytes": str(settings.kafka_fetch_min_bytes),
            "fetch.max.wait.ms": str(settings.kafka_fetch_max_wait_ms),
            "max.partition.fetch.bytes": str(settings.kafka_max_partition_fetch_bytes),
            "session.timeout.ms": str(settings.kafka_session_timeout_ms),
            "heartbeat.interval.ms": str(settings.kafka_heartbeat_interval_ms),
            "enable.auto.commit": "false",  # Manual commit for better control
            "auto.offset.reset": "latest",
            
            # Context7 Consumer optimizations
            "max.poll.records": "10000",  # Larger poll for better throughput
            "receive.buffer.bytes": "262144",  # 256KB receive buffer
            "send.buffer.bytes": "262144",  # 256KB send buffer
            "compression.type": settings.kafka_producer_compression_type,
            
            # Context7 Producer optimizations
            "batch.size": str(settings.kafka_producer_batch_size),
            "linger.ms": str(settings.kafka_producer_linger_ms),
            "buffer.memory": "67108864",  # 64MB buffer
            "acks": "1",  # Balance between performance and durability
            "retries": "3",
            "retry.backoff.ms": "100",
            "request.timeout.ms": "30000",
            "delivery.timeout.ms": "120000",
        }
        
        # Context7 Asynchronous Progress Tracking
        if settings.enable_async_progress_tracking:
            kafka_options.update({
                "asyncProgressTrackingEnabled": "true",
                "asyncProgressTrackingCheckpointIntervalMs": str(settings.async_progress_tracking_checkpoint_interval_ms)
            })
            
        return kafka_options
    
    @staticmethod
    def get_kafka_sink_options(settings: GraphLayerSettings) -> Dict[str, Any]:
        """Get Kafka sink options for output - Context7 Optimized"""
        return {
            "kafka.bootstrap.servers": settings.kafka_bootstrap_servers,
            "topic": settings.kafka_output_topic,
            
            # Context7 Producer optimizations for output
            "kafka.batch.size": str(settings.kafka_producer_batch_size),
            "kafka.linger.ms": str(settings.kafka_producer_linger_ms),
            "kafka.compression.type": settings.kafka_producer_compression_type,
            "kafka.acks": "1",
            "kafka.retries": "3",
            "kafka.buffer.memory": "67108864",  # 64MB
            "kafka.max.request.size": "10485760",  # 10MB
            "kafka.request.timeout.ms": "30000",
            "kafka.delivery.timeout.ms": "120000",
        }


class SparkConfig:
    """Spark-specific configuration helper - Context7 Performance Optimized"""
    
    @staticmethod
    def get_spark_config(settings: GraphLayerSettings) -> Dict[str, str]:
        config = {
            "spark.app.name": settings.spark_app_name,
            "spark.master": settings.spark_master,
            "spark.executor.memory": settings.spark_executor_memory,
            "spark.driver.memory": settings.spark_driver_memory,
            "spark.executor.cores": str(settings.spark_executor_cores),
            "spark.sql.adaptive.enabled": str(settings.spark_sql_adaptive_enabled),
            "spark.sql.adaptive.coalescePartitions.enabled": str(settings.spark_sql_adaptive_coalesce_partitions_enabled),
            "spark.sql.adaptive.skewJoin.enabled": str(settings.spark_sql_adaptive_skew_join_enabled),
            "spark.sql.adaptive.localShuffleReader.enabled": str(settings.spark_sql_adaptive_local_shuffle_reader_enabled),
            "spark.sql.streaming.metricsEnabled": str(settings.enable_streaming_metrics),
            "spark.sql.streaming.checkpointLocation": settings.checkpoint_location,
            
            # Context7 Kafka specific Spark configs
            "spark.jars.packages": "org.apache.spark:spark-sql-kafka-0-10_2.12:3.5.0",
            "spark.sql.streaming.kafka.useDeprecatedOffsetFetching": "false",
            
            # Context7 Performance optimizations
            "spark.serializer": "org.apache.spark.serializer.KryoSerializer",
            "spark.sql.execution.arrow.pyspark.enabled": str(settings.enable_arrow_based_columnar_data_transfers),
            "spark.sql.adaptive.localShuffleReader.enabled": "true",
            "spark.sql.execution.arrow.maxRecordsPerBatch": str(settings.spark_sql_execution_arrow_max_records_per_batch),
            
            # Context7 Memory management
            "spark.executor.memoryOverhead": settings.spark_executor_memory_overhead,
            "spark.driver.maxResultSize": settings.spark_driver_max_result_size,
            "spark.serializer.objectStreamReset": "100",
            "spark.kryoserializer.buffer.max": settings.spark_serializer_buffer_max,
            
            # Context7 Streaming optimizations
            "spark.streaming.backpressure.enabled": str(settings.spark_streaming_backpressure_enabled),
            "spark.streaming.receiver.maxRate": str(settings.spark_streaming_receiver_max_rate),
            "spark.streaming.kafka.maxRatePerPartition": str(settings.spark_streaming_kafka_max_rate_per_partition),
            
            # Context7 RocksDB State Store Optimizations
            "spark.sql.streaming.stateStore.rocksdb.compactOnCommit": str(settings.rocksdb_compact_on_commit),
            "spark.sql.streaming.stateStore.rocksdb.changelogCheckpointing.enabled": str(settings.rocksdb_changelog_checkpointing_enabled),
            "spark.sql.streaming.stateStore.rocksdb.blockSizeKB": str(settings.rocksdb_block_size_kb),
            "spark.sql.streaming.stateStore.rocksdb.blockCacheSizeMB": str(settings.rocksdb_block_cache_size_mb),
            "spark.sql.streaming.stateStore.rocksdb.writeBufferSizeMB": str(settings.rocksdb_write_buffer_size_mb),
            "spark.sql.streaming.stateStore.rocksdb.maxWriteBufferNumber": str(settings.rocksdb_max_write_buffer_number),
            "spark.sql.streaming.stateStore.rocksdb.trackTotalNumberOfRows": str(settings.rocksdb_track_total_number_of_rows),
            
            # Context7 Dynamic allocation
            "spark.dynamicAllocation.enabled": str(settings.dynamic_allocation_enabled),
            "spark.dynamicAllocation.minExecutors": str(settings.dynamic_allocation_min_executors),
            "spark.dynamicAllocation.maxExecutors": str(settings.dynamic_allocation_max_executors),
            "spark.dynamicAllocation.cachedExecutorIdleTimeout": settings.dynamic_allocation_cached_executor_idle_timeout,
            
            # Context7 Speculation
            "spark.speculation": str(settings.enable_speculation),
            "spark.speculation.multiplier": str(settings.speculation_multiplier),
            
            # Context7 Additional performance tuning
            "spark.sql.adaptive.advisoryPartitionSizeInBytes": "128MB",
            "spark.sql.adaptive.coalescePartitions.minPartitionNum": "1",
            "spark.sql.adaptive.coalescePartitions.parallelismFirst": "true",
            "spark.sql.adaptive.skewJoin.skewedPartitionFactor": "5",
            "spark.sql.adaptive.skewJoin.skewedPartitionThresholdInBytes": "256MB",
            
            # Context7 Network optimizations
            "spark.network.timeout": "800s",
            "spark.executor.heartbeatInterval": "20s",
            "spark.rpc.askTimeout": "600s",
            "spark.rpc.lookupTimeout": "120s",
            
            # Context7 Shuffle optimizations
            "spark.shuffle.service.enabled": "true",
            "spark.shuffle.service.port": "7337",
            "spark.shuffle.compress": "true",
            "spark.shuffle.spill.compress": "true",
            "spark.io.compression.codec": "snappy",
        }
        
        # Context7 Kafka Producer optimizations
        kafka_producer_config = {
            "spark.kafka.producer.cache.timeout": str(settings.kafka_producer_cache_timeout_ms),
            "spark.kafka.producer.cache.evictorThreadRunInterval": str(settings.kafka_producer_cache_eviction_interval_ms),
        }
        config.update(kafka_producer_config)
        
        # Override JVM options from environment variables (for container compatibility)
        executor_java_opts = os.getenv("SPARK_EXECUTOR_EXTRA_JAVA_OPTIONS")
        if executor_java_opts is not None:
            config["spark.executor.extraJavaOptions"] = executor_java_opts
            
        driver_java_opts = os.getenv("SPARK_DRIVER_EXTRA_JAVA_OPTIONS")
        if driver_java_opts is not None:
            config["spark.driver.extraJavaOptions"] = driver_java_opts
        
        return config


class PerformanceMonitor:
    """Performance monitoring configuration"""
    
    @staticmethod
    def get_metrics_config(settings: GraphLayerSettings) -> Dict[str, Any]:
        return {
            "enable_metrics": settings.enable_metrics,
            "enable_detailed_metrics": settings.enable_detailed_metrics,
            "latency_percentiles": settings.latency_percentiles,
            "enable_throughput_monitoring": settings.enable_throughput_monitoring,
            "performance_sample_rate": settings.performance_sample_rate,
            "enable_performance_logging": settings.enable_performance_logging
        }


# ARM64/Apple Silicon M3 Pro Optimizations
ARM64_OPTIMIZATIONS = {
    "spark.sql.adaptive.enabled": "true",
    "spark.sql.adaptive.coalescePartitions.enabled": "true",
    "spark.sql.adaptive.coalescePartitions.initialPartitionNum": "8",  # ARM64 optimized
    "spark.sql.adaptive.skewJoin.enabled": "true",
    "spark.serializer": "org.apache.spark.serializer.KryoSerializer",
    "spark.kryo.registrationRequired": "false",
    "spark.sql.execution.arrow.pyspark.enabled": "true",
    "spark.sql.streaming.metricsEnabled": "false",  # Reduce overhead
    "spark.sql.streaming.ui.enabled": "false",      # Disable UI for ARM64
    "spark.task.maxFailures": "1",                  # Fail fast on ARM64
    "spark.speculation": "false",                   # Disable speculation
    "spark.dynamicAllocation.enabled": "false",    # Static allocation for ARM64
}

# Global settings instance
settings = GraphLayerSettings.from_env() 