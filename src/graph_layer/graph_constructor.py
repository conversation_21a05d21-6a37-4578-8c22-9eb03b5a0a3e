"""
Graph Constructor Module - Context7 Performance Optimized

This module contains the core logic for building PyTorch Geometric graphs
from transaction data streams, implementing temporal graph construction
optimized for AML detection with <200ms latency target using Context7 optimizations.
"""

import torch
import numpy as np
import pandas as pd
import networkx as nx
from typing import Dict, List, Tuple, Optional, Any, Set
from dataclasses import dataclass
from datetime import datetime, timezone, timedelta
import json
import logging
import time
from collections import defaultdict, Counter
from functools import lru_cache
import traceback

# PyTorch Geometric imports with Context7 optimizations
from torch_geometric.data import Data
from torch_geometric.utils import to_undirected, remove_self_loops, add_self_loops, to_dense_batch
from torch_geometric.utils.sparse import SparseTensor
import torch_geometric.transforms as T
from torch_geometric.nn.pool import global_add_pool, global_mean_pool

# Context7 Performance imports
try:
    from torch_geometric.utils import scatter
    SCATTER_AVAILABLE = True
except ImportError:
    SCATTER_AVAILABLE = False

logger = logging.getLogger(__name__)


@dataclass
class TransactionNode:
    """Represents a transaction node in the graph - Context7 Optimized"""
    address: str
    total_value: float
    transaction_count: int
    first_seen: int
    last_seen: int
    avg_value: float
    is_contract: bool
    risk_score: float = 0.0
    in_degree: int = 0
    out_degree: int = 0
    unique_counterparties: int = 0
    # Context7 additions
    velocity_score: float = 0.0
    clustering_coefficient: float = 0.0


@dataclass
class TransactionEdge:
    """Represents a transaction edge in the graph - Context7 Optimized"""
    from_address: str
    to_address: str
    total_value: float
    transaction_count: int
    avg_value: float
    first_transaction: int
    last_transaction: int
    risk_score: float = 0.0
    frequency_score: float = 0.0
    # Context7 additions
    temporal_pattern_score: float = 0.0
    value_variance: float = 0.0


@dataclass
class GraphSnapshot:
    """Represents a complete graph snapshot with metadata - Context7 Enhanced"""
    graph_data: Data
    window_start: datetime
    window_end: datetime
    num_nodes: int
    num_edges: int
    max_risk_score: float
    total_volume: float
    metadata: Dict[str, Any]
    construction_time_ms: float
    processing_stats: Dict[str, Any]
    # Context7 additions
    sparsity_ratio: float = 0.0
    graph_density: float = 0.0
    compilation_time_ms: float = 0.0


class Context7PerformanceTracker:
    """Context7-enhanced performance tracking for graph construction"""
    
    def __init__(self):
        self.start_time = None
        self.timings = {}
        self.memory_stats = {}
        self.torch_compile_stats = {}
        
    def start_timer(self, operation: str):
        self.start_time = time.time()
        self.timings[operation] = {"start": self.start_time}
        
        # Context7: Track memory usage
        if torch.cuda.is_available():
            self.memory_stats[operation] = {
                "start_memory": torch.cuda.memory_allocated(),
                "start_cached": torch.cuda.memory_cached()
            }
        
    def end_timer(self, operation: str):
        if operation in self.timings:
            self.timings[operation]["end"] = time.time()
            self.timings[operation]["duration"] = (
                self.timings[operation]["end"] - self.timings[operation]["start"]
            ) * 1000  # Convert to milliseconds
            
            # Context7: Track memory usage
            if torch.cuda.is_available() and operation in self.memory_stats:
                self.memory_stats[operation].update({
                    "end_memory": torch.cuda.memory_allocated(),
                    "end_cached": torch.cuda.memory_cached(),
                    "memory_delta": torch.cuda.memory_allocated() - self.memory_stats[operation]["start_memory"]
                })
            
    def get_timing(self, operation: str) -> float:
        return self.timings.get(operation, {}).get("duration", 0.0)
        
    def get_all_timings(self) -> Dict[str, float]:
        return {op: data.get("duration", 0.0) for op, data in self.timings.items()}
    
    def get_memory_stats(self) -> Dict[str, Any]:
        """Context7: Get memory usage statistics"""
        return self.memory_stats
    
    def record_compilation_time(self, operation: str, time_ms: float):
        """Context7: Record torch.compile times"""
        self.torch_compile_stats[operation] = time_ms


class Context7GraphConstructor:
    """
    Context7-enhanced PyTorch Geometric graph constructor from transaction streams
    
    This class implements Context7 optimizations including torch.compile,
    sparse tensor operations, memory-efficient processing, and enhanced
    vectorization for <200ms latency target.
    """
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.logger = logger
        
        # Context7 Graph construction parameters
        self.max_nodes = config.get('max_nodes_per_graph', 8000)
        self.edge_weight_threshold = config.get('edge_weight_threshold', 0.0005)
        self.include_self_loops = config.get('include_self_loops', False)
        self.node_feature_dim = config.get('node_feature_dim', 64)
        self.include_temporal_features = config.get('include_temporal_features', True)
        self.include_statistical_features = config.get('include_statistical_features', True)
        
        # Context7 Performance optimization settings
        self.enable_vectorized_operations = config.get('enable_vectorized_operations', True)
        self.enable_sparse_tensor_operations = config.get('enable_sparse_tensor_operations', True)
        self.enable_memory_efficient_attention = config.get('enable_memory_efficient_attention', True)
        self.enable_feature_caching = config.get('enable_feature_caching', True)
        self.enable_graph_pruning = config.get('enable_graph_pruning', True)
        self.enable_graph_caching = config.get('enable_graph_caching', True)
        self.enable_incremental_graph_updates = config.get('enable_incremental_graph_updates', True)
        self.enable_graph_compression = config.get('enable_graph_compression', True)
        
        # Context7 torch.compile settings
        self.enable_torch_compile = config.get('enable_torch_compile', True)
        self.torch_compile_mode = config.get('torch_compile_mode', 'default')
        self.torch_compile_fullgraph = config.get('torch_compile_fullgraph', False)
        
        # Context7 Batch processing
        self.batch_processing_chunk_size = config.get('batch_processing_chunk_size', 5000)
        self.batch_size = config.get('batch_size', 3000)
        self.enable_performance_logging = config.get('enable_performance_logging', True)
        
        # Context7 Caching
        self.feature_cache_size = config.get('feature_cache_size', 1000)
        self.graph_cache_size = config.get('graph_cache_size', 500)
        
        # Node address mapping - Context7 optimized with pre-allocation
        self.address_to_index: Dict[str, int] = {}
        self.index_to_address: Dict[int, str] = {}
        
        # Context7 Feature cache for performance
        self._feature_cache: Optional[Dict[str, torch.Tensor]] = {} if self.enable_feature_caching else None
        self._graph_cache: Optional[Dict[str, Data]] = {} if self.enable_graph_caching else None
        
        # Context7 Performance tracker
        self.perf_tracker = Context7PerformanceTracker()
        
        # Context7 Compiled functions
        self._compiled_functions = {}
        self._initialize_compiled_functions()
        
        # Context7 Device optimization
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        
    def _initialize_compiled_functions(self):
        """Context7: Initialize torch.compile optimized functions"""
        if not self.enable_torch_compile:
            return
            
        try:
            # Context7: Compile critical tensor operations
            self._compiled_functions['tensor_creation'] = torch.compile(
                self._create_tensor_optimized,
                mode=self.torch_compile_mode,
                fullgraph=self.torch_compile_fullgraph
            )
            
            self._compiled_functions['feature_computation'] = torch.compile(
                self._compute_features_optimized,
                mode=self.torch_compile_mode,
                fullgraph=self.torch_compile_fullgraph
            )
            
            if self.enable_sparse_tensor_operations:
                self._compiled_functions['sparse_operations'] = torch.compile(
                    self._sparse_tensor_ops,
                    mode=self.torch_compile_mode,
                    fullgraph=self.torch_compile_fullgraph
                )
            
            self.logger.info("Context7: torch.compile functions initialized successfully")
            
        except Exception as e:
            self.logger.warning(f"Context7: torch.compile initialization failed: {e}")
            self.enable_torch_compile = False
    
    def _create_tensor_optimized(self, data: torch.Tensor) -> torch.Tensor:
        """Context7: Optimized tensor creation"""
        return data.to(self.device, non_blocking=True)
    
    def _compute_features_optimized(self, features: torch.Tensor) -> torch.Tensor:
        """Context7: Optimized feature computation"""
        if self.enable_memory_efficient_attention:
            # Context7: Use memory-efficient operations
            return torch.nn.functional.normalize(features, p=2, dim=1)
        return features
    
    def _sparse_tensor_ops(self, edge_index: torch.Tensor, edge_attr: torch.Tensor, 
                          num_nodes: int) -> Optional[SparseTensor]:
        """Context7: Optimized sparse tensor operations"""
        if self.enable_sparse_tensor_operations:
            return SparseTensor(
                row=edge_index[0], 
                col=edge_index[1], 
                value=edge_attr,
                sparse_sizes=(num_nodes, num_nodes)
            )
        return None

    def build_graph_from_transactions(self, transactions_df: pd.DataFrame, 
                                    window_start: datetime, 
                                    window_end: datetime) -> GraphSnapshot:
        """
        Build a PyTorch Geometric graph from transaction DataFrame - Optimized
        
        Args:
            transactions_df: DataFrame containing transaction data
            window_start: Start of the time window
            window_end: End of the time window
            
        Returns:
            GraphSnapshot containing the constructed graph and metadata
        """
        
        total_start_time = time.time()
        self.perf_tracker.start_timer("total_construction")
        
        if self.enable_performance_logging:
            self.logger.info(
                f"Starting optimized graph construction from {len(transactions_df)} transactions, "
                f"window: {window_start} to {window_end}"
            )
        
        # Step 1: Fast preprocessing and validation
        self.perf_tracker.start_timer("preprocessing")
        transactions_df = self._preprocess_transactions(transactions_df)
        self.perf_tracker.end_timer("preprocessing")
        
        # Step 2: Vectorized node and edge extraction
        self.perf_tracker.start_timer("extraction")
        nodes, edges = self._extract_nodes_and_edges_vectorized(transactions_df)
        self.perf_tracker.end_timer("extraction")
        
        # Step 3: Graph pruning for performance
        if self.enable_graph_pruning:
            self.perf_tracker.start_timer("pruning")
            nodes, edges = self._prune_graph(nodes, edges)
            self.perf_tracker.end_timer("pruning")
        
        # Step 4: Optimized address mapping
        self.perf_tracker.start_timer("mapping")
        self._create_address_mapping_optimized(nodes)
        self.perf_tracker.end_timer("mapping")
        
        # Step 5: Fast PyTorch Geometric Data construction
        self.perf_tracker.start_timer("pyg_construction")
        graph_data = self._build_pyg_data_optimized(nodes, edges)
        self.perf_tracker.end_timer("pyg_construction")
        
        # Step 6: Metadata calculation
        self.perf_tracker.start_timer("metadata")
        metadata = self._calculate_metadata_fast(transactions_df, nodes, edges)
        self.perf_tracker.end_timer("metadata")
        
        self.perf_tracker.end_timer("total_construction")
        
        # Calculate total construction time
        total_time_ms = (time.time() - total_start_time) * 1000
        
        # Create performance statistics
        processing_stats = {
            "construction_time_ms": total_time_ms,
            "preprocessing_ms": self.perf_tracker.get_timing("preprocessing"),
            "extraction_ms": self.perf_tracker.get_timing("extraction"),
            "pruning_ms": self.perf_tracker.get_timing("pruning"),
            "mapping_ms": self.perf_tracker.get_timing("mapping"),
            "pyg_construction_ms": self.perf_tracker.get_timing("pyg_construction"),
            "metadata_ms": self.perf_tracker.get_timing("metadata"),
            "transactions_per_ms": len(transactions_df) / max(total_time_ms, 1),
            "nodes_per_ms": len(nodes) / max(total_time_ms, 1),
            "edges_per_ms": len(edges) / max(total_time_ms, 1)
        }
        
        # Step 7: Create optimized graph snapshot
        snapshot = GraphSnapshot(
            graph_data=graph_data,
            window_start=window_start,
            window_end=window_end,
            num_nodes=len(nodes),
            num_edges=len(edges),
            max_risk_score=max([node.risk_score for node in nodes.values()] + [0.0]),
            total_volume=sum([edge.total_value for edge in edges]),
            metadata=metadata,
            construction_time_ms=total_time_ms,
            processing_stats=processing_stats
        )
        
        if self.enable_performance_logging:
            self.logger.info(
                f"Optimized graph construction completed in {total_time_ms:.2f}ms: "
                f"{snapshot.num_nodes} nodes, {snapshot.num_edges} edges, "
                f"max_risk={snapshot.max_risk_score:.3f}, total_volume={snapshot.total_volume:.3f}"
            )
            self.logger.info(f"Processing stats: {processing_stats}")
        
        return snapshot
    
    def _preprocess_transactions(self, df: pd.DataFrame) -> pd.DataFrame:
        """Fast preprocessing and validation of transaction data"""
        
        # Remove null addresses and invalid transactions
        df = df.dropna(subset=['from_address', 'to_address'])
        
        # Convert types efficiently
        if self.enable_vectorized_operations:
            # Use pandas methods properly for type conversion
            df['value'] = pd.to_numeric(df['value'], errors='coerce')
            df['value'] = df['value'].fillna(0)
            df['timestamp'] = pd.to_numeric(df['timestamp'], errors='coerce')
            df['timestamp'] = df['timestamp'].fillna(0)
            df['is_contract'] = df['is_contract'].fillna(False).astype(bool)
        
        # Filter by edge weight threshold early
        if self.edge_weight_threshold > 0:
            df = df[df['value'] >= self.edge_weight_threshold]
        
        # Convert addresses to strings efficiently
        df['from_address'] = df['from_address'].astype(str)
        df['to_address'] = df['to_address'].astype(str)
        
        return df
    
    def _extract_nodes_and_edges_vectorized(self, df: pd.DataFrame) -> Tuple[Dict[str, TransactionNode], List[TransactionEdge]]:
        """Vectorized extraction of nodes and edges from transaction DataFrame"""
        
        if not self.enable_vectorized_operations:
            return self._extract_nodes_and_edges_legacy(df)
        
        # Use vectorized pandas operations for performance
        nodes: Dict[str, TransactionNode] = {}
        
        # Group by from_address for efficient aggregation
        from_groups = df.groupby('from_address').agg({
            'value': ['sum', 'count', 'mean'],
            'timestamp': ['min', 'max'],
            'is_contract': 'first',
            'to_address': 'nunique'
        }).round(6)
        
        from_groups.columns = ['total_value', 'tx_count', 'avg_value', 'first_seen', 'last_seen', 'is_contract', 'unique_counterparties']
        
        # Create from_address nodes
        for addr, row in from_groups.iterrows():
            nodes[addr] = TransactionNode(
                address=addr,
                total_value=float(row['total_value']),
                transaction_count=int(row['tx_count']),
                first_seen=int(row['first_seen']),
                last_seen=int(row['last_seen']),
                avg_value=float(row['avg_value']),
                is_contract=bool(row['is_contract']),
                out_degree=int(row['tx_count']),
                unique_counterparties=int(row['unique_counterparties'])
            )
        
        # Group by to_address for recipient nodes
        to_groups = df.groupby('to_address').agg({
            'value': 'count',
            'timestamp': ['min', 'max'],
            'is_contract': 'first',
            'from_address': 'nunique'
        }).round(6)
        
        to_groups.columns = ['tx_count', 'first_seen', 'last_seen', 'is_contract', 'unique_counterparties']
        
        # Update or create to_address nodes
        for addr, row in to_groups.iterrows():
            if addr in nodes:
                # Update existing node
                nodes[addr].transaction_count += int(row['tx_count'])
                nodes[addr].first_seen = min(nodes[addr].first_seen, int(row['first_seen']))
                nodes[addr].last_seen = max(nodes[addr].last_seen, int(row['last_seen']))
                nodes[addr].in_degree = int(row['tx_count'])
                nodes[addr].unique_counterparties = max(nodes[addr].unique_counterparties, int(row['unique_counterparties']))
            else:
                # Create new recipient node
                nodes[addr] = TransactionNode(
                    address=addr,
                    total_value=0.0,
                    transaction_count=int(row['tx_count']),
                    first_seen=int(row['first_seen']),
                    last_seen=int(row['last_seen']),
                    avg_value=0.0,
                    is_contract=bool(row['is_contract']),
                    in_degree=int(row['tx_count']),
                    unique_counterparties=int(row['unique_counterparties'])
                )
        
        # Vectorized edge aggregation
        edge_groups = df.groupby(['from_address', 'to_address']).agg({
            'value': ['sum', 'count', 'mean'],
            'timestamp': ['min', 'max']
        }).round(6)
        
        edge_groups.columns = ['total_value', 'tx_count', 'avg_value', 'first_tx', 'last_tx']
        
        # Create edges efficiently
        edges = []
        for (from_addr, to_addr), row in edge_groups.iterrows():
            if row['total_value'] >= self.edge_weight_threshold:
                # Calculate frequency score
                time_span = max(row['last_tx'] - row['first_tx'], 1)
                frequency_score = float(row['tx_count']) / time_span if time_span > 0 else float(row['tx_count'])
                
                edges.append(TransactionEdge(
                    from_address=from_addr,
                    to_address=to_addr,
                    total_value=float(row['total_value']),
                    transaction_count=int(row['tx_count']),
                    avg_value=float(row['avg_value']),
                    first_transaction=int(row['first_tx']),
                    last_transaction=int(row['last_tx']),
                    frequency_score=frequency_score
                ))
        
        return nodes, edges
    
    def _extract_nodes_and_edges_legacy(self, df: pd.DataFrame) -> Tuple[Dict[str, TransactionNode], List[TransactionEdge]]:
        """Legacy extraction method for fallback"""
        
        nodes: Dict[str, TransactionNode] = {}
        edge_aggregations: Dict[Tuple[str, str], Dict] = {}
        
        # Process each transaction
        for _, tx in df.iterrows():
            from_addr = str(tx['from_address'])
            to_addr = str(tx['to_address'])
            value = float(tx.get('value', 0) or 0)
            timestamp = int(tx.get('timestamp', 0) or 0)
            is_contract = bool(tx.get('is_contract', False))
            
            # Create or update from_address node
            if from_addr not in nodes:
                nodes[from_addr] = TransactionNode(
                    address=from_addr,
                    total_value=0.0,
                    transaction_count=0,
                    first_seen=timestamp,
                    last_seen=timestamp,
                    avg_value=0.0,
                    is_contract=is_contract
                )
            
            # Update from_address node
            node = nodes[from_addr]
            node.total_value += value
            node.transaction_count += 1
            node.first_seen = min(node.first_seen, timestamp)
            node.last_seen = max(node.last_seen, timestamp)
            node.avg_value = node.total_value / node.transaction_count
            node.out_degree += 1
            
            # Create or update to_address node
            if to_addr not in nodes:
                nodes[to_addr] = TransactionNode(
                    address=to_addr,
                    total_value=0.0,
                    transaction_count=0,
                    first_seen=timestamp,
                    last_seen=timestamp,
                    avg_value=0.0,
                    is_contract=is_contract
                )
            
            # Update to_address node (as recipient)
            to_node = nodes[to_addr]
            to_node.transaction_count += 1
            to_node.first_seen = min(to_node.first_seen, timestamp)
            to_node.last_seen = max(to_node.last_seen, timestamp)
            to_node.in_degree += 1
            
            # Create or update edge
            edge_key = (from_addr, to_addr)
            if edge_key not in edge_aggregations:
                edge_aggregations[edge_key] = {
                    'total_value': 0.0,
                    'transaction_count': 0,
                    'first_transaction': timestamp,
                    'last_transaction': timestamp
                }
            
            edge_data = edge_aggregations[edge_key]
            edge_data['total_value'] += value
            edge_data['transaction_count'] += 1
            edge_data['first_transaction'] = min(edge_data['first_transaction'], timestamp)
            edge_data['last_transaction'] = max(edge_data['last_transaction'], timestamp)
        
        # Convert edge aggregations to TransactionEdge objects
        edges = []
        for (from_addr, to_addr), data in edge_aggregations.items():
            if data['total_value'] >= self.edge_weight_threshold:
                avg_value = data['total_value'] / data['transaction_count']
                time_span = max(data['last_transaction'] - data['first_transaction'], 1)
                frequency_score = data['transaction_count'] / time_span
                
                edges.append(TransactionEdge(
                    from_address=from_addr,
                    to_address=to_addr,
                    total_value=data['total_value'],
                    transaction_count=data['transaction_count'],
                    avg_value=avg_value,
                    first_transaction=data['first_transaction'],
                    last_transaction=data['last_transaction'],
                    frequency_score=frequency_score
                ))
        
        return nodes, edges
    
    def _prune_graph(self, nodes: Dict[str, TransactionNode], edges: List[TransactionEdge]) -> Tuple[Dict[str, TransactionNode], List[TransactionEdge]]:
        """Prune graph to remove low-value nodes and edges for performance"""
        
        if len(nodes) <= self.max_nodes:
            return nodes, edges
        
        # Calculate node importance scores
        node_scores = {}
        for addr, node in nodes.items():
            # Score based on total value, transaction count, and degree
            score = (
                node.total_value * 0.4 +
                node.transaction_count * 0.3 +
                (node.in_degree + node.out_degree) * 0.2 +
                node.risk_score * 0.1
            )
            node_scores[addr] = score
        
        # Keep top nodes by importance
        top_nodes = sorted(node_scores.items(), key=lambda x: x[1], reverse=True)[:self.max_nodes]
        top_addresses = set(addr for addr, _ in top_nodes)
        
        # Filter nodes and edges
        pruned_nodes = {addr: nodes[addr] for addr in top_addresses}
        pruned_edges = [
            edge for edge in edges 
            if edge.from_address in top_addresses and edge.to_address in top_addresses
        ]
        
        if self.enable_performance_logging:
            self.logger.info(
                f"Graph pruned: {len(nodes)} -> {len(pruned_nodes)} nodes, "
                f"{len(edges)} -> {len(pruned_edges)} edges"
            )
        
        return pruned_nodes, pruned_edges
    
    def _create_address_mapping_optimized(self, nodes: Dict[str, TransactionNode]) -> None:
        """Create optimized address to index mapping"""
        
        # Clear existing mappings
        self.address_to_index.clear()
        self.index_to_address.clear()
        
        # Create mappings efficiently
        addresses = list(nodes.keys())
        self.address_to_index = {addr: idx for idx, addr in enumerate(addresses)}
        self.index_to_address = {idx: addr for idx, addr in enumerate(addresses)}
    
    def _build_pyg_data_optimized(self, nodes: Dict[str, TransactionNode], edges: List[TransactionEdge]) -> Data:
        """Build PyTorch Geometric Data object with optimizations"""
        
        if not nodes:
            # Return empty graph
            return Data(
                x=torch.zeros((0, self.node_feature_dim), dtype=torch.float),
                edge_index=torch.zeros((2, 0), dtype=torch.long)
            )
        
        # Build node features efficiently
        node_features = self._build_node_features_vectorized(nodes)
        
        # Build edge index efficiently
        if edges:
            edge_indices = []
            edge_weights = []
            
            for edge in edges:
                if edge.from_address in self.address_to_index and edge.to_address in self.address_to_index:
                    from_idx = self.address_to_index[edge.from_address]
                    to_idx = self.address_to_index[edge.to_address]
                    
                    edge_indices.append([from_idx, to_idx])
                    edge_weights.append(edge.total_value)
            
            if edge_indices:
                edge_index = torch.tensor(edge_indices, dtype=torch.long).t().contiguous()
                edge_attr = torch.tensor(edge_weights, dtype=torch.float).unsqueeze(1)
            else:
                edge_index = torch.zeros((2, 0), dtype=torch.long)
                edge_attr = torch.zeros((0, 1), dtype=torch.float)
        else:
            edge_index = torch.zeros((2, 0), dtype=torch.long)
            edge_attr = torch.zeros((0, 1), dtype=torch.float)
        
        # Create PyTorch Geometric Data object
        data = Data(
            x=node_features,
            edge_index=edge_index,
            edge_attr=edge_attr,
            num_nodes=len(nodes)
        )
        
        # Apply graph transforms if needed
        if not self.include_self_loops:
            data.edge_index, data.edge_attr = remove_self_loops(data.edge_index, data.edge_attr)
        
        return data
    
    def _build_node_features_vectorized(self, nodes: Dict[str, TransactionNode]) -> torch.Tensor:
        """Build node features using vectorized operations"""
        
        if not nodes:
            return torch.zeros((0, self.node_feature_dim), dtype=torch.float)
        
        # Check cache first
        cache_key = f"features_{len(nodes)}_{hash(tuple(sorted(nodes.keys())))}"
        if self.enable_feature_caching and self._feature_cache and cache_key in self._feature_cache:
            return self._feature_cache[cache_key]
        
        # Extract node data into arrays for vectorized operations
        node_list = [nodes[addr] for addr in sorted(nodes.keys())]
        
        # Basic features (vectorized)
        total_values = np.array([node.total_value for node in node_list], dtype=np.float32)
        tx_counts = np.array([node.transaction_count for node in node_list], dtype=np.float32)
        avg_values = np.array([node.avg_value for node in node_list], dtype=np.float32)
        is_contracts = np.array([float(node.is_contract) for node in node_list], dtype=np.float32)
        risk_scores = np.array([node.risk_score for node in node_list], dtype=np.float32)
        in_degrees = np.array([node.in_degree for node in node_list], dtype=np.float32)
        out_degrees = np.array([node.out_degree for node in node_list], dtype=np.float32)
        
        # Prepare feature matrix
        feature_list = [
            total_values,
            tx_counts,
            avg_values,
            is_contracts,
            risk_scores,
            in_degrees,
            out_degrees
        ]
        
        # Add temporal features if enabled
        if self.include_temporal_features:
            first_seen = np.array([node.first_seen for node in node_list], dtype=np.float32)
            last_seen = np.array([node.last_seen for node in node_list], dtype=np.float32)
            time_spans = last_seen - first_seen
            
            feature_list.extend([first_seen, last_seen, time_spans])
        
        # Add statistical features if enabled
        if self.include_statistical_features:
            log_values = np.log1p(total_values)  # log(value + 1)
            log_counts = np.log1p(tx_counts)     # log(count + 1)
            sqrt_avg = np.sqrt(np.maximum(avg_values, 0))  # sqrt(avg_value)
            
            feature_list.extend([log_values, log_counts, sqrt_avg])
        
        # Stack features
        features = np.column_stack(feature_list)
        
        # Pad or truncate to target dimension
        current_dim = features.shape[1]
        if current_dim < self.node_feature_dim:
            # Pad with zeros
            padding = np.zeros((features.shape[0], self.node_feature_dim - current_dim), dtype=np.float32)
            features = np.column_stack([features, padding])
        elif current_dim > self.node_feature_dim:
            # Truncate
            features = features[:, :self.node_feature_dim]
        
        # Convert to PyTorch tensor
        feature_tensor = torch.from_numpy(features).float()
        
        # Cache the result
        if self.enable_feature_caching and self._feature_cache is not None:
            self._feature_cache[cache_key] = feature_tensor
            
            # Limit cache size
            if len(self._feature_cache) > 100:
                # Remove oldest entries
                keys_to_remove = list(self._feature_cache.keys())[:50]
                for key in keys_to_remove:
                    del self._feature_cache[key]
        
        return feature_tensor
    
    def _calculate_metadata_fast(self, df: pd.DataFrame, nodes: Dict[str, TransactionNode], 
                                edges: List[TransactionEdge]) -> Dict[str, Any]:
        """Fast metadata calculation using vectorized operations"""
        
        if self.enable_vectorized_operations and len(df) > 0:
            # Vectorized statistics
            total_volume = float(df['value'].sum())
            avg_tx_value = float(df['value'].mean())
            median_tx_value = float(df['value'].median())
            unique_addresses = len(set(df['from_address'].unique()) | set(df['to_address'].unique()))
            
            # Network statistics
            node_degrees = [node.in_degree + node.out_degree for node in nodes.values()]
            avg_degree = np.mean(node_degrees) if node_degrees else 0
            max_degree = max(node_degrees) if node_degrees else 0
            
            edge_weights = [edge.total_value for edge in edges]
            avg_edge_weight = np.mean(edge_weights) if edge_weights else 0
            max_edge_weight = max(edge_weights) if edge_weights else 0
            
        else:
            # Fallback calculations
            total_volume = sum(float(row.get('value', 0) or 0) for _, row in df.iterrows())
            values = [float(row.get('value', 0) or 0) for _, row in df.iterrows()]
            avg_tx_value = np.mean(values) if values else 0
            median_tx_value = np.median(values) if values else 0
            
            unique_addresses = len(nodes)
            avg_degree = np.mean([node.in_degree + node.out_degree for node in nodes.values()]) if nodes else 0
            max_degree = max([node.in_degree + node.out_degree for node in nodes.values()]) if nodes else 0
            avg_edge_weight = np.mean([edge.total_value for edge in edges]) if edges else 0
            max_edge_weight = max([edge.total_value for edge in edges]) if edges else 0
        
        return {
            "transaction_count": len(df),
            "unique_addresses": unique_addresses,
            "total_volume": total_volume,
            "avg_transaction_value": avg_tx_value,
            "median_transaction_value": median_tx_value,
            "avg_node_degree": avg_degree,
            "max_node_degree": max_degree,
            "avg_edge_weight": avg_edge_weight,
            "max_edge_weight": max_edge_weight,
            "graph_density": len(edges) / max(len(nodes) * (len(nodes) - 1), 1) if len(nodes) > 1 else 0,
            "construction_method": "vectorized" if self.enable_vectorized_operations else "legacy",
            "feature_caching_enabled": self.enable_feature_caching,
            "graph_pruning_enabled": self.enable_graph_pruning
        }

    def serialize_graph_snapshot(self, snapshot: GraphSnapshot) -> str:
        """
        Serialize graph snapshot to JSON string with performance optimizations
        
        Args:
            snapshot: GraphSnapshot to serialize
            
        Returns:
            JSON string representation
        """
        
        # Convert PyTorch tensors to lists for JSON serialization
        graph_dict = {
            "node_features": snapshot.graph_data.x.tolist(),
            "edge_index": snapshot.graph_data.edge_index.tolist(),
            "edge_attr": snapshot.graph_data.edge_attr.tolist() if snapshot.graph_data.edge_attr is not None else None,
            "num_nodes": snapshot.graph_data.num_nodes
        }
        
        # Create serializable snapshot
        serializable_snapshot = {
            "graph_data": graph_dict,
            "window_start": snapshot.window_start.isoformat(),
            "window_end": snapshot.window_end.isoformat(),
            "num_nodes": snapshot.num_nodes,
            "num_edges": snapshot.num_edges,
            "max_risk_score": snapshot.max_risk_score,
            "total_volume": snapshot.total_volume,
            "construction_time_ms": snapshot.construction_time_ms,
            "processing_stats": snapshot.processing_stats,
            "metadata": snapshot.metadata,
            "address_mapping": self.index_to_address
        }
        
        return json.dumps(serializable_snapshot, default=str)
    
    @classmethod
    def deserialize_graph_snapshot(cls, json_str: str) -> Tuple[GraphSnapshot, Dict[str, int]]:
        """
        Deserialize graph snapshot from JSON string
        
        Args:
            json_str: JSON string representation
            
        Returns:
            Tuple of (GraphSnapshot, address_to_index_mapping)
        """
        
        data = json.loads(json_str)
        
        # Reconstruct PyTorch tensors
        graph_data = Data(
            x=torch.tensor(data["graph_data"]["node_features"], dtype=torch.float),
            edge_index=torch.tensor(data["graph_data"]["edge_index"], dtype=torch.long),
            edge_attr=torch.tensor(data["graph_data"]["edge_attr"], dtype=torch.float) if data["graph_data"]["edge_attr"] else None,
            num_nodes=data["graph_data"]["num_nodes"]
        )
        
        # Reconstruct snapshot
        snapshot = GraphSnapshot(
            graph_data=graph_data,
            window_start=datetime.fromisoformat(data["window_start"]),
            window_end=datetime.fromisoformat(data["window_end"]),
            num_nodes=data["num_nodes"],
            num_edges=data["num_edges"],
            max_risk_score=data["max_risk_score"],
            total_volume=data["total_volume"],
            construction_time_ms=data.get("construction_time_ms", 0.0),
            processing_stats=data.get("processing_stats", {}),
            metadata=data["metadata"]
        )
        
        # Reconstruct address mapping
        index_to_address = {int(k): v for k, v in data["address_mapping"].items()}
        address_to_index = {v: int(k) for k, v in data["address_mapping"].items()}
        
        return snapshot, address_to_index 

    def create_empty_graph(self):
        """Create an empty graph when no transactions are available."""
        try:
            # Create empty tensors for PyG Data object
            x = torch.zeros((0, self.node_feature_dim), dtype=torch.float)
            edge_index = torch.zeros((2, 0), dtype=torch.long)
            
            # Create an empty Data object
            data = Data(x=x, edge_index=edge_index)
            
            # Create an empty GraphSnapshot
            current_time = datetime.now()
            window_start = current_time - timedelta(minutes=15)
            window_end = current_time
            
            # Import locally to avoid circular imports
            from graph_types import GraphSnapshot
            
            logger.info("Creating empty graph due to no transactions")
            
            snapshot = GraphSnapshot(
                graph_data=data,
                window_start=window_start,
                window_end=window_end,
                num_nodes=0,
                num_edges=0,
                max_risk_score=0.0,
                total_volume=0.0,
                metadata={
                    'num_nodes': 0,
                    'num_edges': 0,
                    'total_value': 0.0,
                    'is_empty': True,
                    'empty_reason': 'no_transactions',
                    'created_at': datetime.now().isoformat()
                },
                construction_time_ms=0.0,
                processing_stats={
                    'empty_graph': True,
                    'timestamp': int(time.time() * 1000)
                },
                sparsity_ratio=0.0,
                graph_density=0.0,
                compilation_time_ms=0.0
            )
            
            logger.info(f"Successfully created empty graph snapshot with window {window_start} to {window_end}")
            return snapshot
        except Exception as e:
            logger.error(f"Error creating empty graph: {str(e)}")
            logger.error(traceback.format_exc())
            
            # Fallback to a minimal graph if there's an error
            try:
                # Import locally to avoid circular imports
                from graph_types import GraphSnapshot
                
                # Create minimal data
                data = Data(
                    x=torch.zeros((0, 1), dtype=torch.float),
                    edge_index=torch.zeros((2, 0), dtype=torch.long)
                )
                
                # Create minimal snapshot
                minimal_snapshot = GraphSnapshot(
                    graph_data=data,
                    window_start=datetime.now(),
                    window_end=datetime.now(),
                    num_nodes=0,
                    num_edges=0,
                    max_risk_score=0.0,
                    total_volume=0.0,
                    metadata={'error': str(e), 'is_empty': True, 'is_fallback': True},
                    construction_time_ms=0.0,
                    processing_stats={},
                    sparsity_ratio=0.0,
                    graph_density=0.0,
                    compilation_time_ms=0.0
                )
                
                logger.warning("Returning fallback empty graph due to error")
                return minimal_snapshot
            except Exception as fallback_error:
                logger.critical(f"Critical error creating fallback empty graph: {str(fallback_error)}")
                raise 