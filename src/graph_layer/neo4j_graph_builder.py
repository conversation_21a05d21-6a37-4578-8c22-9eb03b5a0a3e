#!/usr/bin/env python3
"""
Neo4j-Integrated Graph Construction Layer
Hybrid Architecture: Spark for Detection + Neo4j for Graph Persistence

This implements the pattern:
1. Spark Structured Streaming = Ingestion + Detection + Scoring
2. Neo4j = Graph Construction + Persistence + Analysis
"""

import os
import sys
import json
import logging
import traceback
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional
from dataclasses import dataclass

import pandas as pd
from pyspark.sql import SparkSession
from pyspark.sql.functions import col, from_json, expr, when, lit
from pyspark.sql.types import StructType, StructField, StringType, DoubleType, LongType, BooleanType, ArrayType

from neo4j import GraphDatabase
from kafka import KafkaProducer

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[logging.StreamHandler(sys.stdout)]
)
logger = logging.getLogger(__name__)

@dataclass
class SuspiciousTransaction:
    """Represents a transaction flagged as suspicious by Spark"""
    hash: str
    from_address: str
    to_address: str
    value_eth: float
    timestamp: datetime
    risk_score: float
    risk_factors: List[str]
    block_number: int
    gas_used: int
    is_contract: bool

class Neo4jGraphBuilder:
    """Neo4j graph builder for suspicious transactions"""
    
    def __init__(self, uri: str, user: str, password: str):
        self.driver = GraphDatabase.driver(uri, auth=(user, password))
        self.setup_constraints()
    
    def setup_constraints(self):
        """Set up Neo4j constraints and indexes"""
        with self.driver.session() as session:
            # Create constraints
            session.run("CREATE CONSTRAINT account_address IF NOT EXISTS FOR (a:Account) REQUIRE a.address IS UNIQUE")
            session.run("CREATE CONSTRAINT transaction_hash IF NOT EXISTS FOR (t:Transaction) REQUIRE t.hash IS UNIQUE")
            
            # Create indexes for performance
            session.run("CREATE INDEX account_risk_score IF NOT EXISTS FOR (a:Account) ON (a.risk_score)")
            session.run("CREATE INDEX transaction_timestamp IF NOT EXISTS FOR (t:Transaction) ON (t.timestamp)")
            session.run("CREATE INDEX transaction_risk_score IF NOT EXISTS FOR (t:Transaction) ON (t.risk_score)")
    
    def store_suspicious_transaction(self, tx: SuspiciousTransaction):
        """Store a suspicious transaction in Neo4j graph"""
        with self.driver.session() as session:
            # Create/update accounts and transaction
            query = """
            MERGE (from_account:Account {address: $from_address})
            MERGE (to_account:Account {address: $to_address})
            
            // Update account risk scores (simple aggregation)
            SET from_account.last_activity = datetime($timestamp),
                from_account.total_sent = COALESCE(from_account.total_sent, 0) + $value_eth,
                from_account.risk_score = COALESCE(from_account.risk_score, 0) + $risk_score * 0.1
            
            SET to_account.last_activity = datetime($timestamp),
                to_account.total_received = COALESCE(to_account.total_received, 0) + $value_eth,
                to_account.risk_score = COALESCE(to_account.risk_score, 0) + $risk_score * 0.05
            
            // Create transaction relationship
            MERGE (from_account)-[transfer:TRANSFER {hash: $hash}]->(to_account)
            SET transfer.amount = $value_eth,
                transfer.timestamp = datetime($timestamp),
                transfer.risk_score = $risk_score,
                transfer.risk_factors = $risk_factors,
                transfer.block_number = $block_number,
                transfer.gas_used = $gas_used,
                transfer.is_contract = $is_contract,
                transfer.created_at = datetime()
            
            RETURN transfer.hash as transaction_hash
            """
            
            result = session.run(query, {
                'from_address': tx.from_address,
                'to_address': tx.to_address,
                'hash': tx.hash,
                'value_eth': tx.value_eth,
                'timestamp': tx.timestamp.isoformat(),
                'risk_score': tx.risk_score,
                'risk_factors': tx.risk_factors,
                'block_number': tx.block_number,
                'gas_used': tx.gas_used,
                'is_contract': tx.is_contract
            })
            
            return result.single()
    
    def close(self):
        """Close Neo4j connection"""
        self.driver.close()

class HybridGraphConstructor:
    """
    Hybrid Graph Constructor implementing:
    Spark = High-throughput detection + scoring
    Neo4j = Graph persistence + analysis
    """
    
    def __init__(self):
        self.spark = self._create_spark_session()
        self.neo4j_builder = Neo4jGraphBuilder(
            uri=os.getenv('NEO4J_URI', 'bolt://localhost:7687'),
            user=os.getenv('NEO4J_USER', 'neo4j'),
            password=os.getenv('NEO4J_PASSWORD', 'password123')
        )
        self.kafka_producer = KafkaProducer(
            bootstrap_servers=os.getenv('KAFKA_BOOTSTRAP_SERVERS', 'localhost:9092'),
            value_serializer=lambda v: json.dumps(v).encode('utf-8')
        )
    
    def _create_spark_session(self) -> SparkSession:
        """Create optimized Spark session for streaming"""
        return SparkSession.builder \
            .appName("HybridGraphConstructor") \
            .config("spark.sql.shuffle.partitions", "10") \
            .config("spark.streaming.kafka.maxRatePerPartition", "100") \
            .config("spark.streaming.backpressure.enabled", "true") \
            .getOrCreate()
    
    def _define_alert_schema(self) -> StructType:
        """Define schema for CEP alerts"""
        transaction_schema = StructType([
            StructField("hash", StringType(), True),
            StructField("fromAddress", StringType(), True),
            StructField("toAddress", StringType(), True),
            StructField("value", StringType(), True),
            StructField("blockNumber", LongType(), True),
            StructField("timestamp", LongType(), True),
            StructField("gasUsed", LongType(), True),
            StructField("valueEth", DoubleType(), True),
            StructField("riskLevel", StringType(), True),
            StructField("contract", BooleanType(), True)
        ])
        
        return StructType([
            StructField("alertId", StringType(), True),
            StructField("patternName", StringType(), True),
            StructField("severity", StringType(), True),
            StructField("involvedTransactions", ArrayType(transaction_schema), True),
            StructField("totalValue", DoubleType(), True),
            StructField("riskScore", StringType(), True)
        ])
    
    def _calculate_risk_score(self, alert_data: Dict[str, Any]) -> float:
        """Calculate numerical risk score from alert data"""
        base_score = 0.5
        
        # Risk factors
        severity_multiplier = {
            'HIGH': 1.0,
            'MEDIUM': 0.7,
            'LOW': 0.4
        }.get(alert_data.get('severity', 'LOW'), 0.4)
        
        pattern_multiplier = {
            'HIGH_VALUE_PATTERN': 0.9,
            'RAPID_FIRE_PATTERN': 0.8,
            'ROUND_AMOUNT_PATTERN': 0.6
        }.get(alert_data.get('patternName', ''), 0.5)
        
        value_multiplier = min(alert_data.get('totalValue', 0) / 10.0, 1.0)
        
        return min(base_score * severity_multiplier * pattern_multiplier + value_multiplier, 1.0)
    
    def _extract_risk_factors(self, alert_data: Dict[str, Any]) -> List[str]:
        """Extract risk factors from alert"""
        factors = []
        
        if alert_data.get('severity') == 'HIGH':
            factors.append('high_severity')
        
        if alert_data.get('totalValue', 0) > 5.0:
            factors.append('high_value')
        
        pattern = alert_data.get('patternName', '')
        if 'HIGH_VALUE' in pattern:
            factors.append('suspicious_amount')
        if 'RAPID_FIRE' in pattern:
            factors.append('rapid_transactions')
        if 'ROUND_AMOUNT' in pattern:
            factors.append('round_amount')
        
        return factors
    
    def process_batch(self, batch_df, batch_id):
        """Process batch of CEP alerts - Spark detection + Neo4j storage"""
        try:
            if batch_df.isEmpty():
                logger.info(f"Batch {batch_id} is empty")
                return
            
            logger.info(f"Processing batch {batch_id} with {batch_df.count()} alerts")
            
            # Convert to pandas for easier processing
            pandas_df = batch_df.toPandas()
            
            suspicious_transactions = []
            
            # Process each alert
            for _, alert in pandas_df.iterrows():
                try:
                    # Calculate risk score
                    risk_score = self._calculate_risk_score(alert.to_dict())
                    
                    # Only process high-risk transactions (threshold filtering)
                    if risk_score < 0.6:
                        continue
                    
                    risk_factors = self._extract_risk_factors(alert.to_dict())
                    
                    # Extract transactions from alert
                    involved_transactions = alert.get('involvedTransactions', [])
                    
                    for tx in involved_transactions:
                        suspicious_tx = SuspiciousTransaction(
                            hash=tx.hash,
                            from_address=tx.fromAddress,
                            to_address=tx.toAddress,
                            value_eth=tx.valueEth,
                            timestamp=datetime.fromtimestamp(tx.timestamp / 1000),
                            risk_score=risk_score,
                            risk_factors=risk_factors,
                            block_number=tx.blockNumber,
                            gas_used=tx.gasUsed,
                            is_contract=tx.contract
                        )
                        
                        suspicious_transactions.append(suspicious_tx)
                        
                        # Store in Neo4j
                        self.neo4j_builder.store_suspicious_transaction(suspicious_tx)
                        
                        logger.info(f"Stored suspicious transaction {tx.hash} with risk score {risk_score:.3f}")
                
                except Exception as e:
                    logger.error(f"Error processing alert: {str(e)}")
                    continue
            
            # Send summary to Kafka for monitoring
            summary = {
                'batch_id': batch_id,
                'timestamp': datetime.now().isoformat(),
                'total_alerts': len(pandas_df),
                'suspicious_transactions': len(suspicious_transactions),
                'avg_risk_score': sum(tx.risk_score for tx in suspicious_transactions) / len(suspicious_transactions) if suspicious_transactions else 0
            }
            
            self.kafka_producer.send('graph-construction-summary', summary)
            logger.info(f"Batch {batch_id}: Processed {len(suspicious_transactions)} suspicious transactions")
            
        except Exception as e:
            logger.error(f"Error processing batch {batch_id}: {str(e)}")
            logger.error(traceback.format_exc())
    
    def start_streaming(self):
        """Start the hybrid streaming pipeline"""
        try:
            # Read from filtered-transactions topic (CEP output)
            streaming_df = self.spark.readStream \
                .format("kafka") \
                .option("kafka.bootstrap.servers", os.getenv('KAFKA_BOOTSTRAP_SERVERS', 'localhost:9092')) \
                .option("subscribe", os.getenv('KAFKA_INPUT_TOPIC', 'filtered-transactions')) \
                .option("startingOffsets", "latest") \
                .option("failOnDataLoss", "false") \
                .load()
            
            # Parse JSON alerts
            alert_schema = self._define_alert_schema()
            parsed_df = streaming_df.selectExpr("CAST(value AS STRING) as json_str") \
                .select(from_json("json_str", alert_schema).alias("data")) \
                .select("data.*")
            
            # Start streaming query
            query = parsed_df.writeStream \
                .foreachBatch(self.process_batch) \
                .option("checkpointLocation", "/tmp/hybrid-graph-checkpoints") \
                .trigger(processingTime='10 seconds') \
                .start()
            
            logger.info("Started hybrid graph construction pipeline")
            query.awaitTermination()
            
        except Exception as e:
            logger.error(f"Error in streaming pipeline: {str(e)}")
            raise
    
    def shutdown(self):
        """Graceful shutdown"""
        self.neo4j_builder.close()
        self.kafka_producer.close()
        self.spark.stop()

def main():
    """Main entry point"""
    constructor = HybridGraphConstructor()
    
    try:
        constructor.start_streaming()
    except KeyboardInterrupt:
        logger.info("Received shutdown signal")
    finally:
        constructor.shutdown()

if __name__ == "__main__":
    main()
