# Simplified Graph Construction Layer Dockerfile with Kafka Fixes
FROM python:3.9-slim

# Set environment variables
ENV PYTHONUNBUFFERED=1
ENV SPARK_HOME=/opt/spark
ENV PATH=$PATH:$SPARK_HOME/bin:$SPARK_HOME/sbin
ENV PYSPARK_PYTHON=python3
ENV PYSPARK_DRIVER_PYTHON=python3
ENV PYTHONPATH=/app

# Critical Kafka fix environment variables
ENV KAFKA_STARTING_OFFSETS=earliest
ENV KAFKA_AUTO_OFFSET_RESET=earliest
ENV KAFKA_CONSUMER_GROUP_ID=graph-construction-group-1
ENV KAFKA_INPUT_TOPIC=filtered-transactions
ENV KAFKA_OUTPUT_TOPIC=graph-snapshots
ENV KAFKA_BOOTSTRAP_SERVERS=kafka:29092

# Install system dependencies including gcc for Python package compilation
RUN apt-get update && apt-get install -y \
    default-jdk \
    wget \
    curl \
    procps \
    gcc \
    g++ \
    python3-dev \
    build-essential \
    && rm -rf /var/lib/apt/lists/*

# Find and set Java home correctly
RUN java_path=$(dirname $(dirname $(readlink -f $(which java)))) \
    && echo "export JAVA_HOME=$java_path" >> /etc/profile \
    && echo "export JAVA_HOME=$java_path" >> /root/.bashrc

# Set Java environment variables
ENV JAVA_HOME=/usr/lib/jvm/default-java
ENV PATH=$PATH:$JAVA_HOME/bin

# Install Apache Spark
RUN wget -q https://archive.apache.org/dist/spark/spark-3.5.0/spark-3.5.0-bin-hadoop3.tgz \
    && tar -xzf spark-3.5.0-bin-hadoop3.tgz \
    && mv spark-3.5.0-bin-hadoop3 /opt/spark \
    && rm spark-3.5.0-bin-hadoop3.tgz

# Install Kafka connector for Spark and other necessary JARs
RUN wget -q https://repo1.maven.org/maven2/org/apache/spark/spark-sql-kafka-0-10_2.12/3.5.0/spark-sql-kafka-0-10_2.12-3.5.0.jar \
    -O /opt/spark/jars/spark-sql-kafka-0-10_2.12-3.5.0.jar \
    && wget -q https://repo1.maven.org/maven2/org/apache/kafka/kafka-clients/3.3.1/kafka-clients-3.3.1.jar \
    -O /opt/spark/jars/kafka-clients-3.3.1.jar \
    && wget -q https://repo1.maven.org/maven2/org/apache/commons/commons-pool2/2.11.1/commons-pool2-2.11.1.jar \
    -O /opt/spark/jars/commons-pool2-2.11.1.jar \
    && wget -q https://repo1.maven.org/maven2/org/apache/kafka/kafka_2.12/3.3.1/kafka_2.12-3.3.1.jar \
    -O /opt/spark/jars/kafka_2.12-3.3.1.jar \
    && wget -q https://repo1.maven.org/maven2/org/apache/kafka/kafka-metadata/3.3.1/kafka-metadata-3.3.1.jar \
    -O /opt/spark/jars/kafka-metadata-3.3.1.jar \
    && wget -q https://repo1.maven.org/maven2/org/apache/spark/spark-token-provider-kafka-0-10_2.12/3.5.0/spark-token-provider-kafka-0-10_2.12-3.5.0.jar \
    -O /opt/spark/jars/spark-token-provider-kafka-0-10_2.12-3.5.0.jar \
    && wget -q https://repo1.maven.org/maven2/org/apache/kafka/connect-json/3.3.1/connect-json-3.3.1.jar \
    -O /opt/spark/jars/connect-json-3.3.1.jar \
    && wget -q https://repo1.maven.org/maven2/org/apache/kafka/connect-api/3.3.1/connect-api-3.3.1.jar \
    -O /opt/spark/jars/connect-api-3.3.1.jar

# Set working directory
WORKDIR /app

# Copy requirements first for better caching
COPY requirements.txt .

# Install core dependencies first
RUN pip install --no-cache-dir pyspark==3.5.0 kafka-python==2.0.2 pandas==2.0.3 numpy==1.24.3 pydantic==2.0.3

# Install PyTorch
RUN pip install --no-cache-dir torch==2.0.1 torchvision==0.15.2

# Install PyTorch Geometric
RUN pip install --no-cache-dir torch-geometric torch-scatter torch-sparse

# Install other dependencies
RUN pip install --no-cache-dir python-dotenv==1.0.0 prometheus-client==0.17.1 requests==2.31.0 tqdm==4.65.0

# Install dependencies that require compilation
RUN pip install --no-cache-dir psutil==5.9.5 pyarrow==12.0.1

# Copy application code
COPY main.py .
COPY graph_constructor.py .
COPY graph_types.py .
COPY config ./config

# Create directories for outputs and checkpoints
RUN mkdir -p /tmp/graph-outputs /tmp/graph-layer-checkpoints-new

# Configure Spark
ENV SPARK_CONF_DIR=/opt/spark/conf
RUN echo "spark.sql.adaptive.enabled=true" > /opt/spark/conf/spark-defaults.conf
RUN echo "spark.driver.extraClassPath=/opt/spark/jars/*" >> /opt/spark/conf/spark-defaults.conf
RUN echo "spark.executor.extraClassPath=/opt/spark/jars/*" >> /opt/spark/conf/spark-defaults.conf

# Create startup script with Kafka fixes
RUN echo '#!/bin/bash\n\
echo "Starting Fixed Graph Construction Layer..."\n\
echo "Kafka Fixes: startingOffsets=earliest, group.id explicitly set"\n\
\n\
# Check Java installation\n\
echo "Java version:"\n\
java -version\n\
echo "JAVA_HOME: $JAVA_HOME"\n\
ls -la $JAVA_HOME/bin\n\
\n\
# Update spark-env.sh with correct JAVA_HOME\n\
echo "export JAVA_HOME=$JAVA_HOME" > $SPARK_HOME/conf/spark-env.sh\n\
echo "export PATH=$PATH:$JAVA_HOME/bin" >> $SPARK_HOME/conf/spark-env.sh\n\
chmod +x $SPARK_HOME/conf/spark-env.sh\n\
\n\
# Check Spark jars directory for Kafka dependencies\n\
echo "Checking Kafka dependencies:"\n\
ls -la $SPARK_HOME/jars/kafka*\n\
ls -la $SPARK_HOME/jars/spark-sql-kafka*\n\
\n\
# Check Python packages\n\
echo "Checking Python packages:"\n\
pip list | grep -E "torch|pyspark|kafka|pandas"\n\
\n\
# List all files in the app directory\n\
echo "Files in app directory:"\n\
ls -la /app\n\
\n\
# Start the application\n\
exec python3 main.py\n\
' > /app/start.sh && chmod +x /app/start.sh

# Expose Spark UI port
EXPOSE 4040

# Default command
CMD ["/app/start.sh"] 