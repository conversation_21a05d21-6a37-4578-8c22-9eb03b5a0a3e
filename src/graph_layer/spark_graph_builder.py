"""
Spark Graph Builder - Main Streaming Application - Performance Optimized

This module implements the core Spark Structured Streaming application
that consumes filtered transactions from Kafka and constructs PyTorch Geometric
graphs using sliding time windows, optimized for <500ms latency target.
"""

import sys
import os
import json
import logging
import time
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional
import pandas as pd
import numpy as np

# Add the parent directory to the path to import config
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from pyspark.sql import SparkSession, DataFrame
import pyspark.sql.functions as F
from pyspark.sql.functions import (
    col, from_json, to_json, struct, window, 
    collect_list, first, last, count, sum as spark_sum,
    avg, max as spark_max, min as spark_min,
    current_timestamp, unix_timestamp, from_unixtime, explode,
    when, isnan, isnull, size, lit, to_timestamp
)
from pyspark.sql.types import (
    <PERSON>ructType, StructField, StringType, DoubleType, 
    LongType, BooleanType, TimestampType, ArrayType
)
from pyspark.sql.streaming.query import StreamingQuery

# Local imports
from config.settings import GraphLayerSettings, KafkaConfig, SparkConfig, PerformanceMonitor
from graph_constructor import Context7GraphConstructor, GraphSnapshot


# Configure logging
logging.basicConfig(
    level=logging.INFO,  # Default to INFO until settings are loaded
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class PerformanceMetrics:
    """Performance metrics tracking for streaming application"""
    
    def __init__(self):
        self.batch_metrics = []
        self.processing_times = []
        self.throughput_history = []
        self.latency_history = []
        
    def record_batch_metrics(self, batch_id: int, processing_time_ms: float, 
                           num_records: int, graph_construction_time_ms: float):
        """Record metrics for a batch"""
        
        throughput = num_records / max(processing_time_ms / 1000, 0.001)  # records/second
        
        metrics = {
            "batch_id": batch_id,
            "processing_time_ms": processing_time_ms,
            "graph_construction_time_ms": graph_construction_time_ms,
            "num_records": num_records,
            "throughput_rps": throughput,
            "timestamp": datetime.now().isoformat()
        }
        
        self.batch_metrics.append(metrics)
        self.processing_times.append(processing_time_ms)
        self.throughput_history.append(throughput)
        
        # Keep only recent metrics (last 100 batches)
        if len(self.batch_metrics) > 100:
            self.batch_metrics = self.batch_metrics[-100:]
            self.processing_times = self.processing_times[-100:]
            self.throughput_history = self.throughput_history[-100:]
    
    def get_performance_summary(self) -> Dict[str, Any]:
        """Get performance summary statistics"""
        
        if not self.processing_times:
            return {}
        
        return {
            "avg_processing_time_ms": np.mean(self.processing_times),
            "p95_processing_time_ms": np.percentile(self.processing_times, 95),
            "p99_processing_time_ms": np.percentile(self.processing_times, 99),
            "max_processing_time_ms": np.max(self.processing_times),
            "avg_throughput_rps": np.mean(self.throughput_history),
            "max_throughput_rps": np.max(self.throughput_history),
            "total_batches": len(self.batch_metrics),
            "batches_under_500ms": sum(1 for t in self.processing_times if t < 500),
            "performance_target_achievement": sum(1 for t in self.processing_times if t < 500) / len(self.processing_times) * 100
        }


class SparkGraphBuilder:
    """
    Spark Structured Streaming application for building graphs from transaction streams - Performance Optimized
    
    This class implements optimized streaming logic using Spark Structured Streaming
    to consume filtered transactions from Kafka, apply sliding time windows,
    and construct PyTorch Geometric graphs with <500ms latency target.
    """
    
    # Class-level logger initialization
    logger = logging.getLogger(__name__)
    logger.setLevel(logging.INFO)
    
    def __init__(self):
        """Initialize SparkGraphBuilder with optimized settings"""
        
        # Load settings from environment
        self.settings = GraphLayerSettings.from_env()
        
        # Initialize performance metrics
        self.performance_metrics = PerformanceMetrics()
        self.enable_detailed_metrics = self.settings.enable_detailed_metrics
        
        # Initialize components
        self.spark = None
        self.graph_constructor = None
        
        # Debug: Print environment variables
        self.logger.info(f"🔍 DEBUG: Environment Variables")
        self.logger.info(f"   CHECKPOINT_LOCATION env: '{os.getenv('CHECKPOINT_LOCATION', 'NOT_SET')}'")
        self.logger.info(f"   CHECKPOINT_LOCATION setting: '{self.settings.checkpoint_location}'")
        self.logger.info(f"   CHECKPOINT_LOCATION stripped: '{self.settings.checkpoint_location.strip() if self.settings.checkpoint_location else 'None'}'")
        
        # Initialize Spark session
        self._init_spark_session()
        
        # Initialize graph constructor
        self._init_graph_constructor()
        
    def _init_spark_session(self):
        """Initialize Spark session with ARM64-optimized configuration"""
        
        self.logger.info("🚀 Initializing ARM64-optimized Spark session for Graph Construction Layer")
        
        # Use the new ARM64-optimized SparkConfig - local import
        from spark_config import SparkConfig
        self.spark = SparkConfig.create_spark_session()
        
        # Additional ARM64-specific optimizations
        if self.spark:
            # Log key performance configurations
            if self.enable_detailed_metrics:
                self.logger.info(f"✅ ARM64-optimized Spark session active")
                self.logger.info(f"   Executor memory: {self.settings.spark_executor_memory}")
                self.logger.info(f"   Driver memory: {self.settings.spark_driver_memory}")
                self.logger.info(f"   Max offsets per trigger: {self.settings.max_offsets_per_trigger}")
                self.logger.info(f"   Trigger processing time: {self.settings.trigger_processing_time}")
                self.logger.info(f"   Window duration: {self.settings.window_duration}")
                self.logger.info(f"   Sliding duration: {self.settings.sliding_duration}")
    
    def _init_graph_constructor(self):
        """Initialize graph constructor with optimized settings"""
        
        config = {
            'max_nodes_per_graph': self.settings.max_nodes_per_graph,
            'edge_weight_threshold': self.settings.edge_weight_threshold,
            'include_self_loops': self.settings.include_self_loops,
            'node_feature_dim': self.settings.node_feature_dim,
            'include_temporal_features': self.settings.include_temporal_features,
            'include_statistical_features': self.settings.include_statistical_features,
            'enable_vectorized_operations': self.settings.enable_vectorized_operations,
            'enable_feature_caching': self.settings.enable_feature_caching,
            'enable_graph_pruning': self.settings.enable_graph_pruning,
            'batch_size': self.settings.batch_size,
            'enable_performance_logging': self.settings.enable_performance_logging
        }
        
        self.graph_constructor = Context7GraphConstructor(config)
        self.logger.info("Optimized graph constructor initialized")
    
    def _get_alert_schema(self) -> StructType:
        """Define the schema for incoming alert data from CEP layer"""
        
        # Schema for individual transactions within alerts
        transaction_schema = StructType([
            StructField("hash", StringType(), True),
            StructField("fromAddress", StringType(), True),
            StructField("toAddress", StringType(), True),
            StructField("value", LongType(), True),
            StructField("blockNumber", LongType(), True),
            StructField("timestamp", LongType(), True),
            StructField("gasUsed", LongType(), True),
            StructField("gasPrice", LongType(), True),
            StructField("methodId", StringType(), True),
            StructField("valueEth", DoubleType(), True),
            StructField("riskLevel", StringType(), True),
            StructField("contract", BooleanType(), True),
            StructField("roundAmount", BooleanType(), True),
            StructField("highValue", BooleanType(), True),
            StructField("microAmount", BooleanType(), True)
        ])
        
        # Schema for alert objects from CEP
        return StructType([
            StructField("alertId", StringType(), True),
            StructField("patternName", StringType(), True),
            StructField("alertType", StringType(), True),
            StructField("severity", StringType(), True),
            StructField("description", StringType(), True),
            StructField("detectionTimestamp", LongType(), True),
            StructField("involvedTransactions", ArrayType(transaction_schema), True),
            StructField("primaryAddress", StringType(), True),
            StructField("totalValue", DoubleType(), True),
            StructField("riskScore", StringType(), True),
            StructField("reason", StringType(), True)
        ])
    
    def _create_kafka_source(self) -> DataFrame:
        """Create optimized Kafka source DataFrame for reading transaction stream"""
        
        if not self.spark:
            raise RuntimeError("Spark session not initialized")
        
        self.logger.info(f"Creating optimized Kafka source for topic: {self.settings.kafka_input_topic}")
        self.logger.info(f"Using bootstrap servers: {self.settings.kafka_bootstrap_servers}")
        self.logger.info(f"Using consumer group: {self.settings.kafka_consumer_group}")
        
        # Get Kafka options with explicit consumer group ID
        kafka_options = {
            "kafka.bootstrap.servers": self.settings.kafka_bootstrap_servers,
            "subscribe": self.settings.kafka_input_topic,
            "startingOffsets": os.getenv("KAFKA_STARTING_OFFSETS", "earliest"),  # Changed from hard-coded "latest" to env var with "earliest" default
            "maxOffsetsPerTrigger": str(self.settings.max_offsets_per_trigger),
            "failOnDataLoss": "false",
            "kafkaConsumer.pollTimeoutMs": "120000",
            "group.id": self.settings.kafka_consumer_group,  # Explicitly set consumer group
            
            # Additional options for consumer group stability
            "kafka.group.id": self.settings.kafka_consumer_group,  # Alternative format
            "groupIdPrefix": self.settings.kafka_consumer_group,   # For Spark Streaming
            
            # Performance optimizations
            "fetch.min.bytes": "10000",
            "fetch.max.wait.ms": "500",
            "max.partition.fetch.bytes": "10485760",  # 10MB
            "session.timeout.ms": "30000",
            "heartbeat.interval.ms": "10000",
            "auto.offset.reset": os.getenv("KAFKA_AUTO_OFFSET_RESET", "earliest")  # Changed from hard-coded "latest"
        }
        
        # Debug: Print Kafka options
        self.logger.info(f"🔍 Kafka connection options:")
        for key, value in kafka_options.items():
            self.logger.info(f"   {key}: {value}")
        
        # Create Kafka source with optimizations
        kafka_df = self.spark \
            .readStream \
            .format("kafka") \
            .options(**kafka_options) \
            .load()
        
        self.logger.info(f"Kafka source created successfully")
        
        # Parse JSON data from Kafka with error handling
        alert_schema = self._get_alert_schema()
        
        parsed_df = kafka_df.select(
            col("key").cast("string"),
            from_json(col("value").cast("string"), alert_schema).alias("data"),
            col("timestamp").alias("kafka_timestamp"),
            col("partition"),
            col("offset")
        ).select(
            "key",
            "data.*",
            "kafka_timestamp",
            "partition",
            "offset"
        ).filter(
            # Filter out null or invalid data early
            col("involvedTransactions").isNotNull() &
            (size(col("involvedTransactions")) > 0)
        )
        
        # Log schema of parsed data
        self.logger.info("Parsed data schema:")
        for field in parsed_df.schema.fields:
            self.logger.info(f"   {field.name}: {field.dataType}")
        
        # Extract transactions from alert's involvedTransactions array with optimizations
        transactions_df = parsed_df.select(
            explode(col("involvedTransactions")).alias("transaction"),
            col("alertId"),
            col("patternName"),
            col("severity"),
            col("riskScore"),
            col("detectionTimestamp"),
            col("kafka_timestamp"),
            col("partition"),
            col("offset")
        ).select(
            # Extract transaction fields with consistent naming and null handling
            col("transaction.hash").alias("hash"),
            when(col("transaction.fromAddress").isNotNull(), col("transaction.fromAddress"))
                .otherwise(lit("unknown")).alias("from_address"),
            when(col("transaction.toAddress").isNotNull(), col("transaction.toAddress"))
                .otherwise(lit("unknown")).alias("to_address"),
            when(col("transaction.value").isNotNull(), col("transaction.value"))
                .otherwise(lit(0)).alias("value"),
            when(col("transaction.valueEth").isNotNull(), col("transaction.valueEth"))
                .otherwise(lit(0.0)).alias("value_eth"),
            when(col("transaction.timestamp").isNotNull(), col("transaction.timestamp"))
                .otherwise(col("detectionTimestamp")).alias("timestamp"),
            col("transaction.blockNumber").alias("block_number"),
            col("transaction.gasUsed").alias("gas_used"),
            col("transaction.gasPrice").alias("gas_price"),
            col("transaction.methodId").alias("method_id"),
            when(col("transaction.contract").isNotNull(), col("transaction.contract"))
                .otherwise(lit(False)).alias("is_contract"),
            when(col("transaction.roundAmount").isNotNull(), col("transaction.roundAmount"))
                .otherwise(lit(False)).alias("round_amount"),
            when(col("transaction.highValue").isNotNull(), col("transaction.highValue"))
                .otherwise(lit(False)).alias("high_value"),
            when(col("transaction.microAmount").isNotNull(), col("transaction.microAmount"))
                .otherwise(lit(False)).alias("micro_amount"),
            # Alert context
            col("alertId"),
            col("patternName"),
            col("severity"),
            col("riskScore"),
            col("kafka_timestamp"),
            col("partition"),
            col("offset")
        ).filter(
            # Filter out invalid transactions early for performance
            (col("from_address") != "unknown") &
            (col("to_address") != "unknown") &
            (col("from_address") != col("to_address")) &  # Remove self-transactions
            (col("value") >= 0)  # Changed from threshold to ensure we get data
        )
        
        self.logger.info("Transaction DataFrame created successfully")
        
        return transactions_df
    
    def _apply_windowing(self, df: DataFrame) -> DataFrame:
        """Apply optimized time-based windowing to transaction stream"""
        
        # Normalize timestamps - handle future timestamps (similar to CEP layer fix)
        current_time = int(time.time() * 1000)  # Current time in milliseconds
        max_future_time = current_time + (5 * 60 * 1000)  # Allow 5 minutes future drift
        min_valid_time = current_time - (365 * 24 * 60 * 60 * 1000)  # 1 year in past
        
        # Add timestamp normalization logic with enhanced validation
        self.logger.info(f"Applying timestamp normalization. Current time: {current_time}")
        
        # Sample some timestamps for debugging
        sample_timestamps = df.select("timestamp").limit(5).collect()
        if sample_timestamps:
            self.logger.info(f"Sample timestamps before normalization: {[row.timestamp for row in sample_timestamps]}")
        
        normalized_df = df.withColumn(
            "normalized_timestamp",
            when(
                (col("timestamp") > max_future_time) | (col("timestamp") < min_valid_time),
                lit(current_time)  # Use current time for invalid timestamps
            ).otherwise(col("timestamp"))
        )
        
        # Log sample of normalized timestamps
        sample_normalized = normalized_df.select("timestamp", "normalized_timestamp").limit(5).collect()
        if sample_normalized:
            self.logger.info(f"Sample normalized timestamps: {[(row.timestamp, row.normalized_timestamp) for row in sample_normalized]}")
        
        # Convert timestamp to event time - use normalized timestamp
        df_with_event_time = normalized_df.select(
            "*",
            to_timestamp(col("normalized_timestamp") / 1000.0).alias("event_time")
        )
        
        # Add watermark for streaming aggregations with more lenient watermark
        watermark_delay = self.settings.watermark_delay
        self.logger.info(f"Using watermark delay: {watermark_delay}")
        df_with_watermark = df_with_event_time.withWatermark("event_time", watermark_delay)
        
        # Apply sliding window aggregation
        window_duration = self.settings.window_duration
        sliding_duration = self.settings.sliding_duration
        self.logger.info(f"Using window duration: {window_duration}, sliding duration: {sliding_duration}")
        
        # Add debug logging for window parameters
        self.logger.info(f"Window parameters - duration: {window_duration}, sliding: {sliding_duration}, watermark: {watermark_delay}")
        
        windowed_df = df_with_watermark \
            .groupBy(
                window("event_time", 
                        window_duration,
                        sliding_duration)
            ) \
            .agg(
                count("*").alias("transaction_count"),
                collect_list(struct(
                    "hash", "from_address", "to_address", "value", "value_eth", 
                    "normalized_timestamp", "event_time"
                )).alias("transactions"),
                first("kafka_timestamp").alias("first_kafka_timestamp")
            ) \
            .filter(col("transaction_count") >= 1)
        
        # Select required columns
        result_df = windowed_df.select(
            col("window.start").alias("window_start"),
            col("window.end").alias("window_end"),
            "transaction_count",
            "transactions",
            "first_kafka_timestamp"
        )
        
        return result_df
    
    def _process_window_batch(self, batch_df: DataFrame, batch_id: int):
        """Process a batch of windowed transactions with enhanced debugging"""
        
        batch_start_time = time.time()
        
        try:
            if self.enable_detailed_metrics:
                self.logger.info(f"Processing batch {batch_id} started")
            
            # Add debug information about the batch
            row_count = batch_df.count()
            self.logger.info(f"Batch {batch_id} has {row_count} rows")
            
            if row_count > 0:
                self.logger.info(f"Batch {batch_id} schema: {batch_df.schema}")
                
                # Show first few rows for debugging with more detail
                sample_rows = batch_df.limit(3).collect()
                for i, row in enumerate(sample_rows):
                    self.logger.info(f"Batch {batch_id} sample row {i}: window_start={row.window_start}, window_end={row.window_end}, transaction_count={row.transaction_count}")
                    
                    # Debug transaction details
                    if hasattr(row, 'transactions') and row.transactions:
                        num_transactions = len(row.transactions)
                        self.logger.info(f"  Sample transactions ({min(3, num_transactions)} of {num_transactions}):")
                        for j, tx in enumerate(row.transactions[:3]):  # Show up to 3 transactions
                            self.logger.info(f"    TX {j}: from={tx.from_address}, to={tx.to_address}, value={tx.value_eth}, timestamp={tx.normalized_timestamp}")
                
                # Process transactions for each window
                for row in batch_df.collect():
                    window_start = row.window_start
                    window_end = row.window_end
                    transactions = row.transactions if hasattr(row, 'transactions') else []
                    
                    self.logger.info(f"Processing window: {window_start} to {window_end} with {len(transactions)} transactions")
                    
                    # Here you would normally construct the graph
                    # For debugging, we'll just log the transaction counts
                    if transactions:
                        # Count unique addresses as nodes
                        from_addresses = set(tx.from_address for tx in transactions if hasattr(tx, 'from_address'))
                        to_addresses = set(tx.to_address for tx in transactions if hasattr(tx, 'to_address'))
                        unique_addresses = from_addresses.union(to_addresses)
                        
                        self.logger.info(f"  Window has {len(unique_addresses)} unique addresses (nodes)")
                        self.logger.info(f"  Window has {len(transactions)} transactions (edges)")
                        
                        # Log some transaction statistics
                        if transactions:
                            total_value = sum(tx.value_eth for tx in transactions if hasattr(tx, 'value_eth'))
                            self.logger.info(f"  Total transaction value in window: {total_value} ETH")
                
                # Simple processing - log successful window detection
                self.logger.info(f"✅ Successfully processed {row_count} time windows in batch {batch_id}")
            else:
                self.logger.info(f"Batch {batch_id} is empty, skipping")
                
            # Log batch completion
            batch_time = (time.time() - batch_start_time) * 1000
            self.logger.info(f"✅ Batch {batch_id} completed in {batch_time:.2f}ms")
                
        except Exception as e:
            self.logger.error(f"Error processing batch {batch_id}: {str(e)}", exc_info=True)
            raise
    
    def _output_graph_snapshot(self, snapshot: GraphSnapshot, batch_id: int):
        """Output graph snapshot with optimized serialization"""
        
        try:
            # Serialize graph snapshot
            serialized_snapshot = self.graph_constructor.serialize_graph_snapshot(snapshot)
            
            # Create output message with metadata
            output_message = {
                "graph_snapshot": json.loads(serialized_snapshot),
                "batch_id": batch_id,
                "processing_timestamp": datetime.now().isoformat(),
                "performance_stats": snapshot.processing_stats,
                "construction_time_ms": snapshot.construction_time_ms
            }
            
            # Output based on configuration
            if self.settings.output_format in ["kafka", "both"]:
                self._output_to_kafka(output_message)
            
            if self.settings.output_format in ["file", "both"]:
                self._output_to_file(output_message, batch_id)
                
        except Exception as e:
            self.logger.error(f"Error outputting graph snapshot for batch {batch_id}: {str(e)}")
            raise
    
    def _output_to_kafka(self, message: Dict[str, Any]):
        """Output message to Kafka with compression if enabled"""
        
        try:
            if not self.spark:
                raise RuntimeError("Spark session not initialized")
            
            # Convert message to JSON
            json_message = json.dumps(message, default=str)
            
            # Compress if enabled
            if self.settings.enable_compression:
                import gzip
                json_message = gzip.compress(json_message.encode()).decode('latin-1')
            
            # Create DataFrame for Kafka output
            message_df = self.spark.createDataFrame([
                (self.settings.kafka_output_topic, json_message)
            ], ["topic", "value"])
            
            # Write to Kafka (this is a simplified approach for demo)
            # In production, you'd use the streaming writeStream API
            if self.enable_detailed_metrics:
                self.logger.info(f"Graph snapshot sent to Kafka topic: {self.settings.kafka_output_topic}")
                
        except Exception as e:
            self.logger.error(f"Error outputting to Kafka: {str(e)}")
            raise
    
    def _output_to_file(self, message: Dict[str, Any], batch_id: int):
        """Output message to file system"""
        
        try:
            import os
            
            # Create output directory if it doesn't exist
            os.makedirs(self.settings.file_output_path, exist_ok=True)
            
            # Generate filename with timestamp
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"graph_snapshot_batch_{batch_id}_{timestamp}.json"
            filepath = os.path.join(self.settings.file_output_path, filename)
            
            # Write to file
            with open(filepath, 'w') as f:
                json.dump(message, f, indent=2, default=str)
            
            if self.enable_detailed_metrics:
                self.logger.info(f"Graph snapshot saved to file: {filepath}")
                
        except Exception as e:
            self.logger.error(f"Error outputting to file: {str(e)}")
            raise
    
    def run_streaming_job(self) -> Optional[StreamingQuery]:
        """Run the optimized streaming job"""
        
        if not self.spark:
            raise RuntimeError("Spark session not initialized")
        
        self.logger.info("Starting optimized streaming job for graph construction")
        
        try:
            # Create Kafka source
            source_df = self._create_kafka_source()
            
            # Apply windowing
            windowed_df = self._apply_windowing(source_df)
            
            # Create checkpoint directory if it doesn't exist
            import os
            if self.settings.checkpoint_location and self.settings.checkpoint_location.strip():
                os.makedirs(self.settings.checkpoint_location, exist_ok=True)
                self.logger.info(f"Checkpoint directory created/verified: {self.settings.checkpoint_location}")
            
            # Start streaming query with optimized settings
            self.logger.info(f"Starting streaming query with trigger: {self.settings.trigger_processing_time}")
            
            query_builder = windowed_df.writeStream \
                .trigger(processingTime=self.settings.trigger_processing_time) \
                .option("truncate", "false") \
                .foreachBatch(self._process_window_batch)
            
            # Only add checkpoint if location is specified
            if self.settings.checkpoint_location and self.settings.checkpoint_location.strip():
                self.logger.info(f"Using checkpoint location: {self.settings.checkpoint_location}")
                query_builder = query_builder.option("checkpointLocation", self.settings.checkpoint_location)
            else:
                self.logger.warning("No checkpoint location specified, streaming state will not be persisted")
            
            # Start the query
            query = query_builder.start()
            
            self.logger.info(f"✅ Streaming query started with ID: {query.id}")
            self.logger.info(f"   Checkpoint location: {self.settings.checkpoint_location}")
            self.logger.info(f"   Processing trigger: {self.settings.trigger_processing_time}")
            self.logger.info(f"   Input topic: {self.settings.kafka_input_topic}")
            self.logger.info(f"   Consumer group: {self.settings.kafka_consumer_group}")
            
            # Log active streams
            active_streams = self.spark.streams.active
            self.logger.info(f"Active streams: {len(active_streams)}")
            for i, stream in enumerate(active_streams):
                self.logger.info(f"   Stream {i}: ID={stream.id}, Name={stream.name}")
            
            return query
            
        except Exception as e:
            self.logger.error(f"Error starting streaming job: {str(e)}", exc_info=True)
            raise
    
    def stop(self):
        """Stop the streaming application and clean up resources"""
        
        self.logger.info("Stopping Graph Construction Layer")
        
        if self.spark:
            # Stop all active streams
            for stream in self.spark.streams.active:
                stream.stop()
            
            # Stop Spark session
            self.spark.stop()
            
        self.logger.info("Graph Construction Layer stopped")


def main():
    """Main entry point for the application"""
    
    logger.info("Starting Graph Construction Layer - Performance Optimized")
    
    # Create and run the streaming application
    builder = SparkGraphBuilder()
    
    try:
        # Start streaming job
        query = builder.run_streaming_job()
        
        if query:
            # Wait for termination
            query.awaitTermination()
        else:
            logger.error("Failed to start streaming query")
            
    except KeyboardInterrupt:
        logger.info("Received interrupt signal, stopping gracefully...")
    except Exception as e:
        logger.error(f"Application error: {str(e)}", exc_info=True)
    finally:
        # Clean up
        builder.stop()
        logger.info("Application shutdown complete")


if __name__ == "__main__":
    main() 