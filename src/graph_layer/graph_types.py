"""
Graph Types Module

This module contains data classes and types used for graph construction and representation.
"""

from dataclasses import dataclass
from datetime import datetime
from typing import Dict, Any, Optional

import torch
from torch_geometric.data import Data


@dataclass
class GraphSnapshot:
    """Represents a complete graph snapshot with metadata"""
    graph_data: Data
    window_start: datetime
    window_end: datetime
    num_nodes: int
    num_edges: int
    max_risk_score: float
    total_volume: float
    metadata: Dict[str, Any]
    construction_time_ms: float
    processing_stats: Dict[str, Any]
    # Optional fields
    sparsity_ratio: float = 0.0
    graph_density: float = 0.0
    compilation_time_ms: float = 0.0 