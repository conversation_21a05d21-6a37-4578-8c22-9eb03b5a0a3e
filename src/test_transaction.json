{"alertId": "test-alert-1", "patternName": "TEST_PATTERN", "alertType": "TEST_ALERT", "severity": "HIGH", "description": "Test alert for graph construction", "detectionTimestamp": 1752074560399, "involvedTransactions": [{"hash": "0x123456789abcdef", "fromAddress": "0xTestSender123456789", "toAddress": "0xTestReceiver987654321", "value": 1000000000000000000, "blockNumber": 12345, "timestamp": 1752074436, "gasUsed": 21000, "gasPrice": 18400877, "methodId": "", "valueEth": 1.0, "riskLevel": "HIGH", "contract": false}], "primaryAddress": "0xTestSender123456789", "totalValue": 1.0, "riskScore": "HIGH", "reason": "Test transaction for graph construction"}