# TGAT Layer

This directory contains the Temporal Graph Attention Network (TGAT) model implementation for AML detection on blockchain transactions.

## Components

### `working_tgat_elliptic.py`
- **Purpose**: Main working TGAT implementation trained on Elliptic Bitcoin dataset
- **Features**:
  - Complete TGAT model with temporal attention mechanisms
  - Elliptic dataset integration (203K+ Bitcoin transactions)
  - Feature selection and preprocessing (166→64 optimized features)
  - Training pipeline with proper evaluation metrics
- **Performance**: 74.7% AUC, 74.2% accuracy on real Bitcoin data

### `optimized_tgat_training.py`
- **Purpose**: Advanced training script with optimization techniques
- **Features**:
  - Adaptive TGAT model handling variable input dimensions
  - Focal Loss for class imbalance handling
  - Feature selection using Random Forest
  - Enhanced edge feature creation
  - Class weight computation for imbalanced datasets

### Model Architecture

The TGAT model consists of:
- **Time Encoder**: Temporal feature encoding for blockchain timestamps
- **TGAT Layers**: Multi-layer temporal graph attention with memory
- **Memory Module**: Node embedding storage with temporal updates
- **Classifier**: Transaction-level illicit detection head

**Model Specifications**:
- Input: Node features (64D after selection), Edge features (8D), Timestamps
- Output: Anomaly scores for transactions (binary classification)
- Parameters: ~58K parameters optimized for blockchain addresses
- Training: Elliptic dataset with smart sampling for label balance

## Usage

```python
from tgat_layer.working_tgat_elliptic import WorkingTGATModel, EllipticDataLoader, WorkingTGATTrainer

# Load and preprocess data
loader = EllipticDataLoader("dataset copy")
data = loader.load_data(max_nodes=12000, min_labeled=2000)

# Initialize and train model
trainer = WorkingTGATTrainer(data)
trainer.train(epochs=50)

# Make predictions
model = trainer.model
predictions = model(node_features, edge_index, edge_attr, edge_time)
```

## Integration

This layer integrates with:
- Graph Construction Layer (input data)
- Database Layer (stores predictions)
- GNN Explainer Layer (provides explanations)
- CEP Layer (filters input transactions)

## Performance Metrics

Current model performance on Elliptic dataset:
- **Test AUC**: 74.7% (good discriminative performance)
- **Test Accuracy**: 74.2% (balanced accuracy)
- **Test Recall**: 66.7% (detecting 2/3 of illicit transactions)
- **Training**: Converged from Loss 0.69 → 0.076 over 40 epochs 