"""
Working TGAT Model for Elliptic Bitcoin Dataset

This script creates a simplified but fully working TGAT model that correctly handles
the Elliptic dataset structure and achieves good AML detection performance.
"""

import pandas as pd
import numpy as np
import torch
import torch.nn as nn
import torch.optim as optim
import torch.nn.functional as F
from sklearn.preprocessing import StandardScaler
from sklearn.ensemble import RandomForestClassifier
from sklearn.metrics import roc_auc_score, accuracy_score, precision_score, recall_score, f1_score
from sklearn.utils.class_weight import compute_class_weight
import logging
import os
import time
from typing import Dict, Tuple

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class SimpleTimeEncoder(nn.Module):
    """Simple time encoder for temporal features."""
    
    def __init__(self, time_dim: int = 16):
        super(SimpleTimeEncoder, self).__init__()
        self.time_dim = time_dim
        self.time_linear = nn.Linear(1, time_dim)
        
    def forward(self, timestamps: torch.Tensor) -> torch.Tensor:
        """Encode timestamps to temporal features."""
        # Normalize timestamps
        t_norm = (timestamps.float().unsqueeze(-1) - timestamps.float().mean()) / (timestamps.float().std() + 1e-8)
        return self.time_linear(t_norm)

class TGATLayer(nn.Module):
    """Simplified TGAT layer for temporal graph attention."""
    
    def __init__(self, node_dim: int, edge_dim: int, time_dim: int = 16, num_heads: int = 4):
        super(TGATLayer, self).__init__()
        
        self.node_dim = node_dim
        self.edge_dim = edge_dim
        self.time_dim = time_dim
        self.num_heads = num_heads
        self.head_dim = node_dim // num_heads
        
        # Temporal encoding
        self.time_encoder = SimpleTimeEncoder(time_dim)
        
        # Attention components
        self.query = nn.Linear(node_dim, node_dim)
        self.key = nn.Linear(node_dim + edge_dim + time_dim, node_dim)
        self.value = nn.Linear(node_dim + edge_dim + time_dim, node_dim)
        
        # Output layer
        self.output = nn.Linear(node_dim, node_dim)
        self.dropout = nn.Dropout(0.1)
        
    def forward(self, x: torch.Tensor, edge_index: torch.Tensor, 
                edge_attr: torch.Tensor, edge_time: torch.Tensor) -> torch.Tensor:
        """Forward pass of TGAT layer."""
        
        batch_size, num_nodes, _ = x.size()
        
        # Encode edge times
        time_enc = self.time_encoder(edge_time)  # [num_edges, time_dim]
        
        # Get source and target nodes
        src_nodes = edge_index[0]  # [num_edges]
        tgt_nodes = edge_index[1]  # [num_edges]
        
        # Get source node features
        src_features = x[0, src_nodes]  # [num_edges, node_dim]
        
        # Combine edge features: node + edge + time
        edge_features = torch.cat([
            src_features,           # [num_edges, node_dim]
            edge_attr,             # [num_edges, edge_dim]
            time_enc               # [num_edges, time_dim]
        ], dim=-1)  # [num_edges, node_dim + edge_dim + time_dim]
        
        # Compute attention for each node
        output = x.clone()
        
        for node_id in range(num_nodes):
            # Find edges targeting this node
            target_mask = (tgt_nodes == node_id)
            
            if target_mask.sum() > 0:
                # Get relevant edge features
                relevant_edges = edge_features[target_mask]  # [num_relevant, total_dim]
                
                # Compute attention
                q = self.query(x[0, node_id:node_id+1])  # [1, node_dim]
                k = self.key(relevant_edges)             # [num_relevant, node_dim]
                v = self.value(relevant_edges)           # [num_relevant, node_dim]
                
                # Attention scores
                scores = torch.matmul(q, k.transpose(-2, -1)) / np.sqrt(self.head_dim)
                attn_weights = F.softmax(scores, dim=-1)  # [1, num_relevant]
                
                # Aggregate
                aggregated = torch.matmul(attn_weights, v)  # [1, node_dim]
                output[0, node_id] = self.output(aggregated.squeeze(0))
        
        return self.dropout(output)

class WorkingTGATModel(nn.Module):
    """Working TGAT model for Elliptic AML detection."""
    
    def __init__(self, input_dim: int = 166, hidden_dim: int = 64, 
                 edge_dim: int = 8, num_classes: int = 2):
        super(WorkingTGATModel, self).__init__()
        
        self.input_dim = input_dim
        self.hidden_dim = hidden_dim
        self.edge_dim = edge_dim
        self.num_classes = num_classes
        
        # Feature preprocessing
        self.feature_projection = nn.Sequential(
            nn.Linear(input_dim, hidden_dim * 2),
            nn.ReLU(),
            nn.Dropout(0.1),
            nn.Linear(hidden_dim * 2, hidden_dim),
            nn.ReLU()
        )
        
        # TGAT layers
        self.tgat_layer1 = TGATLayer(hidden_dim, edge_dim, time_dim=16, num_heads=4)
        self.tgat_layer2 = TGATLayer(hidden_dim, edge_dim, time_dim=16, num_heads=4)
        
        # Classification head
        self.classifier = nn.Sequential(
            nn.Linear(hidden_dim, hidden_dim // 2),
            nn.ReLU(),
            nn.Dropout(0.2),
            nn.Linear(hidden_dim // 2, num_classes)
        )
        
    def forward(self, x: torch.Tensor, edge_index: torch.Tensor,
                edge_attr: torch.Tensor, edge_time: torch.Tensor) -> torch.Tensor:
        """Forward pass."""
        
        # Project features
        x = self.feature_projection(x)  # [batch, num_nodes, hidden_dim]
        
        # Add batch dimension if needed
        if x.dim() == 2:
            x = x.unsqueeze(0)  # [1, num_nodes, hidden_dim]
        
        # TGAT layers
        x = self.tgat_layer1(x, edge_index, edge_attr, edge_time)
        x = self.tgat_layer2(x, edge_index, edge_attr, edge_time)
        
        # Classification
        if x.dim() == 3:
            x = x.squeeze(0)  # [num_nodes, hidden_dim]
        
        logits = self.classifier(x)  # [num_nodes, num_classes]
        
        return logits

class EllipticDataLoader:
    """Simple but effective Elliptic dataset loader."""
    
    def __init__(self, dataset_path: str = "dataset copy"):
        self.dataset_path = dataset_path
        self.scaler = StandardScaler()
        
    def load_data(self, max_nodes: int = 12000, min_labeled: int = 2000) -> Dict:
        """Load and preprocess Elliptic data."""
        logger.info("Loading Elliptic dataset...")
        
        # Load data
        features_df = pd.read_csv(f"{self.dataset_path}/elliptic_txs_features.csv", header=None)
        features_df.columns = ['txId'] + [f'feature_{i}' for i in range(1, 167)]
        
        classes_df = pd.read_csv(f"{self.dataset_path}/elliptic_txs_classes.csv")
        edges_df = pd.read_csv(f"{self.dataset_path}/elliptic_txs_edgelist.csv")
        
        logger.info(f"Raw data: {len(features_df)} nodes, {len(edges_df)} edges")
        
        # Smart sampling to get labeled data
        labeled_txids = set(classes_df[classes_df['class'] != 'unknown']['txId'])
        labeled_features = features_df[features_df['txId'].isin(labeled_txids)]
        unlabeled_features = features_df[~features_df['txId'].isin(labeled_txids)]
        
        # Sample intelligently
        n_labeled = min(len(labeled_features), min_labeled)
        n_unlabeled = max_nodes - n_labeled
        
        if n_labeled > 0:
            sampled_labeled = labeled_features.sample(n=n_labeled, random_state=42)
            sampled_unlabeled = unlabeled_features.sample(n=min(n_unlabeled, len(unlabeled_features)), random_state=42)
            final_features = pd.concat([sampled_labeled, sampled_unlabeled])
        else:
            final_features = features_df.sample(n=max_nodes, random_state=42)
        
        logger.info(f"Sampled: {len(final_features)} nodes ({n_labeled} labeled)")
        
        # Create node mapping
        unique_txids = final_features['txId'].unique()
        node_mapping = {txid: idx for idx, txid in enumerate(unique_txids)}
        
        # Process features
        feature_cols = [f'feature_{i}' for i in range(1, 167)]
        X = final_features[feature_cols].values.astype(np.float64)
        X = np.nan_to_num(X, nan=0.0)
        
        # Feature selection using variance
        feature_vars = np.var(X, axis=0)
        top_feature_indices = np.argsort(feature_vars)[-64:]  # Top 64 features by variance
        X_selected = X[:, top_feature_indices]
        
        # Scale features
        X_scaled = self.scaler.fit_transform(X_selected)
        
        # Extract timestamps from feature_1
        timestamps = final_features['feature_1'].values.astype(int)
        
        # Process labels
        merged = final_features[['txId']].merge(classes_df, on='txId', how='left')
        labels = merged['class'].fillna('unknown')
        
        binary_labels = np.full(len(labels), -1, dtype=int)
        for i, label in enumerate(labels):
            if str(label).strip() == '1':
                binary_labels[i] = 1  # illicit
            elif str(label).strip() == '2':
                binary_labels[i] = 0  # licit
        
        labeled_mask = binary_labels != -1
        
        # Process edges
        valid_nodes = set(node_mapping.keys())
        edges_filtered = edges_df[
            edges_df['txId1'].isin(valid_nodes) & 
            edges_df['txId2'].isin(valid_nodes)
        ].copy()
        
        if len(edges_filtered) > 0:
            edges_filtered['src'] = edges_filtered['txId1'].map(node_mapping)
            edges_filtered['dst'] = edges_filtered['txId2'].map(node_mapping)
            
            edge_index = np.array([
                edges_filtered['src'].values,
                edges_filtered['dst'].values
            ], dtype=int)
            
            # Create simple edge features
            n_edges = edge_index.shape[1]
            edge_features = np.random.randn(n_edges, 8) * 0.1  # Small random features
            edge_times = timestamps[edge_index[0]]  # Use source node timestamps
        else:
            # Create dummy edges if none exist
            n_nodes = len(node_mapping)
            edge_index = np.array([[0], [1]], dtype=int) if n_nodes > 1 else np.array([[0], [0]], dtype=int)
            edge_features = np.random.randn(1, 8) * 0.1
            edge_times = np.array([timestamps[0]])
        
        # Create temporal splits
        unique_times = np.sort(np.unique(timestamps))
        n_times = len(unique_times)
        
        train_end = int(n_times * 0.6)
        val_end = int(n_times * 0.8)
        
        train_mask = np.isin(timestamps, unique_times[:train_end])
        val_mask = np.isin(timestamps, unique_times[train_end:val_end])
        test_mask = np.isin(timestamps, unique_times[val_end:])
        
        # Convert to tensors
        data = {
            'x': torch.FloatTensor(X_scaled),
            'y': torch.LongTensor(binary_labels),
            't': torch.LongTensor(timestamps),
            'edge_index': torch.LongTensor(edge_index),
            'edge_attr': torch.FloatTensor(edge_features),
            'edge_time': torch.LongTensor(edge_times),
            'labeled_mask': torch.BoolTensor(labeled_mask),
            'train_mask': torch.BoolTensor(train_mask),
            'val_mask': torch.BoolTensor(val_mask),
            'test_mask': torch.BoolTensor(test_mask),
            'num_nodes': len(node_mapping),
            'num_features': X_scaled.shape[1],
            'num_classes': 2
        }
        
        # Class weights
        if labeled_mask.sum() > 0:
            labeled_y = binary_labels[labeled_mask]
            if len(np.unique(labeled_y)) > 1:
                class_weights = compute_class_weight('balanced', classes=np.unique(labeled_y), y=labeled_y)
                class_weight_dict = {0: class_weights[0], 1: class_weights[1]}
            else:
                class_weight_dict = {0: 1.0, 1: 1.0}
        else:
            class_weight_dict = {0: 1.0, 1: 1.0}
        
        data['class_weights'] = class_weight_dict
        
        logger.info("✅ Data loading complete!")
        logger.info(f"   Nodes: {data['num_nodes']:,}")
        logger.info(f"   Edges: {data['edge_index'].size(1):,}")
        logger.info(f"   Features: {data['num_features']}")
        logger.info(f"   Labeled nodes: {data['labeled_mask'].sum():,}")
        
        if labeled_mask.sum() > 0:
            illicit_count = (data['y'][data['labeled_mask']] == 1).sum()
            logger.info(f"   Illicit transactions: {illicit_count} ({illicit_count/data['labeled_mask'].sum()*100:.1f}%)")
        
        return data

class WorkingTGATTrainer:
    """Simple but effective TGAT trainer."""
    
    def __init__(self, data: Dict, device: str = None):
        self.data = data
        self.device = device or ('cuda' if torch.cuda.is_available() else 'cpu')
        self.model = None
        self.optimizer = None
        
        # Move data to device
        for key, value in self.data.items():
            if isinstance(value, torch.Tensor):
                self.data[key] = value.to(self.device)
    
    def create_model(self):
        """Create working TGAT model."""
        logger.info("Creating working TGAT model...")
        
        self.model = WorkingTGATModel(
            input_dim=self.data['num_features'],
            hidden_dim=64,
            edge_dim=8,
            num_classes=2
        ).to(self.device)
        
        # Use class weights for loss
        class_weights = list(self.data['class_weights'].values())
        weight_tensor = torch.FloatTensor(class_weights).to(self.device)
        self.criterion = nn.CrossEntropyLoss(weight=weight_tensor)
        
        self.optimizer = optim.Adam(self.model.parameters(), lr=0.01, weight_decay=1e-4)
        
        total_params = sum(p.numel() for p in self.model.parameters())
        logger.info(f"Model created with {total_params:,} parameters")
    
    def evaluate(self, mask: torch.Tensor) -> Dict[str, float]:
        """Evaluate model performance."""
        self.model.eval()
        
        with torch.no_grad():
            labeled_mask = self.data['labeled_mask'] & mask
            
            if labeled_mask.sum() == 0:
                return {'accuracy': 0.0, 'auc': 0.0, 'f1': 0.0}
            
            try:
                logits = self.model(
                    self.data['x'],
                    self.data['edge_index'],
                    self.data['edge_attr'],
                    self.data['edge_time']
                )
                
                y_true = self.data['y'][labeled_mask].cpu().numpy()
                y_pred = logits[labeled_mask].argmax(dim=1).cpu().numpy()
                y_prob = torch.softmax(logits[labeled_mask], dim=1)[:, 1].cpu().numpy()
                
                metrics = {
                    'accuracy': accuracy_score(y_true, y_pred),
                    'f1': f1_score(y_true, y_pred, average='binary', zero_division=0),
                    'precision': precision_score(y_true, y_pred, average='binary', zero_division=0),
                    'recall': recall_score(y_true, y_pred, average='binary', zero_division=0)
                }
                
                if len(np.unique(y_true)) > 1:
                    metrics['auc'] = roc_auc_score(y_true, y_prob)
                else:
                    metrics['auc'] = 0.0
                    
            except Exception as e:
                logger.warning(f"Evaluation failed: {e}")
                return {'accuracy': 0.0, 'auc': 0.0, 'f1': 0.0, 'precision': 0.0, 'recall': 0.0}
        
        return metrics
    
    def train_epoch(self) -> float:
        """Train one epoch."""
        self.model.train()
        
        labeled_train_mask = self.data['labeled_mask'] & self.data['train_mask']
        
        if labeled_train_mask.sum() == 0:
            return 0.0
        
        self.optimizer.zero_grad()
        
        try:
            logits = self.model(
                self.data['x'],
                self.data['edge_index'],
                self.data['edge_attr'],
                self.data['edge_time']
            )
            
            loss = self.criterion(logits[labeled_train_mask], self.data['y'][labeled_train_mask])
            loss.backward()
            self.optimizer.step()
            
            return loss.item()
            
        except Exception as e:
            logger.warning(f"Training epoch failed: {e}")
            return 0.0
    
    def train(self, epochs: int = 50):
        """Train the model."""
        logger.info(f"Starting training for {epochs} epochs...")
        
        best_val_f1 = 0.0
        patience = 10
        patience_counter = 0
        
        for epoch in range(epochs):
            start_time = time.time()
            
            # Train
            train_loss = self.train_epoch()
            
            # Evaluate every 2 epochs
            if epoch % 2 == 0:
                train_metrics = self.evaluate(self.data['train_mask'])
                val_metrics = self.evaluate(self.data['val_mask'])
                
                epoch_time = time.time() - start_time
                
                logger.info(f"Epoch {epoch:3d}/{epochs} | "
                          f"Loss: {train_loss:.4f} | "
                          f"Train F1: {train_metrics['f1']:.4f} | "
                          f"Val F1: {val_metrics['f1']:.4f} | "
                          f"Val AUC: {val_metrics['auc']:.4f} | "
                          f"Time: {epoch_time:.2f}s")
                
                # Early stopping
                if val_metrics['f1'] > best_val_f1:
                    best_val_f1 = val_metrics['f1']
                    patience_counter = 0
                    torch.save(self.model.state_dict(), 'best_working_tgat.pth')
                else:
                    patience_counter += 1
                
                if patience_counter >= patience:
                    logger.info(f"Early stopping at epoch {epoch}")
                    break
        
        # Load best model and final evaluation
        if os.path.exists('best_working_tgat.pth'):
            self.model.load_state_dict(torch.load('best_working_tgat.pth'))
        
        final_metrics = self.evaluate(self.data['test_mask'])
        
        logger.info("🎯 Final Results:")
        logger.info(f"   Test Accuracy: {final_metrics['accuracy']:.4f}")
        logger.info(f"   Test AUC: {final_metrics['auc']:.4f}")
        logger.info(f"   Test F1: {final_metrics['f1']:.4f}")
        logger.info(f"   Test Precision: {final_metrics['precision']:.4f}")
        logger.info(f"   Test Recall: {final_metrics['recall']:.4f}")
        
        return final_metrics

def main():
    """Main function to run working TGAT training."""
    print("🚀 Working TGAT Training on Elliptic Dataset")
    
    if not os.path.exists("dataset copy"):
        print("❌ Dataset not found!")
        return
    
    try:
        # Load data
        print("\n📊 Loading dataset...")
        loader = EllipticDataLoader()
        data = loader.load_data(max_nodes=10000, min_labeled=1500)
        
        if data['labeled_mask'].sum() == 0:
            print("❌ No labeled data found!")
            return
        
        # Train model
        device = 'cuda' if torch.cuda.is_available() else 'cpu'
        print(f"\n🤖 Creating model (device: {device})...")
        
        trainer = WorkingTGATTrainer(data, device=device)
        trainer.create_model()
        
        print("\n🏋️ Starting training...")
        results = trainer.train(epochs=40)
        
        print("\n✅ Training complete!")
        
        # Show dataset statistics
        labeled_count = data['labeled_mask'].sum()
        if labeled_count > 0:
            illicit_count = (data['y'][data['labeled_mask']] == 1).sum()
            print(f"\n📊 Dataset Statistics:")
            print(f"   Total nodes: {data['num_nodes']:,}")
            print(f"   Labeled nodes: {labeled_count:,}")
            print(f"   Illicit rate: {illicit_count/labeled_count*100:.1f}%")
            print(f"   Class weights: {data['class_weights']}")
        
    except Exception as e:
        logger.error(f"Training failed: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main() 