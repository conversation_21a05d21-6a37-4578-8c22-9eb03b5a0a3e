"""
Optimized TGAT Training for Elliptic Bitcoin Dataset

This script implements an enhanced TGAT model with feature selection, class imbalance handling,
and advanced training techniques for better AML detection performance.
"""

import pandas as pd
import numpy as np
import torch
import torch.nn as nn
import torch.optim as optim
import torch.nn.functional as F
from sklearn.preprocessing import StandardScaler
from sklearn.feature_selection import SelectKBest, mutual_info_classif
from sklearn.ensemble import RandomForestClassifier
from sklearn.metrics import roc_auc_score, accuracy_score, precision_score, recall_score, f1_score, classification_report
from sklearn.utils.class_weight import compute_class_weight
import logging
import os
import time
from typing import Dict, Tuple, Optional
import pickle

# Import our TGAT model
from tgat_model import TGATForBlockchainAML

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class FocalLoss(nn.Module):
    """Focal Loss for handling class imbalance."""
    
    def __init__(self, alpha: float = 1.0, gamma: float = 2.0, reduction: str = 'mean'):
        super(FocalLoss, self).__init__()
        self.alpha = alpha
        self.gamma = gamma
        self.reduction = reduction
        
    def forward(self, inputs: torch.Tensor, targets: torch.Tensor) -> torch.Tensor:
        ce_loss = F.cross_entropy(inputs, targets, reduction='none')
        pt = torch.exp(-ce_loss)
        focal_loss = self.alpha * (1 - pt) ** self.gamma * ce_loss
        
        if self.reduction == 'mean':
            return focal_loss.mean()
        elif self.reduction == 'sum':
            return focal_loss.sum()
        return focal_loss

class AdaptiveTGATModel(nn.Module):
    """Adaptive TGAT model that can handle variable input dimensions."""
    
    def __init__(self, input_dim: int = 166, hidden_dim: int = 64, 
                 selected_features: int = 64, use_feature_selection: bool = True):
        super(AdaptiveTGATModel, self).__init__()
        
        self.input_dim = input_dim
        self.hidden_dim = hidden_dim
        self.selected_features = selected_features
        self.use_feature_selection = use_feature_selection
        
        # Feature selection layer
        if use_feature_selection:
            self.feature_selector = nn.Sequential(
                nn.Linear(input_dim, hidden_dim * 2),
                nn.ReLU(),
                nn.Dropout(0.1),
                nn.Linear(hidden_dim * 2, selected_features),
                nn.ReLU()
            )
            tgat_input_dim = selected_features
        else:
            self.feature_selector = nn.Linear(input_dim, selected_features)
            tgat_input_dim = selected_features
        
        # Create base TGAT model with adapted dimensions
        self.tgat = TGATForBlockchainAML(
            node_feat_dim=tgat_input_dim,
            edge_feat_dim=8,
            hidden_dim=hidden_dim,
            num_layers=2,
            num_heads=4,
            dropout=0.15
        )
    
    def forward(self, x: torch.Tensor, edge_index: torch.Tensor, 
                edge_attr: torch.Tensor, edge_time: torch.Tensor, 
                node_time: torch.Tensor) -> Dict[str, torch.Tensor]:
        """Forward pass with feature selection."""
        
        # Apply feature selection
        x_selected = self.feature_selector(x)
        
        # Pass through TGAT
        outputs = self.tgat(x_selected, edge_index, edge_attr, edge_time, node_time)
        
        return outputs

class OptimizedEllipticLoader:
    """Optimized Elliptic dataset loader with feature analysis."""
    
    def __init__(self, dataset_path: str = "dataset copy"):
        self.dataset_path = dataset_path
        self.scaler = StandardScaler()
        self.feature_selector = None
        
    def load_and_analyze(self, max_nodes: int = 10000, min_labeled: int = 1000, 
                        selected_features: int = 64) -> Dict:
        """Load data with feature analysis and selection."""
        logger.info("Loading and analyzing Elliptic dataset...")
        
        # Load all data
        features_df = pd.read_csv(f"{self.dataset_path}/elliptic_txs_features.csv", header=None)
        features_df.columns = ['txId'] + [f'feature_{i}' for i in range(1, 167)]
        
        classes_df = pd.read_csv(f"{self.dataset_path}/elliptic_txs_classes.csv")
        edges_df = pd.read_csv(f"{self.dataset_path}/elliptic_txs_edgelist.csv")
        
        logger.info(f"Loaded {len(features_df)} transactions, {len(edges_df)} edges")
        
        # Find labeled transactions first
        labeled_txids = set(classes_df[classes_df['class'] != 'unknown']['txId'])
        
        # Smart sampling: prioritize labeled transactions
        labeled_features = features_df[features_df['txId'].isin(labeled_txids)]
        unlabeled_features = features_df[~features_df['txId'].isin(labeled_txids)]
        
        logger.info(f"Found {len(labeled_features)} labeled transactions")
        
        # Sample data intelligently
        n_labeled = min(len(labeled_features), min_labeled)
        n_unlabeled = min(max_nodes - n_labeled, len(unlabeled_features))
        
        if n_labeled > 0:
            sampled_labeled = labeled_features.sample(n=n_labeled, random_state=42)
        else:
            sampled_labeled = labeled_features
            
        if n_unlabeled > 0:
            sampled_unlabeled = unlabeled_features.sample(n=n_unlabeled, random_state=42)
            final_features = pd.concat([sampled_labeled, sampled_unlabeled])
        else:
            final_features = sampled_labeled
        
        logger.info(f"Using {len(final_features)} transactions ({len(sampled_labeled)} labeled)")
        
        # Create node mapping
        unique_txids = final_features['txId'].unique()
        node_mapping = {txid: idx for idx, txid in enumerate(unique_txids)}
        
        # Process features
        feature_cols = [col for col in final_features.columns if col.startswith('feature_')]
        X = final_features[feature_cols].values.astype(np.float64)
        X = np.nan_to_num(X, nan=0.0)
        
        # Feature selection using Random Forest on labeled data
        if len(sampled_labeled) > 10:  # Need enough samples for feature selection
            labeled_X = sampled_labeled[feature_cols].values.astype(np.float64)
            labeled_X = np.nan_to_num(labeled_X, nan=0.0)
            
            # Get labels for feature selection
            labeled_classes = sampled_labeled.merge(classes_df, on='txId')['class'].values
            labeled_y = (labeled_classes == 1).astype(int)  # 1=illicit, 0=licit
            
            if len(np.unique(labeled_y)) > 1:  # Need both classes
                logger.info("Performing feature selection...")
                self.scaler.fit(labeled_X)
                labeled_X_scaled = self.scaler.transform(labeled_X)
                
                # Use Random Forest for feature selection
                rf = RandomForestClassifier(n_estimators=50, random_state=42, class_weight='balanced')
                rf.fit(labeled_X_scaled, labeled_y)
                
                # Select top features
                feature_importance = rf.feature_importances_
                top_indices = np.argsort(feature_importance)[-selected_features:]
                
                logger.info(f"Selected {len(top_indices)} most important features")
                self.feature_selector = top_indices
            else:
                # Fallback: use all features
                self.scaler.fit(X)
                self.feature_selector = np.arange(min(selected_features, X.shape[1]))
        else:
            # Fallback: use all features
            self.scaler.fit(X)
            self.feature_selector = np.arange(min(selected_features, X.shape[1]))
        
        # Apply scaling and feature selection
        X_scaled = self.scaler.transform(X)
        if self.feature_selector is not None:
            X_selected = X_scaled[:, self.feature_selector]
        else:
            X_selected = X_scaled
        
        # Extract time steps
        time_steps = final_features['feature_1'].values.astype(int)
        
        # Process labels
        merged = final_features[['txId']].merge(classes_df, on='txId', how='left')
        labels = merged['class'].values
        
        binary_labels = np.full(len(labels), -1, dtype=int)
        for i, label in enumerate(labels):
            if pd.isna(label) or label == 'unknown':
                binary_labels[i] = -1
            elif str(label).strip() == '1':
                binary_labels[i] = 1  # illicit
            elif str(label).strip() == '2':
                binary_labels[i] = 0  # licit
        
        labeled_mask = binary_labels != -1
        
        # Process edges
        valid_nodes = set(node_mapping.keys())
        edges_filtered = edges_df[
            edges_df['txId1'].isin(valid_nodes) & 
            edges_df['txId2'].isin(valid_nodes)
        ].copy()
        
        edges_filtered['src'] = edges_filtered['txId1'].map(node_mapping)
        edges_filtered['dst'] = edges_filtered['txId2'].map(node_mapping)
        
        edge_index = np.array([
            edges_filtered['src'].values,
            edges_filtered['dst'].values
        ], dtype=int)
        
        # Enhanced edge features
        edge_features = self.create_enhanced_edge_features(
            X_selected, edge_index, time_steps
        )
        
        # Temporal splits
        unique_times = np.sort(np.unique(time_steps))
        n_times = len(unique_times)
        train_end = int(n_times * 0.6)
        val_end = int(n_times * 0.8)
        
        train_mask = np.isin(time_steps, unique_times[:train_end])
        val_mask = np.isin(time_steps, unique_times[train_end:val_end])
        test_mask = np.isin(time_steps, unique_times[val_end:])
        
        # Convert to tensors
        data = {
            'x': torch.FloatTensor(X_selected),
            'y': torch.LongTensor(binary_labels),
            't': torch.LongTensor(time_steps),
            'edge_index': torch.LongTensor(edge_index),
            'edge_attr': torch.FloatTensor(edge_features),
            'labeled_mask': torch.BoolTensor(labeled_mask),
            'train_mask': torch.BoolTensor(train_mask),
            'val_mask': torch.BoolTensor(val_mask),
            'test_mask': torch.BoolTensor(test_mask),
            'num_nodes': len(node_mapping),
            'num_features': X_selected.shape[1],
            'num_classes': 2
        }
        
        # Calculate class weights
        if labeled_mask.sum() > 0:
            labeled_y = binary_labels[labeled_mask]
            if len(np.unique(labeled_y)) > 1:
                class_weights = compute_class_weight('balanced', classes=np.unique(labeled_y), y=labeled_y)
                class_weight_dict = {0: class_weights[0], 1: class_weights[1]}
            else:
                class_weight_dict = {0: 1.0, 1: 1.0}
        else:
            class_weight_dict = {0: 1.0, 1: 1.0}
        
        data['class_weights'] = class_weight_dict
        
        logger.info("✅ Data preprocessing complete!")
        logger.info(f"   Nodes: {data['num_nodes']:,}")
        logger.info(f"   Edges: {data['edge_index'].size(1):,}")
        logger.info(f"   Features: {data['num_features']}")
        logger.info(f"   Labeled nodes: {data['labeled_mask'].sum():,}")
        
        if labeled_mask.sum() > 0:
            train_labeled = (data['labeled_mask'] & data['train_mask']).sum()
            val_labeled = (data['labeled_mask'] & data['val_mask']).sum() 
            test_labeled = (data['labeled_mask'] & data['test_mask']).sum()
            logger.info(f"   Train/Val/Test labeled: {train_labeled}/{val_labeled}/{test_labeled}")
        
        return data
    
    def create_enhanced_edge_features(self, X: np.ndarray, edge_index: np.ndarray, 
                                    timestamps: np.ndarray) -> np.ndarray:
        """Create rich edge features from graph structure."""
        n_edges = edge_index.shape[1]
        edge_features = np.zeros((n_edges, 8))
        
        if n_edges == 0:
            return edge_features
        
        # Node degrees
        n_nodes = X.shape[0]
        in_degree = np.bincount(edge_index[1], minlength=n_nodes)
        out_degree = np.bincount(edge_index[0], minlength=n_nodes)
        
        for i in range(n_edges):
            src, dst = edge_index[0, i], edge_index[1, i]
            
            if src < n_nodes and dst < n_nodes:
                # Degree-based features
                edge_features[i, 0] = out_degree[src] / max(out_degree.max(), 1)
                edge_features[i, 1] = in_degree[dst] / max(in_degree.max(), 1) 
                
                # Temporal features
                time_diff = abs(timestamps[src] - timestamps[dst]) if src < len(timestamps) and dst < len(timestamps) else 0
                edge_features[i, 2] = min(time_diff / 50.0, 1.0)
                
                # Feature similarity
                if X.shape[1] > 0:
                    feat_dim = min(10, X.shape[1])
                    src_feat = X[src, :feat_dim]
                    dst_feat = X[dst, :feat_dim]
                    if np.std(src_feat) > 0 and np.std(dst_feat) > 0:
                        similarity = np.corrcoef(src_feat, dst_feat)[0, 1]
                        edge_features[i, 3] = np.nan_to_num(similarity, nan=0.0)
                
                # Topology features
                edge_features[i, 4] = min(out_degree[src] + in_degree[dst], 100) / 100.0
                edge_features[i, 5] = 1.0 if out_degree[src] > np.mean(out_degree) else 0.0
                edge_features[i, 6] = 1.0 if in_degree[dst] > np.mean(in_degree) else 0.0
                edge_features[i, 7] = (timestamps[src] % 50) / 50.0 if src < len(timestamps) else 0.0
        
        return edge_features

class OptimizedTGATTrainer:
    """Optimized TGAT trainer with advanced techniques."""
    
    def __init__(self, data: Dict, device: str = None):
        self.data = data
        self.device = device or ('cuda' if torch.cuda.is_available() else 'cpu')
        self.model = None
        self.optimizer = None
        self.scheduler = None
        
        # Use Focal Loss for class imbalance
        self.criterion = FocalLoss(alpha=2.0, gamma=2.0)
        
        # Move data to device
        for key, value in self.data.items():
            if isinstance(value, torch.Tensor):
                self.data[key] = value.to(self.device)
    
    def create_model(self, hidden_dim: int = 64):
        """Create optimized TGAT model."""
        logger.info("Creating optimized TGAT model...")
        
        # Create adaptive model that handles the actual feature dimension
        self.model = AdaptiveTGATModel(
            input_dim=self.data['num_features'],
            hidden_dim=hidden_dim,
            selected_features=min(64, self.data['num_features']),
            use_feature_selection=True
        ).to(self.device)
        
        # Optimizer with learning rate scheduling
        self.optimizer = optim.AdamW(self.model.parameters(), lr=0.001, weight_decay=1e-4)
        self.scheduler = optim.lr_scheduler.ReduceLROnPlateau(
            self.optimizer, mode='max', factor=0.5, patience=5, verbose=True
        )
        
        # Count parameters
        total_params = sum(p.numel() for p in self.model.parameters())
        logger.info(f"Model created with {total_params:,} parameters")
    
    def evaluate(self, mask: torch.Tensor) -> Dict[str, float]:
        """Enhanced evaluation with multiple metrics."""
        self.model.eval()
        
        with torch.no_grad():
            labeled_mask = self.data['labeled_mask'] & mask
            
            if labeled_mask.sum() == 0:
                return {'accuracy': 0.0, 'auc': 0.0, 'f1': 0.0, 'precision': 0.0, 'recall': 0.0}
            
            try:
                outputs = self.model(
                    self.data['x'],
                    self.data['edge_index'],
                    self.data['edge_attr'],
                    self.data['t'][self.data['edge_index'][0]],
                    self.data['t']
                )
                
                logits = outputs['node_predictions']
            except Exception as e:
                logger.warning(f"Model evaluation failed: {e}")
                return {'accuracy': 0.0, 'auc': 0.0, 'f1': 0.0, 'precision': 0.0, 'recall': 0.0}
            
            # Get predictions
            y_true = self.data['y'][labeled_mask].cpu().numpy()
            y_pred = logits[labeled_mask].argmax(dim=1).cpu().numpy()
            y_prob = torch.softmax(logits[labeled_mask], dim=1)[:, 1].cpu().numpy()
            
            # Calculate metrics
            metrics = {}
            metrics['accuracy'] = accuracy_score(y_true, y_pred)
            
            try:
                metrics['auc'] = roc_auc_score(y_true, y_prob) if len(np.unique(y_true)) > 1 else 0.0
            except:
                metrics['auc'] = 0.0
            
            try:
                metrics['precision'] = precision_score(y_true, y_pred, average='binary', zero_division=0)
                metrics['recall'] = recall_score(y_true, y_pred, average='binary', zero_division=0)
                metrics['f1'] = f1_score(y_true, y_pred, average='binary', zero_division=0)
            except:
                metrics['precision'] = 0.0
                metrics['recall'] = 0.0
                metrics['f1'] = 0.0
        
        return metrics
    
    def train_epoch(self) -> float:
        """Enhanced training epoch."""
        self.model.train()
        
        labeled_train_mask = self.data['labeled_mask'] & self.data['train_mask']
        
        if labeled_train_mask.sum() == 0:
            return 0.0
        
        self.optimizer.zero_grad()
        
        try:
            outputs = self.model(
                self.data['x'],
                self.data['edge_index'],
                self.data['edge_attr'],
                self.data['t'][self.data['edge_index'][0]],
                self.data['t']
            )
            
            logits = outputs['node_predictions']
            loss = self.criterion(logits[labeled_train_mask], self.data['y'][labeled_train_mask])
            
        except Exception as e:
            logger.warning(f"Training forward pass failed: {e}")
            return 0.0
        
        loss.backward()
        torch.nn.utils.clip_grad_norm_(self.model.parameters(), max_norm=1.0)
        self.optimizer.step()
        
        return loss.item()
    
    def train(self, epochs: int = 100, patience: int = 15):
        """Enhanced training with better monitoring."""
        logger.info(f"Starting optimized training for {epochs} epochs...")
        
        best_val_auc = 0.0
        patience_counter = 0
        
        for epoch in range(epochs):
            start_time = time.time()
            
            # Training
            train_loss = self.train_epoch()
            
            # Evaluation
            if epoch % 3 == 0:
                train_metrics = self.evaluate(self.data['train_mask'])
                val_metrics = self.evaluate(self.data['val_mask'])
                
                # Learning rate scheduling
                self.scheduler.step(val_metrics['auc'])
                
                epoch_time = time.time() - start_time
                
                logger.info(f"Epoch {epoch:3d}/{epochs} | "
                          f"Loss: {train_loss:.4f} | "
                          f"Train AUC: {train_metrics['auc']:.4f} | "
                          f"Val AUC: {val_metrics['auc']:.4f} | "
                          f"Val F1: {val_metrics['f1']:.4f} | "
                          f"Time: {epoch_time:.2f}s")
                
                # Early stopping based on AUC
                if val_metrics['auc'] > best_val_auc:
                    best_val_auc = val_metrics['auc']
                    patience_counter = 0
                    torch.save(self.model.state_dict(), 'best_optimized_tgat.pth')
                else:
                    patience_counter += 1
                
                if patience_counter >= patience:
                    logger.info(f"Early stopping at epoch {epoch}")
                    break
        
        # Final evaluation
        if os.path.exists('best_optimized_tgat.pth'):
            self.model.load_state_dict(torch.load('best_optimized_tgat.pth'))
        
        final_metrics = self.evaluate(self.data['test_mask'])
        
        logger.info("🎯 Final Optimized Results:")
        logger.info(f"   Test Accuracy: {final_metrics['accuracy']:.4f}")
        logger.info(f"   Test AUC: {final_metrics['auc']:.4f}")
        logger.info(f"   Test F1: {final_metrics['f1']:.4f}")
        logger.info(f"   Test Precision: {final_metrics['precision']:.4f}")
        logger.info(f"   Test Recall: {final_metrics['recall']:.4f}")
        
        return final_metrics

def main():
    """Main optimized training function."""
    print("🚀 Optimized TGAT Training on Elliptic Dataset")
    
    if not os.path.exists("dataset copy"):
        print("❌ Dataset not found!")
        return
    
    try:
        # Load and analyze data
        print("\n📊 Loading and optimizing dataset...")
        loader = OptimizedEllipticLoader()
        data = loader.load_and_analyze(
            max_nodes=15000,     # Larger dataset for better performance
            min_labeled=3000,    # More labeled samples
            selected_features=64  # Optimized feature count
        )
        
        if data['labeled_mask'].sum() == 0:
            print("❌ No labeled data found!")
            return
        
        # Create and train model
        device = 'cuda' if torch.cuda.is_available() else 'cpu'
        print(f"\n🤖 Creating optimized TGAT model (device: {device})...")
        
        trainer = OptimizedTGATTrainer(data, device=device)
        trainer.create_model(hidden_dim=64)
        
        print("\n🏋️ Starting optimized training...")
        results = trainer.train(epochs=80, patience=20)
        
        print("\n✅ Optimized training complete!")
        
        # Print dataset insights
        labeled_count = data['labeled_mask'].sum()
        if labeled_count > 0:
            illicit_count = (data['y'][data['labeled_mask']] == 1).sum()
            print(f"\n📊 Dataset Insights:")
            print(f"   Total nodes: {data['num_nodes']:,}")
            print(f"   Labeled nodes: {labeled_count:,}")
            print(f"   Illicit transactions: {illicit_count:,} ({illicit_count/labeled_count*100:.1f}%)")
            print(f"   Class weights: {data['class_weights']}")
        
    except Exception as e:
        logger.error(f"Optimized training failed: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main() 