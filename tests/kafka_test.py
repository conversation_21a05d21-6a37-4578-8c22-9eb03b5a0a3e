from pyspark.sql import SparkSession
from pyspark.sql.functions import *
from pyspark.sql.types import *
import time

# Create Spark Session
spark = SparkSession \
    .builder \
    .appName("KafkaTest") \
    .config("spark.jars.packages", "org.apache.spark:spark-sql-kafka-0-10_2.12:3.5.0") \
    .getOrCreate()

# Set log level
spark.sparkContext.setLogLevel("WARN")

print("Spark session created")

# Define Kafka parameters
kafka_bootstrap_servers = "kafka:29092"
kafka_topic = "filtered-transactions-copy"

print(f"Connecting to Kafka: {kafka_bootstrap_servers}, topic: {kafka_topic}")

# Create DataFrame representing the stream of input lines from Kafka
df = spark \
    .readStream \
    .format("kafka") \
    .option("kafka.bootstrap.servers", kafka_bootstrap_servers) \
    .option("subscribe", kafka_topic) \
    .option("startingOffsets", "earliest") \
    .load()

print("Kafka source created")

# Convert the binary value to string
df = df.selectExpr("CAST(key AS STRING)", "CAST(value AS STRING)")

print("Schema:")
df.printSchema()

# Write to console
query = df \
    .writeStream \
    .outputMode("append") \
    .format("console") \
    .start()

print("Query started")

# Wait for termination
try:
    query.awaitTermination(60)  # Wait for 60 seconds
except KeyboardInterrupt:
    print("Stopping...")
finally:
    query.stop()
    spark.stop()
    print("Done") 