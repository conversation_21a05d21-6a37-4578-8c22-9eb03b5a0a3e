#!/usr/bin/env python3
"""
Pipeline Integration Test

This script tests the integration between the CEP layer and Graph Construction Layer
by validating Kafka connectivity and topic configuration.
"""

import json
import time
import logging
from datetime import datetime
from kafka import KafkaProducer, KafkaConsumer, KafkaAdminClient
from kafka.admin import ConfigResource, ConfigResourceType, NewTopic
from kafka.errors import TopicAlreadyExistsError
import sys

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


def test_kafka_connectivity():
    """Test basic Kafka connectivity"""
    logger.info("🧪 Testing Kafka Connectivity")
    
    try:
        # Test admin client
        admin = KafkaAdminClient(
            bootstrap_servers=['localhost:9092'],
            client_id='graph_layer_test'
        )
        
        # List topics
        topic_metadata = admin.list_topics()
        logger.info(f"✅ Kafka connection successful!")
        logger.info(f"   - Available topics: {list(topic_metadata)}")
        
        admin.close()
        return True
        
    except Exception as e:
        logger.error(f"❌ Kafka connectivity test failed: {str(e)}")
        return False


def create_test_topics():
    """Create required topics for Graph Construction Layer"""
    logger.info("🧪 Creating Test Topics")
    
    try:
        admin = KafkaAdminClient(
            bootstrap_servers=['localhost:9092'],
            client_id='graph_layer_test'
        )
        
        # Define topics
        topics = [
            NewTopic(
                name="filtered-transactions",
                num_partitions=3,
                replication_factor=1
            ),
            NewTopic(
                name="graph-snapshots",
                num_partitions=3,
                replication_factor=1
            )
        ]
        
        # Create topics
        try:
            result = admin.create_topics(topics)
            for topic, future in result.items():
                future.result()  # Wait for completion
                logger.info(f"✅ Topic '{topic}' created successfully")
        except TopicAlreadyExistsError:
            logger.info("✅ Topics already exist (expected)")
        
        admin.close()
        return True
        
    except Exception as e:
        logger.error(f"❌ Topic creation failed: {str(e)}")
        return False


def test_producer():
    """Test producing mock filtered transactions"""
    logger.info("🧪 Testing Kafka Producer")
    
    try:
        producer = KafkaProducer(
            bootstrap_servers=['localhost:9092'],
            value_serializer=lambda v: json.dumps(v).encode('utf-8'),
            client_id='graph_layer_test_producer'
        )
        
        # Create mock filtered transaction
        mock_transaction = {
            'hash': '0x123456789abcdef',
            'from_address': '0xABC123',
            'to_address': '0xDEF456',
            'value': 1.5,
            'timestamp': int(datetime.now().timestamp()),
            'block_number': 12345,
            'gas_used': 21000,
            'gas_price': 20000000000,
            'method_id': '0x',
            'is_contract': False,
            'risk_score': 0.75,
            'pattern_name': 'test_pattern',
            'alert_id': 'alert_test_123',
            'severity': 'HIGH'
        }
        
        # Send message
        future = producer.send('filtered-transactions', mock_transaction)
        record_metadata = future.get(timeout=10)
        
        logger.info(f"✅ Message sent successfully!")
        logger.info(f"   - Topic: {record_metadata.topic}")
        logger.info(f"   - Partition: {record_metadata.partition}")
        logger.info(f"   - Offset: {record_metadata.offset}")
        
        producer.close()
        return True
        
    except Exception as e:
        logger.error(f"❌ Producer test failed: {str(e)}")
        return False


def test_consumer():
    """Test consuming from filtered-transactions topic"""
    logger.info("🧪 Testing Kafka Consumer")
    
    try:
        consumer = KafkaConsumer(
            'filtered-transactions',
            bootstrap_servers=['localhost:9092'],
            value_deserializer=lambda m: json.loads(m.decode('utf-8')),
            client_id='graph_layer_test_consumer',
            group_id='graph_layer_test_group',
            auto_offset_reset='earliest',
            consumer_timeout_ms=5000  # 5 second timeout
        )
        
        messages_consumed = 0
        for message in consumer:
            logger.info(f"✅ Consumed message:")
            logger.info(f"   - Key: {message.key}")
            logger.info(f"   - Value: {message.value}")
            logger.info(f"   - Partition: {message.partition}")
            logger.info(f"   - Offset: {message.offset}")
            
            messages_consumed += 1
            if messages_consumed >= 1:  # Only read one message for test
                break
        
        consumer.close()
        
        if messages_consumed > 0:
            logger.info(f"✅ Successfully consumed {messages_consumed} messages")
            return True
        else:
            logger.warning("⚠️ No messages found in topic (this is OK if no data was produced)")
            return True
        
    except Exception as e:
        logger.error(f"❌ Consumer test failed: {str(e)}")
        return False


def test_graph_output_simulation():
    """Test producing mock graph snapshots to output topic"""
    logger.info("🧪 Testing Graph Snapshot Output")
    
    try:
        producer = KafkaProducer(
            bootstrap_servers=['localhost:9092'],
            value_serializer=lambda v: json.dumps(v).encode('utf-8'),
            client_id='graph_layer_test_output'
        )
        
        # Create mock graph snapshot
        mock_graph_snapshot = {
            'batch_id': 1,
            'graph_snapshot': {
                'window_start': datetime.now().isoformat(),
                'window_end': datetime.now().isoformat(),
                'num_nodes': 5,
                'num_edges': 7,
                'max_risk_score': 0.85,
                'total_volume': 12.5,
                'node_features': {
                    '0xABC123': [1.5, 2, 0.75, 1.0, 0.75],
                    '0xDEF456': [2.0, 3, 0.67, 1.0, 0.85],
                    '0xGHI789': [0.5, 1, 0.5, 0.0, 0.2]
                },
                'edge_list': [
                    ['0xABC123', '0xDEF456'],
                    ['0xDEF456', '0xGHI789']
                ]
            },
            'processing_timestamp': datetime.now().isoformat(),
            'layer': 'graph_construction'
        }
        
        # Send message
        future = producer.send('graph-snapshots', mock_graph_snapshot)
        record_metadata = future.get(timeout=10)
        
        logger.info(f"✅ Graph snapshot sent successfully!")
        logger.info(f"   - Topic: {record_metadata.topic}")
        logger.info(f"   - Partition: {record_metadata.partition}")
        logger.info(f"   - Offset: {record_metadata.offset}")
        
        producer.close()
        return True
        
    except Exception as e:
        logger.error(f"❌ Graph output test failed: {str(e)}")
        return False


def test_topic_configuration():
    """Test topic configuration and partitions"""
    logger.info("🧪 Testing Topic Configuration")
    
    try:
        admin = KafkaAdminClient(
            bootstrap_servers=['localhost:9092'],
            client_id='graph_layer_test'
        )
        
        # Check topic configurations
        topics = ['filtered-transactions', 'graph-snapshots']
        
        for topic in topics:
            metadata = admin.describe_topics([topic])
            if topic in metadata:
                topic_meta = metadata[topic]
                logger.info(f"✅ Topic '{topic}' configuration:")
                logger.info(f"   - Partitions: {len(topic_meta.partitions)}")
                logger.info(f"   - Is internal: {topic_meta.is_internal}")
            else:
                logger.warning(f"⚠️ Topic '{topic}' not found")
        
        admin.close()
        return True
        
    except Exception as e:
        logger.error(f"❌ Topic configuration test failed: {str(e)}")
        return False


def main():
    """Run all integration tests"""
    
    logger.info("🚀 Starting Pipeline Integration Tests")
    logger.info("Testing Graph Construction Layer integration with existing pipeline")
    
    tests = [
        ("Kafka Connectivity", test_kafka_connectivity),
        ("Topic Creation", create_test_topics),
        ("Topic Configuration", test_topic_configuration),
        ("Producer Test", test_producer),
        ("Consumer Test", test_consumer),
        ("Graph Output Simulation", test_graph_output_simulation)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        logger.info(f"\n{'='*50}")
        logger.info(f"Running: {test_name}")
        logger.info(f"{'='*50}")
        
        if test_func():
            passed += 1
            logger.info(f"✅ {test_name} - PASSED")
        else:
            logger.error(f"❌ {test_name} - FAILED")
    
    logger.info(f"\n{'='*50}")
    logger.info(f"INTEGRATION TEST RESULTS: {passed}/{total} tests passed")
    logger.info(f"{'='*50}")
    
    if passed == total:
        logger.info("🎉 All integration tests passed!")
        logger.info("✅ Graph Construction Layer is ready for deployment!")
        logger.info("📋 Next steps:")
        logger.info("   1. Deploy Graph Construction Layer: docker-compose up graph-layer")
        logger.info("   2. Monitor Spark UI: http://localhost:4040")
        logger.info("   3. Proceed to TGAT Model Layer implementation")
        return True
    else:
        logger.error(f"❌ {total - passed} tests failed. Please check the pipeline configuration.")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1) 