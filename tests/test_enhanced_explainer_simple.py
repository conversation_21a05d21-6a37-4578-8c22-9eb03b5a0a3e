"""
Simple Test for Enhanced GNN Explainer

Tests core functionality without torch-scatter dependencies.
"""

import sys
import os
import torch
import numpy as np
import logging
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_enhanced_explainer_basic():
    """Test basic enhanced explainer functionality with mock data."""
    logger.info("🚀 Testing Enhanced GNN Explainer - Basic Functionality")
    
    # Create mock data
    num_nodes = 50
    num_features = 64
    num_edges = 100
    
    x = torch.randn(num_nodes, num_features)
    edge_index = torch.randint(0, num_nodes, (2, num_edges))
    edge_attr = torch.randn(num_edges, 8)
    edge_time = torch.randint(1, 100, (num_edges,)).float()
    
    # Mock TGAT model
    class MockTGATModel(torch.nn.Module):
        def __init__(self):
            super().__init__()
            self.linear = torch.nn.Linear(num_features, 2)
            
        def forward(self, x, edge_index, edge_attr, edge_time):
            return self.linear(x)
    
    model = MockTGATModel()
    model.eval()
    
    # Test 1: Temporal Motif Explainer
    logger.info("🧬 Testing Temporal Motif Discovery...")
    try:
        from gnn_explainer_layer.enhanced_gnn_explainer import TemporalMotifExplainer
        
        motif_explainer = TemporalMotifExplainer(model, num_hops=2, device='cpu')
        target_node = 5
        
        motifs = motif_explainer.discover_temporal_motifs(
            x, edge_index, edge_time, target_node
        )
        
        logger.info(f"  ✅ Discovered {len(motifs)} temporal motifs")
        
        if len(motifs) > 0:
            motif = motifs[0]
            logger.info(f"  Top motif: {len(motif['nodes'])} nodes, importance: {motif.get('importance_score', 'N/A')}")
            
    except ImportError as e:
        logger.warning(f"  ⚠️ Temporal Motif Explainer test skipped: {e}")
    except Exception as e:
        logger.error(f"  ❌ Temporal Motif Explainer test failed: {e}")
    
    # Test 2: Information Bottleneck Explainer
    logger.info("🧠 Testing Information Bottleneck Analysis...")
    try:
        from gnn_explainer_layer.enhanced_gnn_explainer import InformationBottleneckExplainer
        
        bottleneck_explainer = InformationBottleneckExplainer(model, beta=0.01, device='cpu')
        target_node = 10
        
        explanation = bottleneck_explainer.explain_with_bottleneck(
            x, edge_index, edge_attr, edge_time, target_node, epochs=5
        )
        
        logger.info(f"  ✅ Information bottleneck analysis complete")
        logger.info(f"  Prediction loss: {explanation.get('prediction_loss', 'N/A'):.4f}")
        logger.info(f"  Information loss: {explanation.get('info_loss', 'N/A'):.4f}")
        
    except ImportError as e:
        logger.warning(f"  ⚠️ Information Bottleneck test skipped: {e}")
    except Exception as e:
        logger.error(f"  ❌ Information Bottleneck test failed: {e}")
    
    # Test 3: Advanced AML Explainer
    logger.info("💼 Testing Advanced AML Explainer...")
    try:
        from gnn_explainer_layer.enhanced_gnn_explainer import AdvancedAMLExplainer
        
        aml_explainer = AdvancedAMLExplainer(model, device='cpu')
        target_node = 15
        
        explanation = aml_explainer.generate_comprehensive_explanation(
            x, edge_index, edge_attr, edge_time, target_node
        )
        
        logger.info(f"  ✅ Comprehensive explanation generated")
        
        # Validate explanation structure
        assert isinstance(explanation, dict), "Explanation should be a dictionary"
        
        required_keys = [
            'node_id', 'temporal_motifs', 'information_bottleneck',
            'feature_importance', 'risk_assessment', 'compliance_report'
        ]
        
        for key in required_keys:
            assert key in explanation, f"Missing key: {key}"
        
        # Check risk assessment
        risk_assessment = explanation['risk_assessment']
        risk_level = risk_assessment['overall_risk']['level']
        risk_score = risk_assessment['overall_risk']['score']
        
        logger.info(f"  Risk Level: {risk_level}")
        logger.info(f"  Risk Score: {risk_score:.3f}")
        
        # Check feature importance
        feature_importance = explanation['feature_importance']
        top_features = feature_importance['top_features']
        
        logger.info(f"  Top risk factors: {len(top_features)}")
        if len(top_features) > 0:
            logger.info(f"  #1 Risk factor: {top_features[0]['feature_name']}")
        
        # Check compliance report
        compliance_report = explanation['compliance_report']
        actions = compliance_report['recommended_actions']
        logger.info(f"  Recommended actions: {len(actions)}")
        
    except ImportError as e:
        logger.warning(f"  ⚠️ Advanced AML Explainer test skipped: {e}")
    except Exception as e:
        logger.error(f"  ❌ Advanced AML Explainer test failed: {e}")
    
    # Test 4: Feature Analysis
    logger.info("🎯 Testing Feature Analysis...")
    try:
        from gnn_explainer_layer.enhanced_gnn_explainer import AdvancedAMLExplainer
        
        aml_explainer = AdvancedAMLExplainer(model, device='cpu')
        
        # Test feature importance analysis
        feature_importance = aml_explainer._analyze_feature_importance(
            x, edge_index, edge_attr, edge_time, target_node=20
        )
        
        logger.info(f"  ✅ Feature importance analysis complete")
        logger.info(f"  Analyzed {len(feature_importance['all_features'])} features")
        logger.info(f"  Original prediction: {feature_importance['original_prediction']:.3f}")
        
        # Check top features
        top_features = feature_importance['top_features'][:3]
        for i, feature in enumerate(top_features):
            logger.info(f"  #{i+1}: {feature['feature_name']} (importance: {feature['importance_score']:.3f})")
            
    except Exception as e:
        logger.error(f"  ❌ Feature Analysis test failed: {e}")
    
    logger.info("🎉 Enhanced GNN Explainer Basic Test Complete!")

def test_real_tgat_integration():
    """Test integration with real TGAT model if available."""
    logger.info("🔗 Testing Real TGAT Model Integration...")
    
    try:
        # Try to load real TGAT model
        from tgat_layer.working_tgat_elliptic import WorkingTGATModel, EllipticDataLoader
        
        # Load small dataset sample
        data_loader = EllipticDataLoader("dataset copy")
        data = data_loader.load_data(max_nodes=200, min_labeled=100)
        
        # Load model
        model = WorkingTGATModel(input_dim=64, hidden_dim=64, edge_dim=8, num_classes=2)
        
        # Try to load pretrained weights
        model_path = "models/best_working_tgat.pth"
        if os.path.exists(model_path):
            try:
                state_dict = torch.load(model_path, map_location='cpu')
                model.load_state_dict(state_dict, strict=False)
                logger.info("  ✅ Loaded pretrained TGAT model")
            except Exception as e:
                logger.warning(f"  ⚠️ Could not load pretrained weights: {e}")
        
        model.eval()
        
        # Test with real data
        x = data['x']
        edge_index = data['edge_index']
        edge_attr = data['edge_attr']
        edge_time = data['edge_time']
        
        # Get model predictions
        with torch.no_grad():
            predictions = model(x, edge_index, edge_attr, edge_time)
            probabilities = torch.softmax(predictions, dim=1)
            suspicious_scores = probabilities[:, 1]
        
        # Find a high-risk node
        high_risk_nodes = torch.where(suspicious_scores > 0.6)[0]
        
        if len(high_risk_nodes) > 0:
            target_node = int(high_risk_nodes[0].item())
            
            # Test explanation
            from gnn_explainer_layer.enhanced_gnn_explainer import AdvancedAMLExplainer
            
            explainer = AdvancedAMLExplainer(model, device='cpu')
            explanation = explainer.generate_comprehensive_explanation(
                x, edge_index, edge_attr, edge_time, target_node
            )
            
            logger.info(f"  ✅ Real TGAT explanation generated for node {target_node}")
            logger.info(f"  Risk Level: {explanation['risk_assessment']['overall_risk']['level']}")
            logger.info(f"  Risk Score: {explanation['risk_assessment']['overall_risk']['score']:.3f}")
            logger.info(f"  Temporal Motifs: {len(explanation['temporal_motifs'])}")
            
        else:
            logger.warning("  ⚠️ No high-risk nodes found in sample")
        
        logger.info("  ✅ Real TGAT integration successful")
        
    except ImportError as e:
        logger.warning(f"  ⚠️ Real TGAT integration test skipped: {e}")
    except Exception as e:
        logger.error(f"  ❌ Real TGAT integration test failed: {e}")

def test_performance_benchmarks():
    """Test performance characteristics."""
    logger.info("⏱️ Testing Performance Benchmarks...")
    
    import time
    
    try:
        # Create larger test data
        num_nodes = 500
        num_features = 64
        num_edges = 1000
        
        x = torch.randn(num_nodes, num_features)
        edge_index = torch.randint(0, num_nodes, (2, num_edges))
        edge_attr = torch.randn(num_edges, 8)
        edge_time = torch.randint(1, 100, (num_edges,)).float()
        
        # Mock model
        class MockTGATModel(torch.nn.Module):
            def __init__(self):
                super().__init__()
                self.linear = torch.nn.Linear(num_features, 2)
                
            def forward(self, x, edge_index, edge_attr, edge_time):
                return self.linear(x)
        
        model = MockTGATModel()
        model.eval()
        
        from gnn_explainer_layer.enhanced_gnn_explainer import AdvancedAMLExplainer
        
        explainer = AdvancedAMLExplainer(model, device='cpu')
        target_node = 100
        
        # Benchmark explanation generation
        start_time = time.time()
        
        explanation = explainer.generate_comprehensive_explanation(
            x, edge_index, edge_attr, edge_time, target_node
        )
        
        end_time = time.time()
        explanation_time = end_time - start_time
        
        logger.info(f"  ✅ Performance benchmark complete")
        logger.info(f"  Explanation time: {explanation_time:.2f} seconds")
        logger.info(f"  Nodes processed: {num_nodes}")
        logger.info(f"  Edges processed: {num_edges}")
        logger.info(f"  Throughput: {num_nodes/explanation_time:.1f} nodes/second")
        
        # Memory usage
        import psutil
        process = psutil.Process(os.getpid())
        memory_mb = process.memory_info().rss / 1024 / 1024
        logger.info(f"  Memory usage: {memory_mb:.1f} MB")
        
    except Exception as e:
        logger.error(f"  ❌ Performance benchmark failed: {e}")

if __name__ == "__main__":
    print("=" * 60)
    print("🔬 ENHANCED GNN EXPLAINER - SIMPLE TEST SUITE")
    print("=" * 60)
    
    test_enhanced_explainer_basic()
    print("\n" + "-" * 40 + "\n")
    
    test_real_tgat_integration()
    print("\n" + "-" * 40 + "\n")
    
    test_performance_benchmarks()
    
    print("\n" + "=" * 60)
    print("✅ Enhanced GNN Explainer Test Suite Complete!")
    print("=" * 60) 