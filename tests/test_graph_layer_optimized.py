#!/usr/bin/env python3
"""
Direct Test of Optimized Graph Construction Layer

This script tests the performance-optimized Graph Construction Layer
by consuming live Sepolia transactions and measuring performance.
"""

import os
import sys
import time
import json
import logging
import pandas as pd
import numpy as np
from datetime import datetime, timed<PERSON>ta
from typing import Dict, Any, List
from kafka import KafkaConsumer
from kafka.errors import KafkaError

# Add src directory to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.graph_layer.graph_constructor import Context7GraphConstructor
from src.graph_layer.config.settings import GraphLayerSettings

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class OptimizedGraphLayerTester:
    """Test the optimized Graph Construction Layer with live data"""
    
    def __init__(self):
        self.kafka_servers = "localhost:9092"
        self.input_topic = "ethereum-transactions"
        
        # Initialize optimized settings
        self.settings = GraphLayerSettings(
            # Performance optimizations
            enable_vectorized_operations=True,
            enable_feature_caching=True,
            enable_graph_pruning=True,
            enable_performance_logging=True,
            enable_detailed_metrics=True,
            
            # Optimized parameters
            max_nodes_per_graph=5000,
            edge_weight_threshold=0.001,
            node_feature_dim=32,
            batch_size=2000,
            parallelism=8,
            
            # Fast processing
            graph_construction_timeout=15,
            trigger_processing_time="10 seconds",
            window_duration="5 minutes",
            sliding_duration="30 seconds"
        )
        
        # Initialize optimized graph constructor
        self.graph_constructor = Context7GraphConstructor(self.settings.__dict__)
        
        # Performance tracking
        self.performance_metrics = []
        self.construction_times = []
        
    def consume_live_transactions(self, duration_seconds: int = 300) -> List[Dict[str, Any]]:
        """Consume live transactions from Kafka"""
        
        logger.info(f"Consuming live transactions for {duration_seconds} seconds...")
        
        try:
            consumer = KafkaConsumer(
                self.input_topic,
                bootstrap_servers=self.kafka_servers,
                value_deserializer=lambda m: json.loads(m.decode('utf-8')),
                auto_offset_reset='latest',
                enable_auto_commit=True,
                group_id='graph-layer-test-consumer'
            )
            
            transactions = []
            start_time = time.time()
            
            for message in consumer:
                try:
                    tx_data = message.value
                    
                    # Convert to graph constructor format
                    processed_tx = {
                        'hash': tx_data.get('hash', ''),
                        'from_address': tx_data.get('from_address', ''),
                        'to_address': tx_data.get('to_address', ''),
                        'value': float(tx_data.get('value', 0)) / 1e18,  # Convert to ETH
                        'timestamp': int(tx_data.get('timestamp', time.time())),
                        'block_number': tx_data.get('block_number', 0),
                        'gas_used': tx_data.get('gas_used', 0),
                        'gas_price': tx_data.get('gas_price', 0),
                        'method_id': tx_data.get('input_data', '')[:10] if tx_data.get('input_data') else None,
                        'is_contract': len(tx_data.get('input_data', '')) > 2,
                        'round_amount': self._is_round_amount(float(tx_data.get('value', 0)) / 1e18),
                        'high_value': float(tx_data.get('value', 0)) / 1e18 > 0.1,
                        'micro_amount': float(tx_data.get('value', 0)) / 1e18 < 0.001
                    }
                    
                    transactions.append(processed_tx)
                    
                    if len(transactions) % 100 == 0:
                        logger.info(f"Collected {len(transactions)} transactions...")
                    
                except Exception as e:
                    logger.warning(f"Error processing transaction: {str(e)}")
                
                # Stop after duration
                if time.time() - start_time > duration_seconds:
                    break
            
            consumer.close()
            logger.info(f"Collected {len(transactions)} live transactions")
            return transactions
            
        except Exception as e:
            logger.error(f"Error consuming transactions: {str(e)}")
            return []
    
    def _is_round_amount(self, value_eth: float) -> bool:
        """Check if value is a round amount"""
        round_amounts = [0.001, 0.01, 0.05, 0.1, 0.5, 1.0, 5.0, 10.0]
        return any(abs(value_eth - amount) < 0.0001 for amount in round_amounts)
    
    def test_graph_construction_performance(self, transactions: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Test graph construction performance with different batch sizes"""
        
        logger.info("Testing graph construction performance...")
        
        batch_sizes = [50, 100, 200, 500, 1000]
        results = {}
        
        for batch_size in batch_sizes:
            if len(transactions) < batch_size:
                continue
                
            logger.info(f"Testing batch size: {batch_size}")
            
            # Take a batch of transactions
            batch = transactions[:batch_size]
            df = pd.DataFrame(batch)
            
            # Test multiple runs for statistical significance
            times = []
            for run in range(5):
                start_time = time.time()
                
                try:
                    snapshot = self.graph_constructor.build_graph_from_transactions(
                        df,
                        datetime.now() - timedelta(minutes=5),
                        datetime.now()
                    )
                    
                    construction_time = (time.time() - start_time) * 1000
                    times.append(construction_time)
                    
                    if run == 0:  # Store detailed metrics from first run
                        results[f'batch_{batch_size}'] = {
                            'avg_construction_time_ms': 0,  # Will be calculated below
                            'nodes': snapshot.num_nodes,
                            'edges': snapshot.num_edges,
                            'total_volume': snapshot.total_volume,
                            'processing_stats': snapshot.processing_stats,
                            'metadata': snapshot.metadata,
                            'meets_latency_target': False  # Will be calculated below
                        }
                    
                except Exception as e:
                    logger.error(f"Error in batch {batch_size}, run {run}: {str(e)}")
                    times.append(float('inf'))
            
            # Calculate statistics
            valid_times = [t for t in times if t != float('inf')]
            if valid_times:
                avg_time = np.mean(valid_times)
                results[f'batch_{batch_size}']['avg_construction_time_ms'] = avg_time
                results[f'batch_{batch_size}']['meets_latency_target'] = avg_time < 500
                results[f'batch_{batch_size}']['p95_time_ms'] = np.percentile(valid_times, 95)
                results[f'batch_{batch_size}']['throughput_tx_per_sec'] = batch_size / (avg_time / 1000)
                
                logger.info(f"Batch {batch_size}: {avg_time:.1f}ms avg, {results[f'batch_{batch_size}']['throughput_tx_per_sec']:.1f} tx/s")
        
        return results
    
    def test_optimization_features(self, transactions: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Test individual optimization features"""
        
        logger.info("Testing optimization features...")
        
        if len(transactions) < 200:
            logger.warning("Not enough transactions for optimization testing")
            return {}
        
        test_batch = transactions[:200]
        df = pd.DataFrame(test_batch)
        
        optimization_tests = {
            'vectorized_operations': {
                'enable_vectorized_operations': True,
                'enable_feature_caching': False,
                'enable_graph_pruning': False
            },
            'feature_caching': {
                'enable_vectorized_operations': False,
                'enable_feature_caching': True,
                'enable_graph_pruning': False
            },
            'graph_pruning': {
                'enable_vectorized_operations': False,
                'enable_feature_caching': False,
                'enable_graph_pruning': True
            },
            'all_optimizations': {
                'enable_vectorized_operations': True,
                'enable_feature_caching': True,
                'enable_graph_pruning': True
            },
            'no_optimizations': {
                'enable_vectorized_operations': False,
                'enable_feature_caching': False,
                'enable_graph_pruning': False
            }
        }
        
        results = {}
        
        for test_name, config in optimization_tests.items():
            logger.info(f"Testing {test_name}...")
            
            # Create constructor with specific configuration
            test_config = self.settings.__dict__.copy()
            test_config.update(config)
            test_constructor = Context7GraphConstructor(test_config)
            
            # Run test
            times = []
            for run in range(3):
                start_time = time.time()
                
                try:
                    snapshot = test_constructor.build_graph_from_transactions(
                        df,
                        datetime.now() - timedelta(minutes=5),
                        datetime.now()
                    )
                    
                    construction_time = (time.time() - start_time) * 1000
                    times.append(construction_time)
                    
                except Exception as e:
                    logger.error(f"Error in {test_name}, run {run}: {str(e)}")
                    times.append(float('inf'))
            
            # Calculate results
            valid_times = [t for t in times if t != float('inf')]
            if valid_times:
                results[test_name] = {
                    'avg_time_ms': np.mean(valid_times),
                    'min_time_ms': np.min(valid_times),
                    'max_time_ms': np.max(valid_times),
                    'meets_target': np.mean(valid_times) < 500,
                    'config': config
                }
                
                logger.info(f"{test_name}: {np.mean(valid_times):.1f}ms avg")
        
        return results
    
    def generate_performance_report(self, batch_results: Dict[str, Any], 
                                  optimization_results: Dict[str, Any]) -> Dict[str, Any]:
        """Generate comprehensive performance report"""
        
        report = {
            'test_timestamp': datetime.now().isoformat(),
            'test_configuration': {
                'kafka_servers': self.kafka_servers,
                'input_topic': self.input_topic,
                'settings': self.settings.__dict__
            },
            'batch_performance': batch_results,
            'optimization_performance': optimization_results,
            'summary': {},
            'recommendations': []
        }
        
        # Analyze batch performance
        successful_batches = [k for k, v in batch_results.items() if v.get('meets_latency_target', False)]
        
        if successful_batches:
            best_batch = max(successful_batches, 
                           key=lambda k: batch_results[k].get('throughput_tx_per_sec', 0))
            
            report['summary']['best_performing_batch'] = {
                'batch_size': best_batch.split('_')[1],
                'avg_time_ms': batch_results[best_batch]['avg_construction_time_ms'],
                'throughput_tx_per_sec': batch_results[best_batch]['throughput_tx_per_sec'],
                'meets_target': True
            }
        
        # Analyze optimization impact
        if optimization_results:
            baseline = optimization_results.get('no_optimizations', {}).get('avg_time_ms', float('inf'))
            optimized = optimization_results.get('all_optimizations', {}).get('avg_time_ms', float('inf'))
            
            if baseline != float('inf') and optimized != float('inf'):
                improvement = ((baseline - optimized) / baseline) * 100
                report['summary']['optimization_improvement'] = {
                    'baseline_ms': baseline,
                    'optimized_ms': optimized,
                    'improvement_percent': improvement
                }
        
        # Generate recommendations
        recommendations = []
        
        if not successful_batches:
            recommendations.append("No batch sizes met the 500ms latency target. Consider reducing batch sizes or optimizing further.")
        
        if optimization_results.get('vectorized_operations', {}).get('meets_target', False):
            recommendations.append("Vectorized operations show good performance. Keep enabled.")
        
        if optimization_results.get('feature_caching', {}).get('meets_target', False):
            recommendations.append("Feature caching improves performance. Keep enabled.")
        
        if optimization_results.get('graph_pruning', {}).get('meets_target', False):
            recommendations.append("Graph pruning helps with performance. Keep enabled.")
        
        report['recommendations'] = recommendations
        
        return report
    
    def run_comprehensive_test(self) -> Dict[str, Any]:
        """Run comprehensive performance test"""
        
        logger.info("🚀 Starting comprehensive Graph Construction Layer performance test...")
        
        try:
            # Step 1: Consume live transactions
            logger.info("Step 1: Consuming live Sepolia transactions...")
            transactions = self.consume_live_transactions(120)  # 2 minutes
            
            if len(transactions) < 50:
                logger.error("Not enough transactions collected for testing")
                return {'error': 'Insufficient transaction data'}
            
            # Step 2: Test batch performance
            logger.info("Step 2: Testing batch performance...")
            batch_results = self.test_graph_construction_performance(transactions)
            
            # Step 3: Test optimization features
            logger.info("Step 3: Testing optimization features...")
            optimization_results = self.test_optimization_features(transactions)
            
            # Step 4: Generate report
            logger.info("Step 4: Generating performance report...")
            report = self.generate_performance_report(batch_results, optimization_results)
            
            logger.info("✅ Comprehensive test completed!")
            return report
            
        except Exception as e:
            logger.error(f"❌ Test failed: {str(e)}")
            return {'error': str(e)}


def main():
    """Main test function"""
    
    print("🧪 Optimized Graph Construction Layer Performance Test")
    print("=" * 55)
    
    tester = OptimizedGraphLayerTester()
    
    try:
        # Run comprehensive test
        report = tester.run_comprehensive_test()
        
        if 'error' in report:
            print(f"❌ Test failed: {report['error']}")
            return
        
        # Save report
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        report_file = f"graph_layer_performance_test_{timestamp}.json"
        
        with open(report_file, 'w') as f:
            json.dump(report, f, indent=2, default=str)
        
        # Print summary
        print("\n📊 PERFORMANCE TEST RESULTS")
        print("=" * 30)
        
        # Batch performance summary
        if 'best_performing_batch' in report.get('summary', {}):
            best = report['summary']['best_performing_batch']
            print(f"🏆 Best Batch Performance:")
            print(f"  • Batch Size: {best['batch_size']} transactions")
            print(f"  • Avg Time: {best['avg_time_ms']:.1f}ms")
            print(f"  • Throughput: {best['throughput_tx_per_sec']:.1f} tx/s")
            print(f"  • Meets Target: {'✅ Yes' if best['meets_target'] else '❌ No'}")
        
        # Optimization impact
        if 'optimization_improvement' in report.get('summary', {}):
            opt = report['summary']['optimization_improvement']
            print(f"\n⚡ Optimization Impact:")
            print(f"  • Baseline: {opt['baseline_ms']:.1f}ms")
            print(f"  • Optimized: {opt['optimized_ms']:.1f}ms")
            print(f"  • Improvement: {opt['improvement_percent']:.1f}%")
        
        # Recommendations
        recommendations = report.get('recommendations', [])
        if recommendations:
            print(f"\n💡 RECOMMENDATIONS:")
            for i, rec in enumerate(recommendations, 1):
                print(f"  {i}. {rec}")
        
        print(f"\n📄 Detailed report saved to: {report_file}")
        
        # Check if performance targets are met
        batch_results = report.get('batch_performance', {})
        target_met = any(v.get('meets_latency_target', False) for v in batch_results.values())
        
        if target_met:
            print("\n🎉 SUCCESS! The optimized Graph Construction Layer meets the <500ms latency target!")
        else:
            print("\n⚠️  Performance target not met. Consider further optimizations.")
    
    except KeyboardInterrupt:
        print("\n⏹️  Test interrupted by user")
    except Exception as e:
        print(f"\n❌ Test failed: {str(e)}")


if __name__ == "__main__":
    main() 