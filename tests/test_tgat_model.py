#!/usr/bin/env python3
"""
TGAT Model Test Suite

Comprehensive tests for the TGAT model implementation including:
- Unit tests for individual components
- Integration tests with the blockchain AML pipeline
- Performance benchmarks
- Memory usage validation
"""

import torch
import torch.nn as nn
import numpy as np
import pytest
import time
from typing import Dict, List, Tuple

# Import TGAT components
from tgat_model import TGATModel, TGATForBlockchainAML, create_tgat_model_for_pipeline
from tgat_model.time_encoding import TimeEncodingLayer
from tgat_model.memory_module import MemoryModule
from tgat_model.tgat_layer import TGATLayer
from tgat_model.message_aggregator import MessageAggregator


class TGATModelTester:
    """Comprehensive test suite for TGAT model components."""
    
    def __init__(self, device: str = 'cpu'):
        self.device = device
        self.test_results = {}
        
    def create_synthetic_graph_data(self, 
                                   num_nodes: int = 100, 
                                   num_edges: int = 500,
                                   node_feat_dim: int = 10,
                                   edge_feat_dim: int = 8) -> Dict[str, torch.Tensor]:
        """Create synthetic blockchain transaction graph data."""
        # Generate random graph structure
        edge_index = torch.randint(0, num_nodes, (2, num_edges), device=self.device)
        
        # Generate node features (address characteristics)
        node_features = torch.randn(num_nodes, node_feat_dim, device=self.device)
        
        # Generate edge features (transaction characteristics)
        edge_features = torch.randn(num_edges, edge_feat_dim, device=self.device)
        
        # Generate temporal data
        edge_times = torch.randint(1000000, 2000000, (num_edges,), device=self.device).float()
        node_times = torch.zeros(num_nodes, device=self.device)
        
        # Assign node times based on their latest transaction
        for i, (src, dst) in enumerate(edge_index.t()):
            node_times[src] = max(node_times[src].item(), edge_times[i].item())
            node_times[dst] = max(node_times[dst].item(), edge_times[i].item())
        
        return {
            'node_features': node_features,
            'edge_index': edge_index,
            'edge_features': edge_features,
            'edge_times': edge_times,
            'node_times': node_times
        }
    
    def test_time_encoding_layer(self) -> bool:
        """Test the time encoding functionality."""
        print("🕒 Testing Time Encoding Layer...")
        
        try:
            time_dim = 32
            time_encoder = TimeEncodingLayer(time_dim, device=self.device)
            
            # Test basic encoding
            timestamps = torch.randint(1000000, 2000000, (100,), device=self.device).float()
            encoded = time_encoder(timestamps)
            
            # Validate output shape
            assert encoded.shape == (100, time_dim), f"Expected shape (100, {time_dim}), got {encoded.shape}"
            
            # Test different basis functions
            for basis_func in ['cosine', 'time', 'learned']:
                encoder = TimeEncodingLayer(time_dim, basis_func, device=self.device)
                encoded = encoder(timestamps)
                assert encoded.shape == (100, time_dim)
            
            print("✅ Time Encoding Layer: PASSED")
            return True
            
        except Exception as e:
            print(f"❌ Time Encoding Layer: FAILED - {e}")
            return False
    
    def test_memory_module(self) -> bool:
        """Test the memory module functionality."""
        print("🧠 Testing Memory Module...")
        
        try:
            num_nodes = 50
            memory_dim = 64
            time_dim = 32
            message_dim = 128
            
            memory_module = MemoryModule(
                num_nodes, memory_dim, time_dim, message_dim, device=self.device
            )
            
            # Test memory retrieval
            node_ids = torch.tensor([0, 1, 2, 5, 10], device=self.device)
            memory = memory_module.get_memory(node_ids)
            assert memory.shape == (5, memory_dim)
            
            # Test memory update
            messages = torch.randn(5, message_dim, device=self.device)
            timestamps = torch.randint(1000000, 2000000, (5,), device=self.device).float()
            memory_module.update_memory(node_ids, messages, timestamps)
            
            # Test temporal memory queries
            updated_memory = memory_module.get_updated_memory(node_ids, timestamps.max())
            assert updated_memory.shape == (5, memory_dim)
            
            print("✅ Memory Module: PASSED")
            return True
            
        except Exception as e:
            print(f"❌ Memory Module: FAILED - {e}")
            return False
    
    def test_tgat_layer(self) -> bool:
        """Test individual TGAT layer."""
        print("🔄 Testing TGAT Layer...")
        
        try:
            input_dim = 64
            hidden_dim = 128
            time_dim = 32
            
            tgat_layer = TGATLayer(
                input_dim, hidden_dim, time_dim, num_heads=4, device=self.device
            )
            
            # Create test data
            data = self.create_synthetic_graph_data(50, 200, input_dim, 8)
            
            # Test forward pass
            output = tgat_layer(
                data['node_features'],
                data['edge_index'],
                data['edge_times'],
                data['node_times']
            )
            
            assert output.shape == (50, hidden_dim)
            print("✅ TGAT Layer: PASSED")
            return True
            
        except Exception as e:
            print(f"❌ TGAT Layer: FAILED - {e}")
            return False
    
    def test_complete_tgat_model(self) -> bool:
        """Test the complete TGAT model."""
        print("🏗️ Testing Complete TGAT Model...")
        
        try:
            model = TGATModel(
                node_feat_dim=10,
                edge_feat_dim=8,
                hidden_dim=64,
                time_dim=32,
                num_layers=2,
                max_nodes=100,
                device=self.device
            )
            
            # Create test data
            data = self.create_synthetic_graph_data(100, 500, 10, 8)
            
            # Test forward pass
            outputs = model(
                data['node_features'],
                data['edge_index'],
                data['edge_features'],
                data['edge_times'],
                data['node_times']
            )
            
            # Validate outputs
            assert 'node_embeddings' in outputs
            assert 'node_predictions' in outputs
            assert 'graph_prediction' in outputs
            assert outputs['node_embeddings'].shape == (100, 64)
            assert outputs['node_predictions'].shape == (100, 2)
            assert outputs['graph_prediction'].shape == (1, 2)
            
            print("✅ Complete TGAT Model: PASSED")
            return True
            
        except Exception as e:
            print(f"❌ Complete TGAT Model: FAILED - {e}")
            return False
    
    def test_blockchain_aml_model(self) -> bool:
        """Test the blockchain AML specialized model."""
        print("🔗 Testing Blockchain AML Model...")
        
        try:
            model = TGATForBlockchainAML(
                node_feat_dim=10,
                edge_feat_dim=8,
                hidden_dim=64,
                max_nodes=100,
                device=self.device
            )
            
            # Create test data
            data = self.create_synthetic_graph_data(100, 500, 10, 8)
            
            # Test blockchain-specific forward pass
            outputs = model.forward_blockchain_specific(
                data['node_features'],
                data['edge_features'],
                data['edge_index'],
                data['edge_times']
            )
            
            # Validate blockchain-specific outputs
            assert 'address_risk_scores' in outputs
            assert outputs['address_risk_scores'].shape == (100,)
            
            # Test suspicious pattern detection
            transaction_amounts = torch.rand(500, device=self.device) * 1000
            patterns = model.detect_suspicious_patterns(
                outputs, data['edge_index'], transaction_amounts
            )
            
            assert isinstance(patterns, dict)
            assert 'high_risk_addresses' in patterns
            assert 'suspicious_transactions' in patterns
            
            print("✅ Blockchain AML Model: PASSED")
            return True
            
        except Exception as e:
            print(f"❌ Blockchain AML Model: FAILED - {e}")
            return False
    
    def test_anomaly_prediction(self) -> bool:
        """Test anomaly prediction functionality."""
        print("🚨 Testing Anomaly Prediction...")
        
        try:
            model = TGATForBlockchainAML(device=self.device)
            data = self.create_synthetic_graph_data(50, 200, 10, 8)
            
            # Test prediction
            predictions = model.predict_anomaly(
                data['node_features'],
                data['edge_index'],
                data['edge_features'],
                data['edge_times'],
                data['node_times']
            )
            
            # Validate prediction outputs
            assert 'node_anomaly_scores' in predictions
            assert 'node_predictions' in predictions
            assert 'graph_anomaly_score' in predictions
            assert 'graph_prediction' in predictions
            
            # Check that scores are probabilities
            assert torch.all(predictions['node_anomaly_scores'] >= 0)
            assert torch.all(predictions['node_anomaly_scores'] <= 1)
            assert predictions['graph_anomaly_score'] >= 0
            assert predictions['graph_anomaly_score'] <= 1
            
            print("✅ Anomaly Prediction: PASSED")
            return True
            
        except Exception as e:
            print(f"❌ Anomaly Prediction: FAILED - {e}")
            return False
    
    def test_memory_efficiency(self) -> bool:
        """Test memory efficiency and scalability."""
        print("⚡ Testing Memory Efficiency...")
        
        try:
            # Test with different graph sizes
            sizes = [(100, 500), (500, 2000), (1000, 5000)]
            
            for num_nodes, num_edges in sizes:
                model = TGATForBlockchainAML(
                    hidden_dim=32,  # Smaller for memory efficiency
                    max_nodes=num_nodes,
                    device=self.device
                )
                
                data = self.create_synthetic_graph_data(num_nodes, num_edges, 10, 8)
                
                # Measure inference time
                start_time = time.time()
                with torch.no_grad():
                    outputs = model(
                        data['node_features'],
                        data['edge_index'],
                        data['edge_features'],
                        data['edge_times'],
                        data['node_times']
                    )
                inference_time = time.time() - start_time
                
                print(f"   📊 Graph size ({num_nodes}, {num_edges}): {inference_time:.3f}s")
                
                # Basic validation
                assert outputs['node_embeddings'].shape[0] == num_nodes
            
            print("✅ Memory Efficiency: PASSED")
            return True
            
        except Exception as e:
            print(f"❌ Memory Efficiency: FAILED - {e}")
            return False
    
    def test_pipeline_integration(self) -> bool:
        """Test integration with the blockchain AML pipeline."""
        print("🔗 Testing Pipeline Integration...")
        
        try:
            # Create model using pipeline factory function
            model = create_tgat_model_for_pipeline(device=self.device)
            
            # Test with pipeline-compatible data
            data = self.create_synthetic_graph_data(200, 1000, 10, 8)
            
            # Test inference
            predictions = model.predict_anomaly(
                data['node_features'],
                data['edge_index'],
                data['edge_features'],
                data['edge_times'],
                data['node_times'],
                threshold=0.7
            )
            
            # Validate pipeline outputs
            assert isinstance(predictions, dict)
            print(f"   📊 Detected {predictions['node_predictions'].sum().item()} suspicious nodes")
            print(f"   📊 Graph anomaly score: {predictions['graph_anomaly_score'].item():.3f}")
            
            print("✅ Pipeline Integration: PASSED")
            return True
            
        except Exception as e:
            print(f"❌ Pipeline Integration: FAILED - {e}")
            return False
    
    def test_edge_cases(self) -> bool:
        """Test edge cases and error handling."""
        print("🔍 Testing Edge Cases...")
        
        try:
            model = TGATForBlockchainAML(device=self.device)
            
            # Test with minimal data
            minimal_data = self.create_synthetic_graph_data(5, 10, 10, 8)
            outputs = model(**minimal_data)
            assert outputs['node_embeddings'].shape[0] == 5
            
            # Test with single node
            single_node_data = {
                'node_features': torch.randn(1, 10, device=self.device),
                'edge_index': torch.empty(2, 0, dtype=torch.long, device=self.device),
                'edge_features': torch.empty(0, 8, device=self.device),
                'edge_times': torch.empty(0, device=self.device),
                'node_times': torch.zeros(1, device=self.device)
            }
            outputs = model(**single_node_data)
            assert outputs['node_embeddings'].shape[0] == 1
            
            # Test memory reset
            model.reset_memory()
            
            print("✅ Edge Cases: PASSED")
            return True
            
        except Exception as e:
            print(f"❌ Edge Cases: FAILED - {e}")
            return False
    
    def run_all_tests(self) -> Dict[str, bool]:
        """Run all tests and return results."""
        print("🚀 Starting TGAT Model Test Suite")
        print("=" * 50)
        
        tests = [
            ('time_encoding', self.test_time_encoding_layer),
            ('memory_module', self.test_memory_module),
            ('tgat_layer', self.test_tgat_layer),
            ('complete_model', self.test_complete_tgat_model),
            ('blockchain_aml', self.test_blockchain_aml_model),
            ('anomaly_prediction', self.test_anomaly_prediction),
            ('memory_efficiency', self.test_memory_efficiency),
            ('pipeline_integration', self.test_pipeline_integration),
            ('edge_cases', self.test_edge_cases)
        ]
        
        results = {}
        passed_count = 0
        
        for test_name, test_func in tests:
            try:
                result = test_func()
                results[test_name] = result
                if result:
                    passed_count += 1
            except Exception as e:
                print(f"❌ {test_name}: FAILED - {e}")
                results[test_name] = False
        
        print("\n" + "=" * 50)
        print(f"🏁 Test Results: {passed_count}/{len(tests)} tests passed")
        
        if passed_count == len(tests):
            print("🎉 All tests PASSED! TGAT model is ready for deployment.")
        else:
            print("⚠️  Some tests failed. Please review the issues above.")
        
        return results


def main():
    """Main test execution."""
    print("TGAT Model Test Suite")
    print("=" * 50)
    
    # Determine device
    device = 'cuda' if torch.cuda.is_available() else 'cpu'
    print(f"🖥️  Using device: {device}")
    
    # Run tests
    tester = TGATModelTester(device=device)
    results = tester.run_all_tests()
    
    # Summary
    print("\n📊 Detailed Results:")
    for test_name, result in results.items():
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"   {test_name}: {status}")
    
    # Exit with appropriate code
    exit_code = 0 if all(results.values()) else 1
    return exit_code


if __name__ == "__main__":
    exit_code = main()
    exit(exit_code) 