#!/usr/bin/env python3
"""
Basic TGAT Components Test

Test the working components of our TGAT implementation:
- Time Encoding Layer ✅
- Memory Module ✅
"""

import torch
import numpy as np
from tgat_model.time_encoding import TimeEncodingLayer
from tgat_model.memory_module import MemoryModule


def test_time_encoding():
    """Test time encoding functionality."""
    print("🕒 Testing Time Encoding Layer...")
    
    device = 'cpu'
    time_dim = 32
    
    # Test basic time encoding
    time_encoder = TimeEncodingLayer(time_dim, device=device)
    
    # Create some blockchain timestamps
    timestamps = torch.tensor([
        1672531200,  # 2023-01-01
        1672617600,  # 2023-01-02
        1672704000,  # 2023-01-03
        1672790400,  # 2023-01-04
        1672876800   # 2023-01-05
    ], dtype=torch.float32, device=device)
    
    # Encode timestamps
    encoded = time_encoder(timestamps)
    
    print(f"   📊 Input timestamps shape: {timestamps.shape}")
    print(f"   📊 Encoded shape: {encoded.shape}")
    print(f"   📊 Time encoding range: [{encoded.min().item():.3f}, {encoded.max().item():.3f}]")
    
    # Test different basis functions
    for basis_func in ['cosine', 'time', 'learned']:
        encoder = TimeEncodingLayer(time_dim, basis_func, device=device)
        encoded = encoder(timestamps)
        print(f"   ✅ {basis_func} encoding: {encoded.shape}")
    
    print("✅ Time Encoding Layer: PASSED")
    return True


def test_memory_module():
    """Test memory module functionality."""
    print("🧠 Testing Memory Module...")
    
    device = 'cpu'
    num_nodes = 100  # 100 blockchain addresses
    memory_dim = 64
    time_dim = 32
    message_dim = 128
    
    # Create memory module
    memory_module = MemoryModule(
        num_nodes=num_nodes,
        memory_dim=memory_dim,
        time_dim=time_dim,
        message_dim=message_dim,
        device=device
    )
    
    print(f"   📊 Memory module created for {num_nodes} nodes")
    print(f"   📊 Memory dimension: {memory_dim}")
    
    # Test memory retrieval for specific addresses
    address_ids = torch.tensor([0, 5, 10, 25, 50], device=device)
    memory = memory_module.get_memory(address_ids)
    print(f"   📊 Retrieved memory shape: {memory.shape}")
    
    # Simulate transaction messages
    messages = torch.randn(5, message_dim, device=device)
    timestamps = torch.tensor([
        1672531200, 1672617600, 1672704000, 1672790400, 1672876800
    ], dtype=torch.float32, device=device)
    
    # Update memory with transaction information
    memory_module.update_memory(address_ids, messages, timestamps)
    print("   ✅ Memory updated with transaction data")
    
    # Test temporal memory with decay
    current_time = torch.tensor(1672963200.0, device=device)  # One day later
    updated_memory = memory_module.get_updated_memory(address_ids, current_time)
    print(f"   📊 Updated memory with temporal decay: {updated_memory.shape}")
    
    # Test memory statistics (basic info)
    memory_stats = {
        'mean_memory_norm': torch.norm(memory_module.memory, dim=1).mean(),
        'max_update_count': memory_module.last_update_time.max(),
        'memory_range': memory_module.memory.max() - memory_module.memory.min()
    }
    print(f"   📊 Memory statistics:")
    for key, value in memory_stats.items():
        print(f"      {key}: {value.item():.3f}")
    
    print("✅ Memory Module: PASSED")
    return True


def test_integration():
    """Test integration of time encoding and memory for blockchain AML."""
    print("🔗 Testing Integration for Blockchain AML...")
    
    device = 'cpu'
    
    # Simulate a small blockchain transaction graph
    num_addresses = 20
    num_transactions = 50
    
    # Create time encoder for transaction timestamps
    time_encoder = TimeEncodingLayer(32, device=device)
    
    # Create memory module for address embeddings
    memory_module = MemoryModule(
        num_nodes=num_addresses,
        memory_dim=64,
        time_dim=32,
        message_dim=96,
        device=device
    )
    
    # Simulate transaction data
    transaction_times = torch.randint(
        1672531200, 1672963200, (num_transactions,), device=device
    ).float()
    
    src_addresses = torch.randint(0, num_addresses, (num_transactions,), device=device)
    dst_addresses = torch.randint(0, num_addresses, (num_transactions,), device=device)
    
    # Process transactions through time encoding
    time_features = time_encoder(transaction_times)
    print(f"   📊 Transaction time features: {time_features.shape}")
    
    # Create mock edge features (transaction amounts, gas fees, etc.)
    edge_features = torch.randn(num_transactions, 8, device=device)
    
    # Compute messages for memory updates
    messages = memory_module.compute_messages(
        src_addresses, dst_addresses, edge_features, transaction_times
    )
    print(f"   📊 Computed messages: {messages.shape}")
    
    # Update memory with transaction information
    all_addresses = torch.cat([src_addresses, dst_addresses])
    all_messages = torch.cat([messages, messages])
    all_times = torch.cat([transaction_times, transaction_times])
    
    memory_module.update_memory(all_addresses, all_messages, all_times)
    print("   ✅ Updated memory with all transactions")
    
    # Retrieve final address embeddings
    all_address_ids = torch.arange(num_addresses, device=device)
    final_embeddings = memory_module.get_updated_memory(
        all_address_ids, transaction_times.max()
    )
    print(f"   📊 Final address embeddings: {final_embeddings.shape}")
    
    # Simple anomaly scoring based on embedding norms
    embedding_norms = torch.norm(final_embeddings, dim=1)
    anomaly_threshold = embedding_norms.mean() + 2 * embedding_norms.std()
    anomalous_addresses = (embedding_norms > anomaly_threshold).sum().item()
    
    print(f"   📊 Potential anomalous addresses: {anomalous_addresses}/{num_addresses}")
    print(f"   📊 Anomaly threshold: {anomaly_threshold.item():.3f}")
    
    print("✅ Integration Test: PASSED")
    return True


def main():
    """Run all basic tests."""
    print("🎯 TGAT Basic Components Test")
    print("=" * 50)
    
    tests = [
        ("Time Encoding", test_time_encoding),
        ("Memory Module", test_memory_module),
        ("Integration", test_integration)
    ]
    
    passed = 0
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
        except Exception as e:
            print(f"❌ {test_name}: FAILED - {e}")
    
    print("\n" + "=" * 50)
    print(f"🏁 Results: {passed}/{len(tests)} tests passed")
    
    if passed == len(tests):
        print("🎉 All basic components working! Ready for full model integration.")
    else:
        print("⚠️  Some basic components need fixing.")
    
    return passed == len(tests)


if __name__ == "__main__":
    success = main()
    exit(0 if success else 1) 