#!/usr/bin/env python3
"""
Performance Testing Script for Optimized Graph Construction Layer

This script validates that the performance-optimized Graph Construction Layer
meets the <500ms latency target with live Sepolia transaction processing.
"""

import os
import sys
import time
import json
import logging
import statistics
import threading
from datetime import datetime, timed<PERSON>ta
from typing import Dict, Any, List, Optional, Tuple
import pandas as pd
import numpy as np
import requests
import docker
from kafka import KafkaProducer, KafkaConsumer
from kafka.errors import KafkaError

# Add parent directory to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class PerformanceTestSuite:
    """Comprehensive performance testing for optimized Graph Construction Layer"""
    
    def __init__(self):
        self.kafka_bootstrap_servers = "localhost:29092"
        self.input_topic = "filtered-transactions"
        self.output_topic = "graph-snapshots"
        self.docker_client = docker.from_env()
        
        # Performance targets
        self.targets = {
            "max_latency_ms": 500,
            "min_throughput_rps": 1000,
            "min_success_rate": 0.95,
            "max_memory_usage_mb": 4096,
            "max_cpu_percent": 80
        }
        
        # Test results
        self.test_results = {
            "latency_tests": [],
            "throughput_tests": [],
            "resource_usage": [],
            "error_rates": [],
            "graph_quality_metrics": []
        }
        
        # Kafka clients
        self.producer = None
        self.consumer = None
        
    def setup_kafka_clients(self):
        """Setup Kafka producer and consumer for testing"""
        
        try:
            self.producer = KafkaProducer(
                bootstrap_servers=self.kafka_bootstrap_servers,
                value_serializer=lambda v: json.dumps(v).encode('utf-8'),
                key_serializer=lambda k: k.encode('utf-8') if k else None,
                acks='all',
                retries=3,
                batch_size=16384,
                buffer_memory=33554432
            )
            
            self.consumer = KafkaConsumer(
                self.output_topic,
                bootstrap_servers=self.kafka_bootstrap_servers,
                value_deserializer=lambda m: json.loads(m.decode('utf-8')),
                key_deserializer=lambda k: k.decode('utf-8') if k else None,
                auto_offset_reset='latest',
                enable_auto_commit=True,
                group_id='performance-test-consumer'
            )
            
            logger.info("Kafka clients initialized successfully")
            
        except Exception as e:
            logger.error(f"Failed to setup Kafka clients: {str(e)}")
            raise
    
    def generate_test_transaction_batch(self, batch_size: int = 100) -> List[Dict[str, Any]]:
        """Generate realistic test transaction batch"""
        
        transactions = []
        base_timestamp = int(time.time())
        
        # Common Sepolia addresses for realistic testing
        test_addresses = [
            "0x742d35Cc6634C0532925a3b8D4Cc24C34C3B5c50",
            "0x8ba1f109551bD432803012645Hac136c22C177",
            "0x1234567890123456789012345678901234567890",
            "0xabcdefabcdefabcdefabcdefabcdefabcdefabcd",
            "0x9876543210987654321098765432109876543210"
        ]
        
        for i in range(batch_size):
            # Create realistic transaction alert
            alert = {
                "alertId": f"test-alert-{i}-{int(time.time())}",
                "patternName": "HighValuePattern" if i % 4 == 0 else "RoundAmountPattern",
                "alertType": "AML_SUSPICIOUS",
                "severity": "HIGH" if i % 3 == 0 else "MEDIUM",
                "description": f"Test transaction {i} for performance testing",
                "detectionTimestamp": base_timestamp + i,
                "involvedTransactions": [
                    {
                        "hash": f"0x{i:064x}",
                        "fromAddress": test_addresses[i % len(test_addresses)],
                        "toAddress": test_addresses[(i + 1) % len(test_addresses)],
                        "value": int(np.random.exponential(0.1) * 1e18),  # Random ETH values
                        "blockNumber": 4000000 + i,
                        "timestamp": base_timestamp + i + np.random.randint(-30, 30),
                        "gasUsed": np.random.randint(21000, 100000),
                        "gasPrice": np.random.randint(1000000000, 50000000000),
                        "methodId": "0xa9059cbb" if i % 2 == 0 else None,
                        "valueEth": np.random.exponential(0.1),
                        "riskLevel": "HIGH" if i % 5 == 0 else "MEDIUM",
                        "contract": i % 3 == 0,
                        "roundAmount": i % 4 == 0,
                        "highValue": i % 5 == 0,
                        "microAmount": i % 6 == 0
                    }
                ],
                "primaryAddress": test_addresses[i % len(test_addresses)],
                "totalValue": np.random.exponential(0.1),
                "riskScore": f"{np.random.uniform(0.1, 0.9):.3f}",
                "reason": f"Test pattern detection {i}"
            }
            
            transactions.append(alert)
        
        return transactions
    
    def send_test_batch(self, transactions: List[Dict[str, Any]]) -> Tuple[float, int]:
        """Send test batch and measure sending time"""
        
        start_time = time.time()
        sent_count = 0
        
        try:
            for i, transaction in enumerate(transactions):
                future = self.producer.send(
                    self.input_topic,
                    key=f"test-key-{i}",
                    value=transaction
                )
                
                # Don't wait for each message, batch them
                if i % 50 == 0:  # Flush every 50 messages
                    self.producer.flush()
                
                sent_count += 1
            
            # Final flush
            self.producer.flush()
            
            send_time = time.time() - start_time
            
            logger.info(f"Sent {sent_count} transactions in {send_time:.2f} seconds")
            return send_time, sent_count
            
        except Exception as e:
            logger.error(f"Error sending test batch: {str(e)}")
            return time.time() - start_time, sent_count
    
    def measure_processing_latency(self, test_duration: int = 300) -> Dict[str, Any]:
        """Measure end-to-end processing latency"""
        
        logger.info(f"Starting latency measurement for {test_duration} seconds...")
        
        latencies = []
        processed_batches = 0
        start_time = time.time()
        
        # Start consumer in separate thread
        consumer_results = []
        
        def consume_results():
            for message in self.consumer:
                try:
                    graph_snapshot = message.value
                    processing_timestamp = graph_snapshot.get('processing_timestamp')
                    construction_time_ms = graph_snapshot.get('construction_time_ms', 0)
                    
                    if processing_timestamp:
                        # Calculate latency from processing timestamp
                        process_time = datetime.fromisoformat(processing_timestamp.replace('Z', '+00:00'))
                        current_time = datetime.now()
                        latency_ms = (current_time - process_time).total_seconds() * 1000
                        
                        latencies.append(latency_ms)
                        consumer_results.append({
                            'latency_ms': latency_ms,
                            'construction_time_ms': construction_time_ms,
                            'timestamp': current_time.isoformat()
                        })
                        
                        processed_batches += 1
                        
                        if processed_batches % 10 == 0:
                            logger.info(f"Processed {processed_batches} batches, avg latency: {np.mean(latencies):.2f}ms")
                
                except Exception as e:
                    logger.error(f"Error processing consumer message: {str(e)}")
                
                # Stop if test duration exceeded
                if time.time() - start_time > test_duration:
                    break
        
        # Start consumer thread
        consumer_thread = threading.Thread(target=consume_results)
        consumer_thread.daemon = True
        consumer_thread.start()
        
        # Send test batches continuously
        batch_count = 0
        while time.time() - start_time < test_duration:
            batch = self.generate_test_transaction_batch(50)  # Smaller batches for latency testing
            send_time, sent_count = self.send_test_batch(batch)
            batch_count += 1
            
            # Wait a bit between batches
            time.sleep(2)
        
        # Wait for consumer to finish
        consumer_thread.join(timeout=30)
        
        # Calculate latency statistics
        if latencies:
            latency_stats = {
                'test_duration': test_duration,
                'total_batches_sent': batch_count,
                'total_batches_processed': processed_batches,
                'avg_latency_ms': np.mean(latencies),
                'median_latency_ms': np.median(latencies),
                'p95_latency_ms': np.percentile(latencies, 95),
                'p99_latency_ms': np.percentile(latencies, 99),
                'max_latency_ms': np.max(latencies),
                'min_latency_ms': np.min(latencies),
                'std_latency_ms': np.std(latencies),
                'latency_target_met': np.percentile(latencies, 95) < self.targets['max_latency_ms'],
                'raw_latencies': latencies[:100]  # Store first 100 for analysis
            }
        else:
            latency_stats = {
                'error': 'No latency measurements collected',
                'total_batches_sent': batch_count,
                'total_batches_processed': processed_batches
            }
        
        logger.info(f"Latency test completed: {latency_stats}")
        return latency_stats
    
    def measure_throughput(self, test_duration: int = 180) -> Dict[str, Any]:
        """Measure system throughput"""
        
        logger.info(f"Starting throughput measurement for {test_duration} seconds...")
        
        total_transactions_sent = 0
        total_transactions_processed = 0
        start_time = time.time()
        
        # Track processing rate
        processed_counts = []
        
        def monitor_processing():
            last_count = 0
            while time.time() - start_time < test_duration:
                time.sleep(10)  # Check every 10 seconds
                
                # Get current processing count from logs or metrics
                try:
                    container = self.docker_client.containers.get("fyp-2-copy_graph-layer_1")
                    logs = container.logs(tail=100).decode('utf-8')
                    
                    # Count processing messages in logs
                    processing_count = logs.count("Window processed")
                    new_processed = processing_count - last_count
                    processed_counts.append(new_processed)
                    last_count = processing_count
                    
                except Exception as e:
                    logger.warning(f"Could not get processing count: {str(e)}")
        
        # Start monitoring thread
        monitor_thread = threading.Thread(target=monitor_processing)
        monitor_thread.daemon = True
        monitor_thread.start()
        
        # Send high-volume batches
        batch_size = 200  # Larger batches for throughput testing
        while time.time() - start_time < test_duration:
            batch = self.generate_test_transaction_batch(batch_size)
            send_time, sent_count = self.send_test_batch(batch)
            total_transactions_sent += sent_count
            
            # Calculate current throughput
            elapsed = time.time() - start_time
            current_throughput = total_transactions_sent / elapsed
            
            logger.info(f"Sent {total_transactions_sent} transactions, current throughput: {current_throughput:.1f} tx/s")
            
            # Brief pause to avoid overwhelming
            time.sleep(1)
        
        # Wait for monitoring to complete
        monitor_thread.join(timeout=10)
        
        # Calculate throughput statistics
        total_time = time.time() - start_time
        avg_throughput = total_transactions_sent / total_time
        
        throughput_stats = {
            'test_duration': total_time,
            'total_transactions_sent': total_transactions_sent,
            'avg_throughput_rps': avg_throughput,
            'throughput_target_met': avg_throughput >= self.targets['min_throughput_rps'],
            'processing_rate_samples': processed_counts,
            'peak_throughput_rps': max(processed_counts) * 6 if processed_counts else 0  # Convert 10s samples to per-second
        }
        
        logger.info(f"Throughput test completed: {throughput_stats}")
        return throughput_stats
    
    def measure_resource_usage(self, test_duration: int = 300) -> Dict[str, Any]:
        """Measure CPU and memory usage during processing"""
        
        logger.info(f"Starting resource usage measurement for {test_duration} seconds...")
        
        cpu_samples = []
        memory_samples = []
        start_time = time.time()
        
        try:
            container = self.docker_client.containers.get("fyp-2-copy_graph-layer_1")
            
            while time.time() - start_time < test_duration:
                try:
                    stats = container.stats(stream=False)
                    
                    # Calculate CPU usage
                    cpu_delta = stats['cpu_stats']['cpu_usage']['total_usage'] - stats['precpu_stats']['cpu_usage']['total_usage']
                    system_delta = stats['cpu_stats']['system_cpu_usage'] - stats['precpu_stats']['system_cpu_usage']
                    
                    if system_delta > 0:
                        cpu_percent = (cpu_delta / system_delta) * len(stats['cpu_stats']['cpu_usage']['percpu_usage']) * 100
                        cpu_samples.append(cpu_percent)
                    
                    # Calculate memory usage
                    memory_usage_mb = stats['memory_stats']['usage'] / (1024 * 1024)
                    memory_samples.append(memory_usage_mb)
                    
                    if len(cpu_samples) % 10 == 0:
                        logger.info(f"Current CPU: {cpu_percent:.1f}%, Memory: {memory_usage_mb:.1f}MB")
                
                except Exception as e:
                    logger.warning(f"Error collecting stats: {str(e)}")
                
                time.sleep(5)  # Sample every 5 seconds
        
        except Exception as e:
            logger.error(f"Error accessing container: {str(e)}")
            return {'error': str(e)}
        
        # Calculate resource usage statistics
        resource_stats = {
            'test_duration': time.time() - start_time,
            'cpu_samples': len(cpu_samples),
            'memory_samples': len(memory_samples),
            'avg_cpu_percent': np.mean(cpu_samples) if cpu_samples else 0,
            'max_cpu_percent': np.max(cpu_samples) if cpu_samples else 0,
            'avg_memory_mb': np.mean(memory_samples) if memory_samples else 0,
            'max_memory_mb': np.max(memory_samples) if memory_samples else 0,
            'cpu_target_met': np.max(cpu_samples) < self.targets['max_cpu_percent'] if cpu_samples else False,
            'memory_target_met': np.max(memory_samples) < self.targets['max_memory_usage_mb'] if memory_samples else False
        }
        
        logger.info(f"Resource usage test completed: {resource_stats}")
        return resource_stats
    
    def test_graph_quality(self, sample_size: int = 50) -> Dict[str, Any]:
        """Test the quality of generated graphs"""
        
        logger.info(f"Starting graph quality test with {sample_size} samples...")
        
        quality_metrics = []
        
        # Send test batch and collect results
        test_batch = self.generate_test_transaction_batch(sample_size)
        send_time, sent_count = self.send_test_batch(test_batch)
        
        # Wait for processing
        time.sleep(30)
        
        # Collect graph snapshots
        collected_samples = 0
        timeout = time.time() + 60  # 1 minute timeout
        
        for message in self.consumer:
            if time.time() > timeout:
                break
            
            try:
                graph_snapshot = message.value.get('graph_snapshot', {})
                
                if graph_snapshot:
                    # Extract graph quality metrics
                    metadata = graph_snapshot.get('metadata', {})
                    
                    quality_metrics.append({
                        'num_nodes': graph_snapshot.get('num_nodes', 0),
                        'num_edges': graph_snapshot.get('num_edges', 0),
                        'total_volume': graph_snapshot.get('total_volume', 0),
                        'construction_time_ms': graph_snapshot.get('construction_time_ms', 0),
                        'graph_density': metadata.get('graph_density', 0),
                        'avg_node_degree': metadata.get('avg_node_degree', 0),
                        'max_node_degree': metadata.get('max_node_degree', 0),
                        'construction_method': metadata.get('construction_method', 'unknown')
                    })
                    
                    collected_samples += 1
                    
                    if collected_samples >= 10:  # Collect at least 10 samples
                        break
            
            except Exception as e:
                logger.warning(f"Error processing graph quality sample: {str(e)}")
        
        # Calculate quality statistics
        if quality_metrics:
            quality_stats = {
                'samples_collected': len(quality_metrics),
                'avg_nodes_per_graph': np.mean([m['num_nodes'] for m in quality_metrics]),
                'avg_edges_per_graph': np.mean([m['num_edges'] for m in quality_metrics]),
                'avg_graph_density': np.mean([m['graph_density'] for m in quality_metrics]),
                'avg_construction_time_ms': np.mean([m['construction_time_ms'] for m in quality_metrics]),
                'vectorized_operations_used': all(m['construction_method'] == 'vectorized' for m in quality_metrics),
                'construction_time_target_met': all(m['construction_time_ms'] < 500 for m in quality_metrics),
                'sample_metrics': quality_metrics[:5]  # Store first 5 for analysis
            }
        else:
            quality_stats = {
                'error': 'No graph quality samples collected',
                'samples_collected': 0
            }
        
        logger.info(f"Graph quality test completed: {quality_stats}")
        return quality_stats
    
    def run_comprehensive_performance_test(self) -> Dict[str, Any]:
        """Run comprehensive performance test suite"""
        
        logger.info("🚀 Starting comprehensive performance test suite...")
        
        try:
            # Setup
            self.setup_kafka_clients()
            
            # Test 1: Latency measurement
            logger.info("Test 1: Measuring processing latency...")
            latency_results = self.measure_processing_latency(300)  # 5 minutes
            self.test_results['latency_tests'].append(latency_results)
            
            # Brief pause between tests
            time.sleep(30)
            
            # Test 2: Throughput measurement
            logger.info("Test 2: Measuring system throughput...")
            throughput_results = self.measure_throughput(180)  # 3 minutes
            self.test_results['throughput_tests'].append(throughput_results)
            
            # Test 3: Resource usage (concurrent with other tests)
            logger.info("Test 3: Measuring resource usage...")
            resource_results = self.measure_resource_usage(300)  # 5 minutes
            self.test_results['resource_usage'].append(resource_results)
            
            # Test 4: Graph quality
            logger.info("Test 4: Testing graph quality...")
            quality_results = self.test_graph_quality(100)
            self.test_results['graph_quality_metrics'].append(quality_results)
            
            # Generate final report
            final_report = self.generate_performance_report()
            
            logger.info("✅ Comprehensive performance test suite completed!")
            return final_report
            
        except Exception as e:
            logger.error(f"❌ Performance test suite failed: {str(e)}")
            raise
        finally:
            # Cleanup
            if self.producer:
                self.producer.close()
            if self.consumer:
                self.consumer.close()
    
    def generate_performance_report(self) -> Dict[str, Any]:
        """Generate comprehensive performance report"""
        
        report = {
            'test_timestamp': datetime.now().isoformat(),
            'performance_targets': self.targets,
            'test_results': self.test_results,
            'summary': {},
            'recommendations': []
        }
        
        # Analyze results and generate summary
        summary = {}
        
        # Latency analysis
        if self.test_results['latency_tests']:
            latency_test = self.test_results['latency_tests'][0]
            summary['latency'] = {
                'target_met': latency_test.get('latency_target_met', False),
                'p95_latency_ms': latency_test.get('p95_latency_ms', 0),
                'avg_latency_ms': latency_test.get('avg_latency_ms', 0)
            }
        
        # Throughput analysis
        if self.test_results['throughput_tests']:
            throughput_test = self.test_results['throughput_tests'][0]
            summary['throughput'] = {
                'target_met': throughput_test.get('throughput_target_met', False),
                'avg_throughput_rps': throughput_test.get('avg_throughput_rps', 0),
                'peak_throughput_rps': throughput_test.get('peak_throughput_rps', 0)
            }
        
        # Resource usage analysis
        if self.test_results['resource_usage']:
            resource_test = self.test_results['resource_usage'][0]
            summary['resources'] = {
                'cpu_target_met': resource_test.get('cpu_target_met', False),
                'memory_target_met': resource_test.get('memory_target_met', False),
                'max_cpu_percent': resource_test.get('max_cpu_percent', 0),
                'max_memory_mb': resource_test.get('max_memory_mb', 0)
            }
        
        # Graph quality analysis
        if self.test_results['graph_quality_metrics']:
            quality_test = self.test_results['graph_quality_metrics'][0]
            summary['graph_quality'] = {
                'construction_time_target_met': quality_test.get('construction_time_target_met', False),
                'vectorized_operations_used': quality_test.get('vectorized_operations_used', False),
                'avg_construction_time_ms': quality_test.get('avg_construction_time_ms', 0)
            }
        
        report['summary'] = summary
        
        # Generate recommendations
        recommendations = []
        
        if not summary.get('latency', {}).get('target_met', False):
            recommendations.append("Consider reducing batch size or increasing processing parallelism to improve latency")
        
        if not summary.get('throughput', {}).get('target_met', False):
            recommendations.append("Consider increasing Kafka partitions or Spark executor resources for better throughput")
        
        if not summary.get('resources', {}).get('cpu_target_met', False):
            recommendations.append("CPU usage is high - consider optimizing algorithms or scaling horizontally")
        
        if not summary.get('resources', {}).get('memory_target_met', False):
            recommendations.append("Memory usage is high - consider reducing graph size limits or optimizing memory usage")
        
        report['recommendations'] = recommendations
        
        # Calculate overall performance score
        targets_met = [
            summary.get('latency', {}).get('target_met', False),
            summary.get('throughput', {}).get('target_met', False),
            summary.get('resources', {}).get('cpu_target_met', False),
            summary.get('resources', {}).get('memory_target_met', False)
        ]
        
        performance_score = sum(targets_met) / len(targets_met) * 100
        report['overall_performance_score'] = performance_score
        
        return report
    
    def save_report(self, report: Dict[str, Any], filename: str = None):
        """Save performance report to file"""
        
        if not filename:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"graph_layer_performance_report_{timestamp}.json"
        
        filepath = os.path.join("/tmp", filename)
        
        with open(filepath, 'w') as f:
            json.dump(report, f, indent=2, default=str)
        
        logger.info(f"Performance report saved to: {filepath}")
        return filepath


def main():
    """Main testing function"""
    
    print("🧪 Optimized Graph Construction Layer Performance Test Suite")
    print("=" * 60)
    
    # Check if services are running
    docker_client = docker.from_env()
    try:
        container = docker_client.containers.get("fyp-2-copy_graph-layer_1")
        if container.status != "running":
            print("❌ Graph Layer container is not running")
            print("Please start the services first using: python3 deploy_optimized_graph_layer.py")
            sys.exit(1)
    except docker.errors.NotFound:
        print("❌ Graph Layer container not found")
        print("Please deploy the services first using: python3 deploy_optimized_graph_layer.py")
        sys.exit(1)
    
    # Run performance tests
    test_suite = PerformanceTestSuite()
    
    try:
        # Run comprehensive test
        report = test_suite.run_comprehensive_performance_test()
        
        # Save report
        report_file = test_suite.save_report(report)
        
        # Print summary
        print("\n📊 PERFORMANCE TEST RESULTS")
        print("=" * 40)
        
        summary = report.get('summary', {})
        score = report.get('overall_performance_score', 0)
        
        print(f"Overall Performance Score: {score:.1f}%")
        print()
        
        # Latency results
        if 'latency' in summary:
            latency = summary['latency']
            status = "✅ PASS" if latency['target_met'] else "❌ FAIL"
            print(f"Latency Test: {status}")
            print(f"  • P95 Latency: {latency['p95_latency_ms']:.1f}ms (target: <500ms)")
            print(f"  • Avg Latency: {latency['avg_latency_ms']:.1f}ms")
        
        # Throughput results
        if 'throughput' in summary:
            throughput = summary['throughput']
            status = "✅ PASS" if throughput['target_met'] else "❌ FAIL"
            print(f"Throughput Test: {status}")
            print(f"  • Avg Throughput: {throughput['avg_throughput_rps']:.1f} tx/s (target: >1000 tx/s)")
            print(f"  • Peak Throughput: {throughput['peak_throughput_rps']:.1f} tx/s")
        
        # Resource usage results
        if 'resources' in summary:
            resources = summary['resources']
            cpu_status = "✅ PASS" if resources['cpu_target_met'] else "❌ FAIL"
            mem_status = "✅ PASS" if resources['memory_target_met'] else "❌ FAIL"
            print(f"Resource Usage Test: CPU {cpu_status}, Memory {mem_status}")
            print(f"  • Max CPU: {resources['max_cpu_percent']:.1f}% (target: <80%)")
            print(f"  • Max Memory: {resources['max_memory_mb']:.1f}MB (target: <4096MB)")
        
        # Graph quality results
        if 'graph_quality' in summary:
            quality = summary['graph_quality']
            status = "✅ PASS" if quality['construction_time_target_met'] else "❌ FAIL"
            print(f"Graph Quality Test: {status}")
            print(f"  • Avg Construction Time: {quality['avg_construction_time_ms']:.1f}ms")
            print(f"  • Vectorized Operations: {'✅ Yes' if quality['vectorized_operations_used'] else '❌ No'}")
        
        # Recommendations
        recommendations = report.get('recommendations', [])
        if recommendations:
            print("\n💡 RECOMMENDATIONS:")
            for i, rec in enumerate(recommendations, 1):
                print(f"  {i}. {rec}")
        
        print(f"\n📄 Detailed report saved to: {report_file}")
        
        if score >= 75:
            print("\n🎉 Performance test PASSED! The optimized Graph Construction Layer meets performance targets.")
        else:
            print("\n⚠️  Performance test shows room for improvement. Check recommendations above.")
    
    except KeyboardInterrupt:
        print("\n⏹️  Performance test interrupted by user")
    except Exception as e:
        print(f"\n❌ Performance test failed: {str(e)}")
        sys.exit(1)


if __name__ == "__main__":
    main() 