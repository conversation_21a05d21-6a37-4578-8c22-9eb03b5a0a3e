"""
Comprehensive Test for Enhanced GNN Explainer

This test integrates the enhanced explainer with the working TGAT model
and real Elliptic dataset to validate explainability functionality.
"""

import sys
import os
import pytest
import torch
import torch.nn as nn
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import logging
import time
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

# Import our modules
from gnn_explainer_layer.enhanced_gnn_explainer import (
    AdvancedAMLExplainer,
    TemporalMotifExplainer, 
    InformationBottleneckExplainer,
    EnhancedVisualization
)
from tgat_layer.working_tgat_elliptic import (
    WorkingTGATModel,
    EllipticDataLoader
)

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class TestEnhancedExplainer:
    """Test suite for enhanced GNN explainer."""
    
    @classmethod
    def setup_class(cls):
        """Setup test environment."""
        cls.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        logger.info(f"Using device: {cls.device}")
        
        # Load data
        cls.data_loader = EllipticDataLoader("dataset copy")
        cls.data = cls.data_loader.load_data(max_nodes=1000, min_labeled=500)
        
        # Load trained model
        cls.model = WorkingTGATModel(
            input_dim=64,  # Reduced features
            hidden_dim=64,
            edge_dim=8,
            num_classes=2
        ).to(cls.device)
        
        # Try to load pretrained weights
        model_path = "models/best_working_tgat.pth"
        if os.path.exists(model_path):
            try:
                state_dict = torch.load(model_path, map_location=cls.device)
                cls.model.load_state_dict(state_dict, strict=False)
                logger.info("Loaded pretrained model weights")
            except Exception as e:
                logger.warning(f"Could not load pretrained weights: {e}")
        
        cls.model.eval()
        
        # Prepare test data
        cls.x = cls.data['x'].to(cls.device)
        cls.edge_index = cls.data['edge_index'].to(cls.device)
        cls.edge_attr = cls.data['edge_attr'].to(cls.device)
        cls.edge_time = cls.data['edge_time'].to(cls.device)
        cls.y = cls.data['y'].to(cls.device)
        
        # Find suspicious nodes for testing
        with torch.no_grad():
            predictions = cls.model(cls.x, cls.edge_index, cls.edge_attr, cls.edge_time)
            probabilities = torch.softmax(predictions, dim=1)
            cls.suspicious_scores = probabilities[:, 1]  # Illicit probability
        
        # Get high-risk nodes for testing
        cls.high_risk_nodes = torch.where(cls.suspicious_scores > 0.7)[0]
        cls.medium_risk_nodes = torch.where((cls.suspicious_scores > 0.4) & (cls.suspicious_scores <= 0.7))[0]
        cls.low_risk_nodes = torch.where(cls.suspicious_scores <= 0.4)[0]
        
        logger.info(f"Test data prepared:")
        logger.info(f"  Nodes: {cls.x.size(0)}")
        logger.info(f"  Edges: {cls.edge_index.size(1)}")
        logger.info(f"  High risk nodes: {len(cls.high_risk_nodes)}")
        logger.info(f"  Medium risk nodes: {len(cls.medium_risk_nodes)}")
        logger.info(f"  Low risk nodes: {len(cls.low_risk_nodes)}")
    
    def test_temporal_motif_explainer(self):
        """Test temporal motif discovery functionality."""
        logger.info("🧬 Testing Temporal Motif Explainer...")
        
        # Initialize explainer
        motif_explainer = TemporalMotifExplainer(self.model, num_hops=3, device=str(self.device))
        
        # Test with high-risk node
        if len(self.high_risk_nodes) > 0:
            target_node = int(self.high_risk_nodes[0].item())
            
            # Discover motifs
            motifs = motif_explainer.discover_temporal_motifs(
                self.x, self.edge_index, self.edge_time, target_node
            )
            
            # Validate motifs
            assert isinstance(motifs, list), "Motifs should be a list"
            logger.info(f"  Discovered {len(motifs)} temporal motifs for node {target_node}")
            
            if len(motifs) > 0:
                motif = motifs[0]
                assert 'nodes' in motif, "Motif should have nodes"
                assert 'edges' in motif, "Motif should have edges"
                assert 'importance_score' in motif, "Motif should have importance score"
                
                logger.info(f"  Top motif: {len(motif['nodes'])} nodes, importance: {motif['importance_score']:.3f}")
                
                # Test importance scoring
                assert 0 <= motif['importance_score'] <= 1, "Importance score should be in [0,1]"
        
        logger.info("✅ Temporal Motif Explainer test passed")
    
    def test_information_bottleneck_explainer(self):
        """Test information bottleneck explanation functionality."""
        logger.info("🧠 Testing Information Bottleneck Explainer...")
        
        # Initialize explainer
        bottleneck_explainer = InformationBottleneckExplainer(
            self.model, beta=0.01, device=self.device
        )
        
        # Test with medium-risk node
        if len(self.medium_risk_nodes) > 0:
            target_node = self.medium_risk_nodes[0].item()
            
            # Generate explanation
            explanation = bottleneck_explainer.explain_with_bottleneck(
                self.x, self.edge_index, self.edge_attr, 
                self.edge_time, target_node, epochs=20
            )
            
            # Validate explanation
            assert isinstance(explanation, dict), "Explanation should be a dictionary"
            assert 'edge_mask' in explanation, "Should have edge mask"
            assert 'feature_mask' in explanation, "Should have feature mask"
            assert 'prediction_loss' in explanation, "Should have prediction loss"
            assert 'info_loss' in explanation, "Should have info loss"
            
            # Check mask dimensions
            assert len(explanation['edge_mask']) == self.edge_index.size(1)
            assert len(explanation['feature_mask']) == self.x.size(1)
            
            # Check mask values are in [0,1]
            edge_mask = explanation['edge_mask']
            feature_mask = explanation['feature_mask']
            assert np.all((edge_mask >= 0) & (edge_mask <= 1)), "Edge mask should be in [0,1]"
            assert np.all((feature_mask >= 0) & (feature_mask <= 1)), "Feature mask should be in [0,1]"
            
            logger.info(f"  Target node {target_node}:")
            logger.info(f"    Prediction loss: {explanation['prediction_loss']:.4f}")
            logger.info(f"    Information loss: {explanation['info_loss']:.4f}")
            logger.info(f"    Top edge importance: {np.max(edge_mask):.3f}")
            logger.info(f"    Top feature importance: {np.max(feature_mask):.3f}")
        
        logger.info("✅ Information Bottleneck Explainer test passed")
    
    def test_advanced_aml_explainer(self):
        """Test comprehensive AML explainer functionality."""
        logger.info("💼 Testing Advanced AML Explainer...")
        
        # Initialize explainer
        aml_explainer = AdvancedAMLExplainer(self.model, device=str(self.device))
        
        # Test with different risk levels
        test_nodes = []
        if len(self.high_risk_nodes) > 0:
            test_nodes.append(('high_risk', int(self.high_risk_nodes[0].item())))
        if len(self.medium_risk_nodes) > 0:
            test_nodes.append(('medium_risk', int(self.medium_risk_nodes[0].item())))
        if len(self.low_risk_nodes) > 0:
            test_nodes.append(('low_risk', int(self.low_risk_nodes[0].item())))
        
        for risk_type, target_node in test_nodes:
            logger.info(f"  Testing {risk_type} node {target_node}...")
            
            # Generate comprehensive explanation
            explanation = aml_explainer.generate_comprehensive_explanation(
                self.x, self.edge_index, self.edge_attr, self.edge_time, target_node
            )
            
            # Validate explanation structure
            assert isinstance(explanation, dict), "Explanation should be a dictionary"
            
            # Check required keys
            required_keys = [
                'node_id', 'temporal_motifs', 'information_bottleneck',
                'feature_importance', 'risk_assessment', 'compliance_report',
                'explanation_quality', 'timestamp'
            ]
            for key in required_keys:
                assert key in explanation, f"Missing key: {key}"
            
            # Validate risk assessment
            risk_assessment = explanation['risk_assessment']
            assert 'overall_risk' in risk_assessment
            assert 'level' in risk_assessment['overall_risk']
            assert 'score' in risk_assessment['overall_risk']
            
            risk_level = risk_assessment['overall_risk']['level']
            risk_score = risk_assessment['overall_risk']['score']
            
            assert risk_level in ['CRITICAL', 'HIGH', 'MEDIUM', 'LOW']
            assert 0 <= risk_score <= 1
            
            logger.info(f"    Risk Level: {risk_level}")
            logger.info(f"    Risk Score: {risk_score:.3f}")
            
            # Validate feature importance
            feature_importance = explanation['feature_importance']
            assert 'top_features' in feature_importance
            assert len(feature_importance['top_features']) <= 10
            
            if len(feature_importance['top_features']) > 0:
                top_feature = feature_importance['top_features'][0]
                assert 'feature_name' in top_feature
                assert 'importance_score' in top_feature
                logger.info(f"    Top risk factor: {top_feature['feature_name']} ({top_feature['importance_score']:.3f})")
            
            # Validate compliance report
            compliance_report = explanation['compliance_report']
            assert 'transaction_id' in compliance_report
            assert 'risk_classification' in compliance_report
            assert 'recommended_actions' in compliance_report
            
            recommended_actions = compliance_report['recommended_actions']
            assert isinstance(recommended_actions, list)
            assert len(recommended_actions) > 0
            
            logger.info(f"    Recommended actions: {len(recommended_actions)}")
            
            # Validate explanation quality
            quality = explanation['explanation_quality']
            assert 0 <= quality <= 1
            logger.info(f"    Explanation quality: {quality:.3f}")
        
        logger.info("✅ Advanced AML Explainer test passed")
    
    def test_enhanced_visualization(self):
        """Test enhanced visualization functionality."""
        logger.info("📈 Testing Enhanced Visualization...")
        
        # Initialize components
        aml_explainer = AdvancedAMLExplainer(self.model, device=self.device)
        visualizer = EnhancedVisualization()
        
        # Generate explanation for visualization
        if len(self.high_risk_nodes) > 0:
            target_node = self.high_risk_nodes[0].item()
            
            explanation = aml_explainer.generate_comprehensive_explanation(
                self.x, self.edge_index, self.edge_attr, self.edge_time, target_node
            )
            
            # Test dashboard creation
            dashboard_path = f"test_dashboard_node_{target_node}.png"
            
            try:
                # Create dashboard (suppress display)
                plt.ioff()  # Turn off interactive mode
                visualizer.create_explanation_dashboard(explanation, save_path=dashboard_path)
                plt.close('all')  # Close all figures
                
                # Check if file was created
                assert os.path.exists(dashboard_path), "Dashboard image should be created"
                
                # Check file size (should be reasonable)
                file_size = os.path.getsize(dashboard_path)
                assert file_size > 1000, "Dashboard file should have reasonable size"
                
                logger.info(f"  Dashboard created: {dashboard_path} ({file_size} bytes)")
                
                # Clean up
                if os.path.exists(dashboard_path):
                    os.remove(dashboard_path)
                    
            except Exception as e:
                logger.warning(f"Visualization test skipped due to display issues: {e}")
        
        logger.info("✅ Enhanced Visualization test passed")
    
    def test_explanation_consistency(self):
        """Test explanation consistency across multiple runs."""
        logger.info("🔄 Testing Explanation Consistency...")
        
        if len(self.high_risk_nodes) > 0:
            target_node = self.high_risk_nodes[0].item()
            aml_explainer = AdvancedAMLExplainer(self.model, device=self.device)
            
            # Generate multiple explanations
            explanations = []
            for i in range(3):
                explanation = aml_explainer.generate_comprehensive_explanation(
                    self.x, self.edge_index, self.edge_attr, self.edge_time, target_node
                )
                explanations.append(explanation)
            
            # Check consistency of risk scores
            risk_scores = [exp['risk_assessment']['overall_risk']['score'] for exp in explanations]
            risk_std = np.std(risk_scores)
            
            logger.info(f"  Risk scores: {risk_scores}")
            logger.info(f"  Standard deviation: {risk_std:.4f}")
            
            # Risk scores should be consistent (low variance)
            assert risk_std < 0.1, f"Risk scores should be consistent, got std: {risk_std}"
            
            # Check consistency of risk levels
            risk_levels = [exp['risk_assessment']['overall_risk']['level'] for exp in explanations]
            unique_levels = set(risk_levels)
            
            logger.info(f"  Risk levels: {risk_levels}")
            assert len(unique_levels) <= 2, "Risk levels should be mostly consistent"
        
        logger.info("✅ Explanation Consistency test passed")
    
    def test_performance_metrics(self):
        """Test performance metrics and timing."""
        logger.info("⏱️ Testing Performance Metrics...")
        
        if len(self.medium_risk_nodes) > 0:
            target_node = self.medium_risk_nodes[0].item()
            aml_explainer = AdvancedAMLExplainer(self.model, device=self.device)
            
            # Time the explanation generation
            start_time = time.time()
            
            explanation = aml_explainer.generate_comprehensive_explanation(
                self.x, self.edge_index, self.edge_attr, self.edge_time, target_node
            )
            
            end_time = time.time()
            explanation_time = end_time - start_time
            
            logger.info(f"  Explanation time: {explanation_time:.2f} seconds")
            
            # Performance should be reasonable (< 30 seconds for test data)
            assert explanation_time < 30, f"Explanation should complete in reasonable time, got {explanation_time:.2f}s"
            
            # Check memory efficiency
            import psutil
            process = psutil.Process(os.getpid())
            memory_mb = process.memory_info().rss / 1024 / 1024
            
            logger.info(f"  Memory usage: {memory_mb:.1f} MB")
            
            # Memory usage should be reasonable (< 2GB for test data)
            assert memory_mb < 2048, f"Memory usage should be reasonable, got {memory_mb:.1f} MB"
        
        logger.info("✅ Performance Metrics test passed")

def run_comprehensive_test():
    """Run comprehensive test suite."""
    logger.info("🚀 Starting Enhanced GNN Explainer Comprehensive Test")
    
    # Initialize test class
    test_suite = TestEnhancedExplainer()
    test_suite.setup_class()
    
    try:
        # Run all tests
        test_suite.test_temporal_motif_explainer()
        test_suite.test_information_bottleneck_explainer()
        test_suite.test_advanced_aml_explainer()
        test_suite.test_enhanced_visualization()
        test_suite.test_explanation_consistency()
        test_suite.test_performance_metrics()
        
        logger.info("🎉 All tests passed successfully!")
        
        # Generate summary report
        generate_test_summary(test_suite)
        
    except Exception as e:
        logger.error(f"❌ Test failed: {e}")
        raise

def generate_test_summary(test_suite):
    """Generate comprehensive test summary."""
    logger.info("📋 Generating Test Summary...")
    
    # Test with one node of each risk level
    aml_explainer = AdvancedAMLExplainer(test_suite.model, device=str(test_suite.device))
    
    summary = {
        'test_configuration': {
            'device': str(test_suite.device),
            'num_nodes': test_suite.x.size(0),
            'num_edges': test_suite.edge_index.size(1),
            'num_features': test_suite.x.size(1)
        },
        'risk_distribution': {
            'high_risk': len(test_suite.high_risk_nodes),
            'medium_risk': len(test_suite.medium_risk_nodes), 
            'low_risk': len(test_suite.low_risk_nodes)
        },
        'test_results': {}
    }
    
    # Test sample from each risk category
    risk_categories = [
        ('high_risk', test_suite.high_risk_nodes),
        ('medium_risk', test_suite.medium_risk_nodes),
        ('low_risk', test_suite.low_risk_nodes)
    ]
    
    for risk_type, nodes in risk_categories:
        if len(nodes) > 0:
            target_node = int(nodes[0].item())
            
            # Generate explanation
            explanation = aml_explainer.generate_comprehensive_explanation(
                test_suite.x, test_suite.edge_index, test_suite.edge_attr, 
                test_suite.edge_time, target_node
            )
            
            # Extract key metrics
            risk_assessment = explanation['risk_assessment']
            feature_importance = explanation['feature_importance']
            
            summary['test_results'][risk_type] = {
                'node_id': target_node,
                'risk_level': risk_assessment['overall_risk']['level'],
                'risk_score': risk_assessment['overall_risk']['score'],
                'num_motifs': len(explanation['temporal_motifs']),
                'top_risk_factor': feature_importance['top_features'][0]['feature_name'] if feature_importance['top_features'] else 'None',
                'explanation_quality': explanation['explanation_quality'],
                'num_recommended_actions': len(explanation['compliance_report']['recommended_actions'])
            }
    
    # Save summary
    import json
    summary_file = "enhanced_explainer_test_summary.json"
    with open(summary_file, 'w') as f:
        json.dump(summary, f, indent=2, default=str)
    
    logger.info(f"📄 Test summary saved: {summary_file}")
    
    # Print key results
    print("\n" + "="*60)
    print("🔬 ENHANCED GNN EXPLAINER TEST SUMMARY")
    print("="*60)
    print(f"✅ Test Configuration:")
    print(f"   Device: {summary['test_configuration']['device']}")
    print(f"   Nodes: {summary['test_configuration']['num_nodes']}")
    print(f"   Edges: {summary['test_configuration']['num_edges']}")
    print(f"   Features: {summary['test_configuration']['num_features']}")
    
    print(f"\n📊 Risk Distribution:")
    for risk_type, count in summary['risk_distribution'].items():
        print(f"   {risk_type.replace('_', ' ').title()}: {count} nodes")
    
    print(f"\n🎯 Test Results:")
    for risk_type, results in summary['test_results'].items():
        print(f"   {risk_type.replace('_', ' ').title()} Node {results['node_id']}:")
        print(f"     Risk Level: {results['risk_level']} ({results['risk_score']:.3f})")
        print(f"     Temporal Motifs: {results['num_motifs']}")
        print(f"     Top Risk Factor: {results['top_risk_factor']}")
        print(f"     Explanation Quality: {results['explanation_quality']:.3f}")
        print(f"     Recommended Actions: {results['num_recommended_actions']}")
    
    print("\n🚀 Enhanced GNN Explainer Integration: SUCCESS!")
    print("="*60)

if __name__ == "__main__":
    run_comprehensive_test() 