#!/usr/bin/env python3

import json
import time
from kafka import KafkaProducer
from datetime import datetime, timezone

def create_test_transaction(hash_prefix, from_addr, to_addr, value_eth, block_number=8624900, timestamp=None):
    """Create a test transaction with specific parameters"""
    if timestamp is None:
        timestamp = int(time.time())
    
    # Convert ETH to Wei (multiply by 10^18)
    value_wei = int(value_eth * 1e18)
    
    return {
        "hash": f"0x{hash_prefix}{'0' * (64 - len(hash_prefix))}",
        "from_address": from_addr,
        "to_address": to_addr,
        "value": value_wei,
        "block_number": block_number,
        "timestamp": timestamp,
        "gas_used": 21000,
        "gas_price": 3000000000,  # 30 Gwei
        "input_data": "0x"
    }

def main():
    # Initialize Kafka producer
    producer = KafkaProducer(
        bootstrap_servers=['localhost:9092'],
        value_serializer=lambda v: json.dumps(v).encode('utf-8'),
        key_serializer=str.encode
    )
    
    print("🔥 FOCUSED Enhanced AML CEP Test")
    print("=" * 60)
    
    current_time = int(time.time())
    test_address = "******************************************"
    
    # Test 1: High Value Pattern (should trigger immediately)
    print("\n📈 Test 1: HIGH VALUE Pattern (10 ETH)")
    high_value_tx = create_test_transaction(
        "HIGHVAL1",
        test_address,
        "******************************************",
        10.0,  # 10 ETH - definitely > 5 ETH threshold
        timestamp=current_time
    )
    producer.send('ethereum-transactions', value=high_value_tx, key='test-high-value')
    producer.flush()
    print(f"   ✅ Sent 10 ETH transaction: {high_value_tx['hash'][:12]}")
    
    time.sleep(2)
    
    # Test 2: Round Amount Pattern
    print("\n💰 Test 2: ROUND AMOUNT Pattern")
    round_tx1 = create_test_transaction(
        "ROUND001",
        test_address,
        "******************************************",
        1.0,  # Exactly 1.0 ETH
        timestamp=current_time + 10
    )
    producer.send('ethereum-transactions', value=round_tx1, key='test-round-1')
    producer.flush()
    print(f"   ✅ Sent 1.0 ETH transaction: {round_tx1['hash'][:12]}")
    
    time.sleep(2)
    
    round_tx2 = create_test_transaction(
        "ROUND002",
        test_address,
        "******************************************",
        0.1,  # Exactly 0.1 ETH
        timestamp=current_time + 20
    )
    producer.send('ethereum-transactions', value=round_tx2, key='test-round-2')
    producer.flush()
    print(f"   ✅ Sent 0.1 ETH transaction: {round_tx2['hash'][:12]}")
    
    time.sleep(2)
    
    # Test 3: Rapid Succession Pattern (3 transactions in quick succession)
    print("\n🚨 Test 3: RAPID SUCCESSION Pattern")
    rapid_base_time = current_time + 30
    
    for i in range(3):
        rapid_tx = create_test_transaction(
            f"RAPID00{i+1}",
            test_address,
            f"0x666666666666666666666666666666666666666{i}",
            0.01,  # Small amount but > 0
            timestamp=rapid_base_time + i * 5  # 5 seconds apart
        )
        producer.send('ethereum-transactions', value=rapid_tx, key=f'test-rapid-{i+1}')
        producer.flush()
        print(f"   ✅ Sent rapid tx {i+1}: {rapid_tx['hash'][:12]} (0.01 ETH)")
        time.sleep(1)
    
    time.sleep(2)
    
    # Test 4: Micro-Structuring Pattern (5 micro transactions)
    print("\n🔍 Test 4: MICRO-STRUCTURING Pattern")
    micro_base_time = current_time + 60
    
    for i in range(5):
        micro_tx = create_test_transaction(
            f"MICRO00{i+1}",
            test_address,
            f"0x555555555555555555555555555555555555555{i}",
            0.005,  # 0.005 ETH - should be classified as micro (<0.01)
            timestamp=micro_base_time + i * 30  # 30 seconds apart
        )
        producer.send('ethereum-transactions', value=micro_tx, key=f'test-micro-{i+1}')
        producer.flush()
        print(f"   ✅ Sent micro tx {i+1}: {micro_tx['hash'][:12]} (0.005 ETH)")
        time.sleep(1)
    
    time.sleep(2)
    
    # Test 5: Velocity Pattern (7+ transactions in 15 minutes)
    print("\n⚡ Test 5: VELOCITY Pattern")
    velocity_base_time = current_time + 120
    
    for i in range(8):  # Send 8 transactions to exceed the 7 threshold
        velocity_tx = create_test_transaction(
            f"VELOC00{i+1}",
            test_address,
            f"0x444444444444444444444444444444444444444{i}",
            0.02,  # Small but consistent amount
            timestamp=velocity_base_time + i * 60  # 1 minute apart (8 tx in 8 minutes)
        )
        producer.send('ethereum-transactions', value=velocity_tx, key=f'test-velocity-{i+1}')
        producer.flush()
        print(f"   ✅ Sent velocity tx {i+1}: {velocity_tx['hash'][:12]} (0.02 ETH)")
        time.sleep(0.5)
    
    print("\n🎯 ALL FOCUSED TESTS COMPLETED!")
    print("💡 Monitor 'filtered-transactions' topic for AML alerts")
    print("🕐 Wait 30-60 seconds for pattern processing")
    print(f"🔍 Look for alerts from address: {test_address}")
    
    producer.close()

if __name__ == "__main__":
    main() 