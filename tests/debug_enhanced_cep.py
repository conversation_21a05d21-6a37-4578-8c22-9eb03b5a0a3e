#!/usr/bin/env python3

import json
import time
from kafka import KafkaProducer

def create_high_value_test():
    """Send a single 10 ETH transaction that should definitely trigger high value pattern"""
    
    producer = KafkaProducer(
        bootstrap_servers=['localhost:9092'],
        value_serializer=lambda v: json.dumps(v).encode('utf-8'),
        key_serializer=str.encode
    )
    
    # Create a very clear high-value transaction
    current_time = int(time.time())
    high_value_tx = {
        "hash": "0xHIGHVALUETEST000000000000000000000000000000000000000000000000",
        "from_address": "******************************************",
        "to_address": "******************************************",
        "value": 10000000000000000000,  # 10 ETH in Wei (10 * 10^18)
        "block_number": 8624900,
        "timestamp": current_time,
        "gas_used": 21000,
        "gas_price": 3000000000,
        "input_data": "0x"
    }
    
    print("🎯 DEBUG: Sending HIGH VALUE transaction")
    print(f"   Hash: {high_value_tx['hash'][:20]}...")
    print(f"   Value: 10 ETH (10,000,000,000,000,000,000 Wei)")
    print(f"   From: {high_value_tx['from_address']}")
    print(f"   Timestamp: {current_time}")
    
    producer.send('ethereum-transactions', value=high_value_tx, key='debug-high-value')
    producer.flush()
    producer.close()
    
    print("✅ High value transaction sent!")
    print("🔍 This should trigger HIGH_VALUE pattern in Enhanced CEP")
    print("📊 Monitor TaskManager logs for pattern debug output")

if __name__ == "__main__":
    create_high_value_test() 