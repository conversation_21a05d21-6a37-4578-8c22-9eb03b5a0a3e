#!/usr/bin/env python3
"""
Comprehensive test script for AML Data Ingestion Layer
Tests real-time Sepolia testnet data ingestion through Infura API
"""

import asyncio
import json
import time
import os
import sys
from typing import Dict, Any, List
from dataclasses import dataclass
from datetime import datetime, timedelta

# Add ingestion module to path
sys.path.append('./ingestion')

try:
    from kafka import KafkaConsumer, KafkaProducer
    from kafka.admin import KafkaAdminClient
    import requests
    from web3 import Web3
except ImportError as e:
    print(f"Missing dependencies: {e}")
    print("Please install with: pip install kafka-python web3 requests")
    sys.exit(1)


@dataclass
class TestResults:
    """Test execution results"""
    test_name: str
    success: bool
    duration: float
    message: str
    details: Dict[str, Any] = None


class IngestionTester:
    """Comprehensive tester for the ingestion layer"""
    
    def __init__(self):
        self.results: List[TestResults] = []
        self.kafka_bootstrap_servers = "localhost:9092"
        self.kafka_ui_url = "http://localhost:8080"
        self.infura_project_id = None
        self.load_config()
    
    def load_config(self):
        """Load configuration from environment or .env file"""
        # Try to load from .env file
        env_files = ['.env', 'test_sepolia_config.env']
        
        for env_file in env_files:
            if os.path.exists(env_file):
                with open(env_file, 'r') as f:
                    for line in f:
                        if '=' in line and not line.strip().startswith('#'):
                            key, value = line.strip().split('=', 1)
                            if not os.getenv(key):
                                os.environ[key] = value
                break
        
        self.infura_project_id = os.getenv('INFURA_PROJECT_ID')
    
    def print_header(self, title: str):
        """Print a formatted test section header"""
        print("\n" + "="*60)
        print(f"  {title}")
        print("="*60)
    
    def print_result(self, result: TestResults):
        """Print test result with color coding"""
        status = "✅ PASS" if result.success else "❌ FAIL"
        print(f"{status} {result.test_name} ({result.duration:.2f}s)")
        if not result.success or result.details:
            print(f"     {result.message}")
            if result.details:
                for key, value in result.details.items():
                    print(f"     {key}: {value}")
    
    async def run_test(self, test_name: str, test_func, *args, **kwargs) -> TestResults:
        """Run a single test with timing and error handling"""
        start_time = time.time()
        try:
            result = await test_func(*args, **kwargs) if asyncio.iscoroutinefunction(test_func) else test_func(*args, **kwargs)
            if isinstance(result, tuple):
                success, message, details = result
            else:
                success, message, details = result, "Test completed", {}
            
            return TestResults(
                test_name=test_name,
                success=success,
                duration=time.time() - start_time,
                message=message,
                details=details
            )
        except Exception as e:
            return TestResults(
                test_name=test_name,
                success=False,
                duration=time.time() - start_time,
                message=f"Test failed with exception: {str(e)}",
                details={"exception_type": type(e).__name__}
            )
    
    def test_docker_services(self) -> tuple:
        """Test Docker services are running"""
        try:
            import subprocess
            
            # Check if docker-compose services are running
            result = subprocess.run(
                ['docker-compose', '-f', 'docker-compose-ingestion.yml', 'ps'],
                capture_output=True, text=True, timeout=10
            )
            
            if result.returncode != 0:
                return False, "Docker compose not running or accessible", {}
            
            # Parse output to check service status
            lines = result.stdout.strip().split('\n')
            services = {}
            
            for line in lines[2:]:  # Skip header lines
                if line.strip():
                    parts = line.split()
                    if len(parts) >= 2:
                        service_name = parts[0]
                        status = "Up" in line
                        services[service_name] = status
            
            all_up = all(services.values()) if services else False
            
            return all_up, f"Services status checked", {"services": services}
            
        except subprocess.TimeoutExpired:
            return False, "Docker command timed out", {}
        except Exception as e:
            return False, f"Error checking Docker services: {str(e)}", {}
    
    def test_kafka_connectivity(self) -> tuple:
        """Test Kafka broker connectivity"""
        try:
            admin_client = KafkaAdminClient(
                bootstrap_servers=[self.kafka_bootstrap_servers],
                request_timeout_ms=5000
            )
            
            # List topics to verify connectivity
            topics = admin_client.list_topics(timeout_ms=5000)
            admin_client.close()
            
            return True, f"Kafka connectivity successful", {"topics": list(topics)}
            
        except Exception as e:
            return False, f"Kafka connectivity failed: {str(e)}", {}
    
    def test_kafka_topics(self) -> tuple:
        """Test if required Kafka topics exist"""
        try:
            admin_client = KafkaAdminClient(
                bootstrap_servers=[self.kafka_bootstrap_servers],
                request_timeout_ms=5000
            )
            
            topics = admin_client.list_topics(timeout_ms=5000)
            required_topics = ['ethereum-transactions', 'ethereum-blocks']
            
            existing_topics = []
            missing_topics = []
            
            for topic in required_topics:
                if topic in topics:
                    existing_topics.append(topic)
                else:
                    missing_topics.append(topic)
            
            admin_client.close()
            
            success = len(missing_topics) == 0
            message = f"Topics check: {len(existing_topics)}/{len(required_topics)} exist"
            
            return success, message, {
                "existing_topics": existing_topics,
                "missing_topics": missing_topics
            }
            
        except Exception as e:
            return False, f"Topic check failed: {str(e)}", {}
    
    def test_kafka_ui_access(self) -> tuple:
        """Test Kafka UI accessibility"""
        try:
            response = requests.get(self.kafka_ui_url, timeout=5)
            
            if response.status_code == 200:
                return True, "Kafka UI is accessible", {"status_code": response.status_code}
            else:
                return False, f"Kafka UI returned status {response.status_code}", {}
                
        except requests.RequestException as e:
            return False, f"Kafka UI not accessible: {str(e)}", {}
    
    def test_infura_config(self) -> tuple:
        """Test Infura API configuration"""
        if not self.infura_project_id or self.infura_project_id == "your_infura_project_id_here":
            return False, "Infura project ID not configured", {
                "config_file": "Please set INFURA_PROJECT_ID in .env file"
            }
        
        # Test Infura connectivity with HTTP first
        try:
            http_url = f"https://sepolia.infura.io/v3/{self.infura_project_id}"
            w3 = Web3(Web3.HTTPProvider(http_url))
            
            # Simple connectivity test
            chain_id = w3.eth.chain_id
            latest_block = w3.eth.block_number
            
            return True, "Infura API connectivity successful", {
                "chain_id": chain_id,
                "latest_block": latest_block,
                "network": "Sepolia" if chain_id == 11155111 else f"Unknown ({chain_id})"
            }
            
        except Exception as e:
            return False, f"Infura API test failed: {str(e)}", {}
    
    async def test_realtime_transaction_stream(self, duration: int = 30) -> tuple:
        """Test real-time transaction streaming for specified duration"""
        if not self.infura_project_id or self.infura_project_id == "your_infura_project_id_here":
            return False, "Cannot test streaming: Infura not configured", {}
        
        try:
            # Set up Kafka consumer to monitor incoming data
            consumer = KafkaConsumer(
                'ethereum-transactions',
                bootstrap_servers=[self.kafka_bootstrap_servers],
                auto_offset_reset='latest',
                enable_auto_commit=True,
                group_id='test-consumer',
                value_deserializer=lambda x: json.loads(x.decode('utf-8')) if x else None,
                consumer_timeout_ms=1000
            )
            
            print(f"     Monitoring transaction stream for {duration} seconds...")
            
            transactions_received = 0
            start_time = time.time()
            sample_transactions = []
            
            while time.time() - start_time < duration:
                messages = consumer.poll(timeout_ms=1000)
                
                for topic_partition, records in messages.items():
                    for record in records:
                        transactions_received += 1
                        
                        # Store sample transaction for analysis
                        if len(sample_transactions) < 3:
                            sample_transactions.append({
                                "hash": record.value.get("hash", ""),
                                "from": record.value.get("from_address", ""),
                                "to": record.value.get("to_address", ""),
                                "value_ether": record.value.get("value_ether", 0),
                                "timestamp": record.value.get("timestamp", 0)
                            })
                        
                        if transactions_received % 10 == 0:
                            print(f"     Received {transactions_received} transactions...")
            
            consumer.close()
            
            success = transactions_received > 0
            message = f"Received {transactions_received} transactions in {duration}s"
            
            return success, message, {
                "transactions_count": transactions_received,
                "rate_per_second": transactions_received / duration,
                "sample_transactions": sample_transactions
            }
            
        except Exception as e:
            return False, f"Stream test failed: {str(e)}", {}
    
    def test_ingestion_service_health(self) -> tuple:
        """Test ingestion service health"""
        try:
            import subprocess
            
            # Check if ingestion service container is running
            result = subprocess.run(
                ['docker-compose', '-f', 'docker-compose-ingestion.yml', 'ps', 'ingestion-service'],
                capture_output=True, text=True, timeout=10
            )
            
            if result.returncode != 0:
                return False, "Cannot check ingestion service status", {}
            
            # Check if service is in "Up" state
            is_running = "Up" in result.stdout
            
            if not is_running:
                return False, "Ingestion service is not running", {"docker_status": result.stdout.strip()}
            
            # Try to get service logs for health indicators
            log_result = subprocess.run(
                ['docker-compose', '-f', 'docker-compose-ingestion.yml', 'logs', '--tail=10', 'ingestion-service'],
                capture_output=True, text=True, timeout=5
            )
            
            recent_logs = log_result.stdout.strip()
            
            return True, "Ingestion service is running", {
                "status": "Running",
                "recent_logs_sample": recent_logs[-200:] if recent_logs else "No logs available"
            }
            
        except Exception as e:
            return False, f"Health check failed: {str(e)}", {}
    
    async def run_all_tests(self, stream_duration: int = 30):
        """Run comprehensive test suite"""
        self.print_header("AML Data Ingestion Layer - Comprehensive Test Suite")
        
        print("Testing Sepolia testnet real-time data ingestion via Infura API")
        print(f"Stream monitoring duration: {stream_duration} seconds\n")
        
        # Infrastructure tests
        self.print_header("Infrastructure Tests")
        
        tests = [
            ("Docker Services", self.test_docker_services),
            ("Kafka Connectivity", self.test_kafka_connectivity),
            ("Kafka Topics", self.test_kafka_topics),
            ("Kafka UI Access", self.test_kafka_ui_access),
            ("Ingestion Service Health", self.test_ingestion_service_health),
        ]
        
        for test_name, test_func in tests:
            result = await self.run_test(test_name, test_func)
            self.results.append(result)
            self.print_result(result)
        
        # Configuration tests
        self.print_header("Configuration Tests")
        
        config_tests = [
            ("Infura API Configuration", self.test_infura_config),
        ]
        
        for test_name, test_func in config_tests:
            result = await self.run_test(test_name, test_func)
            self.results.append(result)
            self.print_result(result)
        
        # Real-time data tests
        self.print_header("Real-time Data Flow Tests")
        
        result = await self.run_test(
            "Real-time Transaction Stream", 
            self.test_realtime_transaction_stream, 
            stream_duration
        )
        self.results.append(result)
        self.print_result(result)
        
        # Summary
        self.print_header("Test Summary")
        
        passed = sum(1 for r in self.results if r.success)
        total = len(self.results)
        
        print(f"Tests passed: {passed}/{total}")
        print(f"Success rate: {(passed/total)*100:.1f}%")
        
        if passed == total:
            print("\n🎉 All tests passed! The ingestion layer is working correctly.")
        else:
            print(f"\n⚠️  {total-passed} test(s) failed. Please check the configuration and services.")
            
            failed_tests = [r.test_name for r in self.results if not r.success]
            print("Failed tests:", ", ".join(failed_tests))
        
        return passed == total


async def main():
    """Main test execution"""
    import argparse
    
    parser = argparse.ArgumentParser(description="Test AML Data Ingestion Layer")
    parser.add_argument("--duration", type=int, default=30, help="Stream monitoring duration in seconds")
    parser.add_argument("--quick", action="store_true", help="Run quick tests only (skip stream monitoring)")
    
    args = parser.parse_args()
    
    duration = 10 if args.quick else args.duration
    
    tester = IngestionTester()
    success = await tester.run_all_tests(stream_duration=duration)
    
    if success:
        print("\n🚀 Ready to proceed with CEP layer implementation!")
    else:
        print("\n🔧 Please fix the issues before proceeding.")
    
    return success


if __name__ == "__main__":
    try:
        success = asyncio.run(main())
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n\nTest interrupted by user.")
        sys.exit(1)
    except Exception as e:
        print(f"\nUnexpected error: {e}")
        sys.exit(1) 