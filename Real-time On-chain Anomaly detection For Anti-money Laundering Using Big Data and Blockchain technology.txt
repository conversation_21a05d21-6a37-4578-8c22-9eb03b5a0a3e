Title:
 Real-Time On-Chain Anomaly Detection for Anti-Money Laundering
1.	Introduction:
The integration of big data analytics and blockchain or distributed-ledger technology helps anti-money laundering (AML) to sit at the nexus of financial regulation in blockchain ecosystem. Recent research demonstrates that hybrid big-data and machine learning approaches can achieve high detection accuracy in real-time. For example, behavioral-biometric and machine learning pipelines in mobile-payment systems have reached up to 96% accuracy with sub-second alerts (<PERSON> et al., 2024). Deep learning frameworks for cross-border cryptocurrency anomaly detection report over 97% accuracy on public blockchain datasets which highlights the need for structural and ledger-wide analytics (<PERSON> et al., 2024). Conceptual models integrating immutable blockchain ledgers with machine learning driven smart contract responses underline the promise of real time and end-to-end fraud prevention but remain largely unimplemented in live streaming environments (<PERSON><PERSON> et al., 2024). Despite these advances, existing anti-money laundering solutions are soiled because they cannot support real-time and scalable graph construction for high velocity blockchain streams; rely on rigid hand-crafted CEP rules that struggle to adapt to evolving laundering schemes; confine powerful spatio-temporal GNNs to batch processing, delaying important alert; and lack integrated, streaming -capable explainability to foster investigators’ trust.
1.1	Problem statement:
i.	High velocity blockchain data streams produce massive transaction volumes (Rosa-Bil<PERSON> et al., 2025) (<PERSON><PERSON> et al., 2024), but no existing framework supports real-time graph construction at scale for downstream analysis;
ii.	Complex Event Processing (CEP) systems rely on hand-crafted rules that lack adaptability to emerging laundering topologies (Rosa-Bilbao et al., 2025) which leads to false negatives and escalating maintenance burdens;
iii.	Spatio-Temporal Graph Neural Networks (GNNs) have demonstrated superior accuracy in offline experiments but are constrained by batch-processing inference (Ning et al., 2024) and this prevent timely alert on dynamic transaction networks; and
iv.	The absence of integrated and streaming capable explainability modules inhibits investigators’ ability to audit and trust automated alerts (Kute et al., 2024) which prevent compliance adoption.
By addressing these four problems are important to deliver a scalable, adaptive and transparent anti-money laundering pipeline capable of defending against evolving threats in blockchain finance. Also, this motivates the proposed real-time on-chain anomaly detection for anti-money laundering which aims to unite low-latency rule-based filtering with learned graph representations, offer context-aware and rapid detection of money-laundering chains on dynamic transaction networks.
2.	Aim and Objectives:
2.1	Aim:
Current blockchain-based Anti-Money Laundering frameworks lack integrated, low-latency graph and temporal analysis capabilities to detect evolving laundering schemes. Therefore, this study aims to develop a real-time on-chain anomaly detection streaming pipeline for blockchain transaction monitoring. It will employ Kafka-based event ingestion, Complex Event Processing pre-filtering, sliding-window graph construction in Spark and GNN inference with explainability.
2.2	Objectives:
i.	Deploy a Kafka-Flink-SQL module to process Sepolia events and prune more than 85% of benign transactions;
ii.	Implement a Spark Structured Streaming to build 5-minute window graphs (sliding every one minute) with a latency of less than 500ms per snapshot;
iii.	Train TGAT model on Elliptic dataset and deploy under Docker/GPU to achieve end-to-end inference which less than 1 second;
iv.	Integrate GNNExplainer to identify node/edge importances for larger than 90% of alert interpretability in user test; and
v.	Benchmark the entire pipeline on Sepolia to evaluate precision, recall, F1, throughput (tx/s), latency and CPU/GPU usage.
3.	Background Study:
The evolution of blockchain technology shows significant opportunities and challenges in the context of Anti-Money Laundering (AML). Anti-money laundering has gradually become an essential pioneer in financial crime prevention on public blockchains. Unlike traditional banking system, blockchain transactions are pseudonymous but permanently recorded due to its foundation of distributed-ledger technology. It allows sophisticated actors to layer, integrate and disguise illicit transaction flows at unprecedented scale and speed. Therefore, the detection of money laundering schemes in real-time is also important as even little delays can allow laundering rings to complete multi-step protocols, escape penalties or shift profits across exchanges. So, these are two complementary paradigms have emerged in the literature which are complex event processing (CEP) for low-latency and rule-based anomaly filtering while the another is graph neural network (GNNs) for structural pattern recognition on transaction graphs. Complex event processing good at matching hand-crafted “event patterns” such as rapid multi-hop transfer but it struggles with evolving and multi-dimensional laundering tactics. In contrast, GNNs learn rich relational embeddings but are normally applied offline or in batch mode which limit their utility for real-time monitoring. Therefore, this literature review compares the proposed Real-Time Spatio-Temporal GNN with Complex Event Processing Pipeline to existing studies, focusing on big data scale, temporal dynamics, graph structure and explainability.
3.1	Literature Review:
3.1.1	Static Machine-Learning and Ensemble Methods
In the early stage of anti-money laundering on-chain research, it focused on tabular features and ensemble classifiers. Apiecionek and Karbowski (2024) introduced a Fuzzy Neural Network with Ordered Fuzzy Numbers to handle uncertainty in synthetic Ethereum-like anomalies which can achieve up to 97.7% accuracy and faster inference than autoencoders. However, the network depended solely on artificial data and lacked real-world validation. Similarly, a Hierarchical Ensemble Learning Model (HELM) combined ten classifiers (Extra Trees, XGBoost, LightGBM, etc) in a three-layer soft-voting scheme with a 98.85% detection accuracy and robustness to adversarial poisoning on Ethereum transaction records (Kamran et al., 2024). While these methods illustrate that ensemble diversity can achieve high accuracy, they disregard the graph structure of illicit flows and operate in batch, offline contexts.
3.1.2	Graph-Based and Spatio-Temporal Deep Learning
Recent research has shift toward Graph Neural Network as it recognizes the importance of relational context. Xu et al. (2024) developed a heterogeneous GNN with attention, temporal convolutions and SHAP-based explainability to detect 35.2% of illicit transactions in the top 1% of ranking flags on a 50 M-transaction Bitcoin dataset. Adaptive Temporal GCN (AT-GCN) dynamically updates convolutional weights via an LSTM, supplement minority-class labels across snapshots and leverages similarity-weighted neighbour aggregation, resulting in up to +15% F1 gain over static GCN/GAT baselines on Elliptic Bitcoin data (Ning et al., 2024). MDGC-LSTM further integrates dynamic graph convolutions with LSTM-based temporal modeling which achieve a +0.25 Macra-F1 improvement as compared to prior models and demonstrating transferability between cryptocurrency (Elliptic) and non-AML (OGB-Arxiv) benchmarks (Wan & Li, 2024). These spatio-temporal GNNs showcase the power of evolving embeddings for capturing laundering chains but have been evaluated in offline and batch experiments.
3.1.3	Real-Time and Complex Event Processing-Augmented Architectures
A real-time detection of suspicious blockchain activities and transition from batch-processing to continuous streaming is essential for an effective low-latency anti-money laundering. Therefore, Rosa-Bilbao et al., (2024) implement a containerized two-layer Complex Event Processing (CEP) system for Ethereum network and Polygon in sub-millisecond windows by employing SiddhiQl rules to detect protocol abnormalities such as repeated block numbers and abnormal gas usage. They demonstrate sub-millisecond pattern matching but rely solely on preconfigured rules without adaptive learning or explainability features. This approach treats each log event separately and cannot learn novel schemes while it delivers real-time. After that, X. Li et al. (2025) implemented a Temporal-Directed Louvain community detection on Spark GraphX to identify laundering gangs, but they relied on batch windows and manual threshold tuning. Neither of them integrates learned graph representations into CEP layer to enrich detection or reduce false positives from hard thresholds.
3.1.4	Explainability and Human-in-the-Loop Refinement
Explainability is essential for compliance adoption. For example, Kute et al. (2024) fused a Conv 1D CNN with SHAP explanations to trace feature contributions for each flagged transaction to improve investigator trust. Also, Alarab and Prakoonwit (2022) used active-learning loops with TAGCN and Monte Carlo dropout/adversarial uncertainty sampling to lower labeling effort by a nearly 80% while maintaining high F1 score on Elliptic Bitcoin data (Rosa Bilbao et al., 2025). However, explainable approaches remained post-hoc and detached from live pipelines. At the same time, active learning also has not yet to be integrated into streaming frameworks.
3.1.5	Privacy-Preserving and Federated Learning Approaches
Cross-institution collaboration is important to detect multi-chain laundering rings, but privacy considerations prevent raw data sharing. Therefore, conceptual frameworks integrate federated learning and blockchain for safe model updates, but no empirical on-chain, cross-chain federated GNN has been demonstrated (Kute et al., 2024; Bello et al.,2024).
1.1	Identifications of Research Gaps:
i.	In contrast, Rosa-Bilbao et al., (2025) built a CEP engine but did not construct transaction graphs in real-time while Ning et al., (2024) delivered powerful spatio-temporal modeling but only in batch snapshots. Therefore, there are no end-to-end system combines Complex Event Processing’s sub-second filtering with spatio-temporal GNN inference on sliding-window graphs. 
ii.	Existing work either offers rule transparency (CEP) or post-hoc attention/SHAP but a unifies and streaming-capable explainability layer is absent such as Kute et al., (2024) and Xu et al., (2024) both attach SHAP or attention to offline GNN/CNN models. Therefore, there is no one emits real-time and per-alert explanations alongside anomaly scores.
iii.	Some research shows that active-learning frameworks reduce labeling but are untested in continuous processing pipelines. This can be observed from Alarab and Prakoonwit (2022) that they showed active-learning cut labels by 80% on static graphs, but no one embeds that loop into a live stream.
iv.	Most of the research papers are focused on single-batch processing transaction data. For example, Bello et al., (2024) just proposed federated blockchain machine learning conceptually but no offer empirical multi-chain GNN implementation. Therefore, empirical demonstrations of federated and multi-chain GNN training for privacy-preserving anti-money laundering are missing.
v.	Techniques such as diffusion-based synthetic subgraph generation or causal GNNs have not been leveraged to augment scarce illicit labels or distinguish causal money flows from spurious correlations. These cutting-edge techniques are entirely absent from current AML on-chain research.








2.	Research Methodology:
2.1	Introduction
In research methodology, four of the five research gaps are addressed through the development and deployment of streaming technologies with temporal graph neural networks. First, the implemented Kafka-Flink-Spark streaming architecture addresses the high velocity data streaming processing that it can process Sepolia transactions in real-time. Second, integrated GNNExplainer represents a functional integration rather than a fully validated human-centered explainable AI solution that it can provide node and edge attribution analysis. Third, the Flink-SQL based CEP implementation addresses the limited CEP adaptability that it processes streaming events with configurable rule sets to filter benign transaction. Fourth, a real-time TGAT deployment with GPU acceleration addresses the constrained GNN implementation as it can eliminate batch processing constraint. Finally, this research methodology provides end-to-end streaming implementation that integrates CEP filtering with real-time spatio-temporal GNN inference on Docker-containarised infrastructure with performance monitoring.
2.2	System Architecture Prototype Flowchart 
 
2.3	Data Collection
In the data collection, there are two complementary sources to support the project’s model training and live, end-to-end evaluation.
2.3.1	Labelled Elliptic Bitcoin Dataset
This Elliptic Bitcoin Dataset is a publicly available source from Kaggle, and it contains 3 files in CSV format which titled “elliptic_txs_classes.csv”, “elliptic_txs_edgelist.csv” and “elliptic_txs_features.csv”. These datasets are time-series of Bitcoin transactions. They will structure as directed graph where the Nodes represent transaction and Edges represent the flows of bitcoins between transactions.
•	Number of records: 203769 transactions
•	Number of features: 166 features
•	Data type: 
o	Numerical features: transaction amounts, timestamps
o	Categorical features: transaction types
o	Graph structural data: transaction connections
•	Dataset components:
o	Elliptic_txs_features.csv: It contains transaction features
o	Elliptic_txs_classes.csv: It contains transaction labels
o	Elliptic_txs_edgelist.csv: It contains transaction connections
2.3.2	Live Ethereum Data
Data is collected from the Ethereum blockchain through the Infura API
•	Number of records: Continuous streaming data
•	Number of features: more than 20 features in a transaction
•	Data type: 
o	Numerical features: block numbers, gas prices, transaction values
o	Categorical features: transaction types, contract interactions
o	Time-series data: block timestamps, transaction sequences
o	Graph structural data: transaction flows
2.4	Data Preprocessing
The Elliptic dataset is preprocessed and anonymized with sensitive information are removed while maintaining the structural integrity of the transaction network.
2.4.1	Elliptic dataset
•	Missing Value Treatment: There are no missing values in the three csv files
•	Encoding categorical variables: Label encoding is performed such as 
o	“unknown” is encoded into 0
o	“illicit” is encoded into 1
o	“licit” is encoded into 2
•	Feature Scaling: 
o	StandardScalar is applied to normalize node features
o	Min-max scaling is applied to normalize the degree features 
o	Temporal features are standardized
•	Feature Engineering:
o	Graph based features:
	In-degree and out-degree calculations
	Degree ratio computation
	Total degree calculation
o	Temporal features:
	Mean incoming time
	Standard deviation of incoming times
	Mean outgoing time
	Standard deviation of outgoing times
	Current time step
	Normalized time
2.4.2	Live Ethereum Data
Nodes, edges and timestamps are extracted from incoming data. Then, node features are created from transaction data. Also, the edge indices and timestamps are constructed.
•	Missing Value Treatment: There are no missing values in the three csv files
•	Data Validation:
o	Verify the rransaction hash integrity 
o	Validate the block number sequences
o	Check the timestamp consistency
•	Feature Engineering:
o	Raw features are converted to PyTorch tensors
o	Timestamps are normalized
o	Edge attributed are created from temporal information

2.5	System Architecture Prototype Development Methodology
This development methodology follows a modular, containarised approach using Docker Compose orchestration to ensure reproducibility and scalability. The prototype development is structured into five interconnected layers which are Data Ingestion, Complex Event Processing, Graph Construction, Machine Learning Inference and Visualisation or Monitoring. Each component is developed as an independent microservice to enable parallel development and individual testing while maintaining system-wide integration capabilities.
2.5.1	Data Ingestion Layer Development
This layer begins with establishing Kafka broker infrastructure supported by zookeeper for cluster coordination. Then, the infura API integration module is built with Python’s asyncio libraries to handle high throughput Ethereum transaction streams. It has built-in retry mechanisms and connection pooling to make sure that data is captured reliably. 
2.5.2	Complex Event Processing Layer 
Apache Flink SQL is being utilized for real-time pattern detection where the specific money laundering patterns such as rapid multi-hop transfer, unusual transaction amounts and suspicious timing patterns will be targeted by the CEP rules which also implemented as SQL queries. The CEP rules are constructing filtering logic to prune more than 85% of benign transaction as it can help to maintain high sensitivity for potentially illicit activities. After that, customizable Flink operators are developed to handle blockchain specific data types and temporal windowing requirements. 
2.5.3	Graph Construction Layer 
Spark Structured Streaming with custom graph assembly algorithms is employed to maintain 5-minute sliding windows with 1-minute intervals. This layer is to develop efficient in-memory graph structures by using PyTorch Geometric data formats, implement temporal edge weighting schemes and create dynamic node feature extraction pipelines to capture both transaction-level and network-level attributes in real-time.
2.5.4	Machine Learning Model Development
In this development layer can be split into three sub-layers which are TGAT model implementation, model training pipeline and GNNExplainer integration.
*******	TGAT Model Implementation
TGAT model implementation starts with baseline model training on the elliptic Bitcoin dataset and it entails constructing bespoke attention mechanism for temporal graph learning, efficient batch processing routine for training and model checkpointing systems for iterative improvement. The model architecture is designed to optimize for GPU deployment CUDA acceleration and it includes memory management algorithms for handling huge graph structures in limited GPU memory.
*******	Model Training Pipeline
Stratified sampling techniques are used to overcome class imbalance in the Elliptic dataset where illicit transactions make up a small percentage. Also, this methodology helps to handle extreme class imbalance by implementing focal loss function, develop data augmentation tactics via temporal graph alteration and develop robust validation frameworks using time-aware cross-validation to prevent data leakage.
*******	GNNExplainer Integration
GNNExplainer generates node and edge importance scores for each prediction as a real-time explanation service. The implementation also develops efficient gradient-based attribution methods, design visualization interfaces for explanation delivery and defining explanation quality metrics to achieve more than 90% interpretability standards in user evaluations.
2.6	Evaluation Methodology 
Evaluation Methodology has an performance evaluation frameworks which are accuracy assessment, latency throughput evaluation and scalability assessment.
2.6.1	Accuracy Assessment 
This methodology employs various metrics designed for highly imbalance datasets. The primary evaluation focuses on Precision, Recall and F1-Score to emphasis on minority class performance as false negative in money laundering detection can lead to severe consequence. Then, the area under the ROC curve and precision-recall AUC are computed to assess model discrimination capabilities across different decision thresholds. After that, temporal validation is carried out through time-split cross-validation to guarantee the model can generalize well across various time periods, simulate real-world deployment scenarios where future patterns may differ from historical training data.
2.6.2	Latency and Throughput Evaluation
End-to-end system performance is measured from transaction ingestion to alert generation. Component-level latency profiling to identify bottlenecks, measuring graph construction time, model inference duration and explanation generation overhead are included in this methodology. Throughput testing evaluates system capacity under varying load conditions. The goal is to have more than 1000 transactions per second while keeping the graph construction time under 500 milliseconds and the total inference time under 1 second.
2.6.3	Scalability assessment
Synthetic transaction generation is used to perform the stress testing on the entire pipeline under high-volume conditions as it simulate real blockchain traffic patterns. The evaluation includes testing the system’s resource utilization of CPU. GPU and RAM under different heavy conditions. Also, it analyses system stability prolonged operation and evaluate horizontal scaling capabilities through distributed deployment testing.
2.7	Results Analysis Methodology
It includes false positive and false negative analysis as they are focusing on incorrectly flagged transaction and missed money laundering cases.
2.7.1	False Positive Analysis
To identify systematic biases and improvement opportunities, this analysis will examine incorrectly flagged transactions. It includes clustering false positive cases by transaction characteristics, temporal patterns and network properties to identify common failure modes.
2.7.2	False Negative Analysis
This analysis traces individual laundering chains to identify detection gaps through case study methodology. It examines graph topology of missed cases, temporal pattern patterns that go undetected. Also, it includes feature space analysis to uncover underrepresented patterns in training data.
3.	Potential Project Significance:
Based on the above problem statements and research gaps, they leave financial institutions and regulators to multi-step laundering schemes that evade rule engines and delayed graph analyses. By utilizing CEP’s low-latency filtering with a streaming spatio-temporal GNN, the proposed pipeline will 
i.	Reduce alert latency to less than one second which enable for immediate investigation and transaction blocking;
ii.	Improve detection accuracy for complex laundering chains by learning relational embeddings on sliding-window graphs; and
iii.	Provide integrated explainability to trace each alert back to specific transactions and network structures.
Therefore, bank, cryptocurrency exchange and regulatory bodies will gain a powerful tool that mixes reflexive speed with deep pattern recognition to minimize financial loss and reputational risk. In a long run, this integrated pipeline will also bring broader impacts:
i.	Financial institutions will discover and disrupt money laundering rings faster, reducing illicit capital flows and enhancing system integrity;
ii.	It helps to streamline audits and support compliance with evolving AML directives such as FATF’s travel rule; and
iii.	It bolsters financial stability, protect legitimate firms and reduce funding for illicit enterprises.
4.	Expected Outcomes
This anti-money laundering pipeline with first prune at least 85% of benign transaction at the CEP pre filtering stage. It is expected to lower the downstream load and form an efficient transaction graph. Second, 5 minutes to 1 minute slide transaction graph will be constructed to demonstrate a scalable real-time graph assembly. Third, the temporal graph attention network model will be integrated into a dockerised GPU service after training on a public Elliptic Bitcoin dataset. Then, it should maintain at least 80% F1 score in benchmark tests while performing less than 1 second per snapshot inference.
	Furthermore, an interpretable node and edge level attributions which provided by GNNExplainer for more than 90% of alerts in user evaluations is important as it wished to enhance the trustiness of investigator and streamline compliance processes. After that, the system will ingest the live transaction data from Sepolia and maintain a throughput more than 1,00 tx/s while keeping a below 70% CPU or GPU usage.
In conclusion, this proposed system will be shown as a unique and transparent anti-money laundering solution in the finance sector of blockchain as it will shorten the gap between the low-latency complex event processing engines and batch-processed graph models. Also, this system will provide an outstanding blueprint to other high velocity graph domains for extending the streaming graph neural network inference.
